import 'dart:async';

import 'package:amplitude_flutter/amplitude.dart';
import 'package:amplitude_flutter/configuration.dart';
import 'package:amplitude_flutter/default_tracking.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:psm_app/data_sources/purchase_api.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/noficationServices.dart';
import 'package:psm_app/view/splash_screen.dart';
import 'package:psm_app/view/widgets/error_dialog.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'firebase_options.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/provider/locale_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

String userName = '';
String fontSize = '';
String device_type = '';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await PurchaseApi.initPlatformState();
  await PurchaseApi.purchaserInfo();
  // Common.premium = true;
  //MobileAds.instance.initialize();
  //await Firebase.initializeApp();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await PurchaseApi.setCustomerAttribute();
  FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);

  final Amplitude amplitude = Amplitude(Configuration(
      apiKey: Common.amplitudeKey,
      defaultTracking: const DefaultTrackingOptions(sessions: true)));

  await amplitude.isBuilt;

  SharedPreferences preferences = await SharedPreferences.getInstance();
  userName = preferences.getString('userName') ?? '';
  fontSize = preferences.getString('font') ?? '';
  Common.username = userName;
  if (fontSize == '') {
    CommonFont.size = '20';
  } else {
    CommonFont.size = fontSize;
  }
  /* await Request_key();
  var version = await ApiServices().checkVersion();
  print(version);
  Common.update = version; */
  String getDeviceType() {
    final data = MediaQueryData.fromWindow(WidgetsBinding.instance.window);
    return data.size.shortestSide < 610 ? 'phone' : 'tablet';
  }

  device_type = getDeviceType();
  NotificationService().initNotification();
  await SentryFlutter.init(
    (options) {
      options.dsn =
          'https://<EMAIL>/4506664712077312';
      options.tracesSampleRate = 1.0;
      options.sendDefaultPii = true;
      options.maxRequestBodySize = MaxRequestBodySize.always;
      options.maxResponseBodySize = MaxResponseBodySize.always;
      // options.attachScreenshot = true;
      // options.screenshotQuality = SentryScreenshotQuality.low;
    },
  );

  runApp(ChangeNotifierProvider<LocaleProvider>(
    create: (context) => LocaleProvider(),
    child: const MyApp(),
  ));
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    Common.appOpened = true;
    //getListQuiz();
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    if (device_type == 'phone') {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    }
    return Consumer<LocaleProvider>(builder: (context, appState, child) {
      return MaterialApp(
        home: const Splash(),
        title: 'ScrumPass PSM Exam Tool',
        locale: Provider.of<LocaleProvider>(context).locale,
        localizationsDelegates: const [
          AppLocalizationsDelegate(),
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: localizedLabels.keys.toList(),
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          // This is the theme of your application.
          //
          // Try running your application with "flutter run". You'll see the
          // application has a blue toolbar. Then, without quitting the app, try
          // changing the primarySwatch below to Colors.green and then invoke
          // "hot reload" (press "r" in the console where you ran "flutter run",
          // or simply save your changes to "hot reload" in a Flutter IDE).
          // Notice that the counter didn't reset back to zero; the application
          // is not restarted.
          primarySwatch: Colors.blue,
        ),
        // Hiển thị popup lỗi thay vì màn đỏ ở debug và xám ở release
        builder: (context, widget) {
          ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
            Sentry.captureException(errorDetails,
                stackTrace: errorDetails.stack);
            return ErrorDialog(
                error:
                    '${errorDetails.summary.toString()} | ${Common.version} | Stack trace: ${errorDetails.stack.toString()}');
          };
          return widget!;
        },
      );
    });
  }
}
