import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter/services.dart';
import 'package:html/parser.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/helper/helper.dart';
import 'package:psm_app/localization.dart';
import 'package:http/http.dart' as http;
import 'package:psm_app/models/question_api_model.dart';
import 'package:psm_app/models/result_model.dart';
import 'package:psm_app/view/exam/exam.dart';
import 'package:psm_app/view/home.dart';
import 'package:psm_app/view/home/<USER>/level_button_widget.dart';
import 'package:psm_app/view/home/<USER>/score_card/score_card_result_widget.dart';
import 'package:psm_app/view/widgets/result_app_bar_widget.dart';
import 'package:psm_app/view/widgets/success_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

Future<int> getCountQuiz(String quizname, String time) async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  // Fetch and decode data
  final String resultString = await prefs.getString('result') ?? "";
  final List<Result> musics = Result.decode(resultString);
  int count = 0;
  if (musics == "") {
    return count;
  } else {
    for (var i = 0; i < musics.length; i++) {
      if (musics[i].name == quizname) {
        count++;
      }
      if (musics[i].time == time) {
        break;
      }
    }
    return count;
  }
}

int test = 4;

class ExamResult extends StatefulWidget {
  final Map<int, dynamic> answers;
  final Map<int, dynamic> answersText;
  final Map bookmark;
  final List<Question> questions;
  final String quizId;
  final String time;
  final String quizName;
  double percent = 0;
  late Map correctList = {};
  var correctQuestion;
  final int passPercent;
  final String exam_quiz;
  final String quizType;
  final int quizDuration;
  final int timeDoQuiz; //seconds
  ExamResult(
      {Key? key,
      required this.answers,
      required this.questions,
      this.quizType = "1",
      required this.answersText,
      required this.quizId,
      required this.exam_quiz,
      required this.quizName,
      required this.time,
      required this.bookmark,
      required this.passPercent,
      required this.quizDuration,
      required this.timeDoQuiz})
      : super(key: key) {
    answers.forEach((index, value) {
      if (questions[index].type == "single") {
        correctQuestion = questions[index].correct;
        if (correctQuestion == value) {
          correctList[index] = true;
          correct++;
        } else {
          correctList[index] = false;
        }
      } else if (questions[index].type == "multi") {
        correctList[index] = true;
        questions[index].correctIndex!.forEach((element) {
          if (answers[index][element] == false) {
            correctList[index] = false;
            return;
          }
        });
        if (correctList[index] == true) correct++;
      }
    });
    percent = correct / questions.length;
  }

  int correct = 0;
  @override
  ExamRes createState() => ExamRes();
}

class ExamRes extends State<ExamResult> {
  @override
  void initState() {
    super.initState();
  }

  launchURL() async {
    const url = 'http://m.me/scrumpassvn';
    if (await canLaunch(url)) {
      launch(
        url,
        enableJavaScript: true,
      );
    } else if (await canLaunch(
        'mailto:<EMAIL>?subject=Reprot issue for app ${Common.appName}&body=')) {
      launch(
          'mailto:<EMAIL>?subject=Reprot issue for app ${Common.appName}&body=');
    } else {
      return (await showDialog(
            context: context,
            builder: (BuildContext context) => AlertDialog(
              title: Text(AppLocalizations.of(context).error),
              content: Text(AppLocalizations.of(context).errorDialog),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
          )) ??
          false;
    }
  }

  Future<bool> _onRetryPressed() async {
    return (await showDialog(
          context: context,
          builder: (BuildContext context) => Dialog(
            // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
            insetPadding: EdgeInsets.symmetric(horizontal: 16),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
                child: Column(
                  children: [
                    Container(
                      width: 196,
                      height: 139,
                      decoration: BoxDecoration(
                          image: DecorationImage(
                        image: AssetImage("assets/images/info_dialog.png"),
                        fit: BoxFit.fill,
                      )),
                    ),
                    Text(
                      AppLocalizations.of(context).confirmRetry,
                      style: AppStyles.dialogText,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          children: [
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(right: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop(false);
                                },
                                child: Text(
                                  AppLocalizations.of(context).no,
                                  style: AppStyles.secondaryButton,
                                  textAlign: TextAlign.center,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.greenPrimary,
                                  backgroundColor: Colors
                                      .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                                  shadowColor: Colors
                                      .white, //specify the button's elevation color
                                  elevation: 0, //buttons Material shadow
                                  // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                                  minimumSize: Size(20,
                                      44), //specify the button's first: width and second: height
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle
                                          .solid), //set border for the button
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            )),
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(left: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop(false);
                                  Navigator.pushReplacement(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => Exam(
                                                idQuiz: widget.quizId,
                                                exam_quiz: widget.exam_quiz,
                                                questionSelection:
                                                    widget.quizType,
                                                quizName: widget.quizName,
                                                passPercent: widget.passPercent,
                                                duration: widget.quizDuration,
                                              )));
                                },
                                child: Text(
                                  "OK",
                                  style: AppStyles.primaryButton,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor: Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            ))
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        )) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    int count = 0;
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarBrightness: Brightness.dark,
          statusBarIconBrightness: Brightness.light), // light status bar text
      child: FutureBuilder<int>(
          future: getCountQuiz(widget.quizName, widget.time), // async work
          builder: (BuildContext context, AsyncSnapshot<int> snapshot) {
            if (snapshot.hasError) {
              count = 0;
            } else {
              count = snapshot.data ?? 0;
            }
            return Scaffold(
              appBar: PreferredSize(
                preferredSize: Size.fromHeight(220),
                child: ResultAppBarWidget(
                    correct: widget.correct,
                    incorrect: widget.answers.length - widget.correct,
                    percent: widget.percent,
                    passPercent: widget.passPercent,
                    num: count,
                    time: widget.time,
                    timeDoQuiz: widget.timeDoQuiz,
                    context: context),
              ),
              body: Column(
                children: [
                  BoMe(
                    questions: widget.questions,
                    answers: widget.answers,
                    answersText: widget.answersText,
                    correctList: widget.correctList,
                    bookmark: widget.bookmark,
                    quizId: widget.quizId,
                  ),
                  /* Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(10.0),
                itemCount: widget.questions.length,
                itemBuilder: _buildItem,
              ),
            ), */
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      children: [
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(
                              top: 12, right: 6, bottom: 38),
                          child: ElevatedButton(
                            onPressed: () {
                              logPage("Main");
                              Navigator.pushReplacement(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => Home()));
                            },
                            child: Text(
                              AppLocalizations.of(context).home,
                              style: AppStyles.secondaryButton,
                              textAlign: TextAlign.center,
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.greenPrimary,
                              backgroundColor: Colors
                                  .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                              shadowColor: Colors
                                  .white, //specify the button's elevation color
                              elevation: 0, //buttons Material shadow
                              // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                              minimumSize: Size(20,
                                  48), //specify the button's first: width and second: height
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle
                                      .solid), //set border for the button
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          ),
                        )),
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(
                              top: 12, left: 6, bottom: 38),
                          child: ElevatedButton(
                            onPressed: () {
                              _onRetryPressed();
                            },
                            child: Text(
                              AppLocalizations.of(context).retry,
                              style: AppStyles.primaryButton,
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.white,
                              backgroundColor: AppColors.greenPrimary,
                              shadowColor: Color.fromARGB(92, 0, 166, 144),
                              elevation: 4,
                              minimumSize: Size(20, 48),
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle.solid),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          ),
                        ))
                      ],
                    ),
                  )
                ],
              ),
            );
          }),
    );
  }
}

class MyDialog extends StatefulWidget {
  const MyDialog({
    Key? key,
    required this.questions,
    required this.quizId,
    required this.num,
  }) : super(key: key);
  final Question questions;
  final String quizId;
  final int num;
  @override
  _MyDialogState createState() => _MyDialogState();
}

class _MyDialogState extends State<MyDialog> {
  int dropdownvalue = 1;
  late TextEditingController controltext;
  List<int> arr = [];
  @override
  void initState() {
    super.initState();
    controltext = TextEditingController();
  }

  @override
  void dispose() {
    super.dispose();
    controltext.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                AppLocalizations.of(context).question + ":",
                style: AppStyles.reportDialogTitle,
              ),
              const SizedBox(
                height: 4,
              ),
              Container(
                // constraints: BoxConstraints(maxHeight: 90),
                child: Column(
                  children: <Widget>[
                    Container(
                      width: double.infinity,
                      //height: 200.0,
                      color: Colors.white.withOpacity(0.7),
                      child: Text(
                        parse(widget.questions.question).documentElement!.text,
                        style: AppStyles.reportDialogText,
                      ),
                    )
                  ],
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Text(
                AppLocalizations.of(context).report + ":",
                style: AppStyles.reportDialogTitle,
              ),
              const SizedBox(
                height: 4,
              ),
              TextField(
                decoration: InputDecoration(
                  border: new OutlineInputBorder(
                      borderSide: new BorderSide(color: Color(0xFFACB7C5))),
                ),
                autofocus: false,
                maxLines: 3,
                // expands: true,
                keyboardType: TextInputType.text,
                controller: controltext,
                textAlignVertical: TextAlignVertical.top,
              ),
              const SizedBox(
                height: 24,
              ),
              Container(
                child: Row(
                  children: [
                    Expanded(
                        child: Padding(
                      padding: const EdgeInsets.only(right: 6),
                      child: ElevatedButton(
                        onPressed: close,
                        child: Text(
                          AppLocalizations.of(context).cancel,
                          style: AppStyles.secondaryButton,
                          textAlign: TextAlign.center,
                        ),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: AppColors.greenPrimary,
                          backgroundColor: Colors
                              .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                          shadowColor: Colors
                              .white, //specify the button's elevation color
                          elevation: 0, //buttons Material shadow
                          // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                          minimumSize: Size(20,
                              44), //specify the button's first: width and second: height
                          side: BorderSide(
                              color: AppColors.greenPrimary,
                              width: 1.0,
                              style: BorderStyle
                                  .solid), //set border for the button
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0)),
                        ),
                      ),
                    )),
                    Expanded(
                        child: Padding(
                      padding: const EdgeInsets.only(left: 6),
                      child: ElevatedButton(
                        onPressed: submit,
                        child: Text(
                          AppLocalizations.of(context).send,
                          style: AppStyles.primaryButton,
                        ),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: AppColors.white,
                          backgroundColor: AppColors.greenPrimary,
                          shadowColor: Color.fromARGB(92, 0, 166, 144),
                          elevation: 0,
                          minimumSize: Size(20, 44),
                          side: BorderSide(
                              color: AppColors.greenPrimary,
                              width: 1.0,
                              style: BorderStyle.solid),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0)),
                        ),
                      ),
                    ))
                  ],
                ),
              )
            ],
          ),
        ),
      ),
      // actions: [
      //   TextButton(
      //       onPressed: submit,
      //       child: Text(AppLocalizations.of(context).send)),
      //   TextButton(
      //       onPressed: close,
      //       child: Text(AppLocalizations.of(context).cancel))
      // ]
    );
  }

  Future<void> sendReport() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    String url = apiUrl + "send_report";
    String parsedstring = '';
    var doc = parse(widget.questions.question);
    if (doc.documentElement != null) {
      parsedstring = doc.documentElement!.text;
    }
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "-Quiz List Request";
      Map data = {
        'appid': Common.appid,
        'qid': widget.questions.id,
        'quid': widget.quizId,
        'email': '',
        'message': controltext.text,
        'questionReport': parsedstring,
      };
      try {
        final response = await dio.post(url,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data),
              "debugId": Common.debugId
            }));
        if (response.statusCode == 200) {
          inspect(response.data);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        // if (loadRemoteDatatSucceed == false) retryFuture(sendReport, 200);
      }
    }
  }

  void submit() {
    Navigator.of(context).pop(controltext);
    if (controltext.text.isEmpty) {
      error();
    } else {
      sendReport();
      success();
    }
    //Navigator.of(context).pop();
    //inspect(widget.quizId);
  }

  void close() {
    Navigator.of(context).pop();
  }

  Future success() => showDialog(
      context: context,
      builder: (context) => SuccessDialog(
          text: AppLocalizations.of(context)
              .reportSuccess /* actions: [TextButton(onPressed: close, child: const Text("OK"))] */
          ));
  Future error() => showDialog(
      context: context,
      builder: (context) => AlertDialog(
            content: Text(AppLocalizations.of(context)
                .noInfo), /* actions: [TextButton(onPressed: close, child: const Text("OK"))] */
          ));
  DropdownMenuItem<int> buildMenuItem(int e) =>
      DropdownMenuItem(value: e, child: Text(e.toString()));
}

class BoMe extends StatefulWidget {
  const BoMe(
      {Key? key,
      required this.questions,
      required this.answers,
      required this.answersText,
      required this.bookmark,
      required this.quizId,
      required this.correctList})
      : super(key: key);
  final Map<int, dynamic> answers;
  final Map<int, dynamic> answersText;
  final Map bookmark;
  final List<Question> questions;
  final String quizId;
  final Map correctList;
  @override
  _BoMeState createState() => _BoMeState();
}

class _BoMeState extends State<BoMe> {
  Future<List> getNumMarked() async {
    int Marked = 0;
    int Correct = 0;
    int Wrong = 0;
    int UnAnswered = 0;
    int AllAns = widget.questions.length;
    List<int> arr;
    for (var i = 0; i < widget.questions.length; i++) {
      if (widget.bookmark[i] == 1) {
        Marked++;
      }
      if (widget.correctList[i]) {
        Correct++;
      }
      if (widget.answersText[i] == '' ||
          widget.answersText[i].toString() == "[]") {
        UnAnswered++;
      }
    }
    Wrong = widget.questions.length - Correct;
    arr = [AllAns, Correct, Wrong, Marked, UnAnswered];
    return arr;
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
        child: Container(
            // ADDED "child:"
            alignment: Alignment.center,
            child: FutureBuilder<List<dynamic>>(
                future: getNumMarked(), // async work
                builder: (context, snapshot) {
                  if (!snapshot.hasData) {
                    return CircularProgressIndicator();
                  }
                  return Column(children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.fromLTRB(12, 16, 12, 8),
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: <Widget>[
                            GestureDetector(
                              onTap: () {
                                test = 4;
                                setState(() {});
                              },
                              child: LevelButtonWidget(
                                label: test == 4 ? "Selected" : "NotSelect",
                                type: AppLocalizations.of(context).allAns,
                              ),
                            ),
                            /* SizedBox(
                              width: 10,
                            ), */
                            GestureDetector(
                              onTap: () {
                                test = 2;
                                setState(() {});
                              },
                              child: snapshot.data![3] != 0
                                  ? LevelButtonWidget(
                                      label:
                                          test == 2 ? "Selected" : "NotSelect",
                                      type:
                                          AppLocalizations.of(context).marked +
                                              " (" +
                                              snapshot.data![3].toString() +
                                              ")",
                                    )
                                  : Container(
                                      height: 0,
                                      width: 0,
                                    ),
                            ),
                            /* SizedBox(
                              width: 10,
                            ), */
                            GestureDetector(
                              onTap: () {
                                test = 1;
                                setState(() {});
                              },
                              child: snapshot.data![1] != 0
                                  ? LevelButtonWidget(
                                      label:
                                          test == 1 ? "Selected" : "NotSelect",
                                      type: AppLocalizations.of(context)
                                              .rightAns +
                                          " (" +
                                          snapshot.data![1].toString() +
                                          ")",
                                    )
                                  : Container(
                                      height: 0,
                                      width: 0,
                                    ),
                            ),
                            GestureDetector(
                              onTap: () {
                                test = 0;
                                setState(() {});
                              },
                              child: snapshot.data![2] != 0
                                  ? LevelButtonWidget(
                                      label:
                                          test == 0 ? "Selected" : "NotSelect",
                                      type: AppLocalizations.of(context)
                                              .wrongAns +
                                          " (" +
                                          snapshot.data![2].toString() +
                                          ")",
                                    )
                                  : Container(),
                            ),
                            /* SizedBox(
                              width: 10,
                            ), */
                            GestureDetector(
                              onTap: () {
                                test = 3;
                                setState(() {});
                              },
                              child: snapshot.data![4] != 0
                                  ? LevelButtonWidget(
                                      label:
                                          test == 3 ? "Selected" : "NotSelect",
                                      type: AppLocalizations.of(context)
                                              .noAnsSelected +
                                          " (" +
                                          snapshot.data![4].toString() +
                                          ")",
                                    )
                                  : Container(
                                      height: 0,
                                      width: 0,
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(10.0),
                        itemCount: widget.questions.length,
                        itemBuilder: _buildItem,
                        shrinkWrap: true,
                      ),
                    ),
                  ]);
                })));
  }

  Widget _buildItem(BuildContext context, int index) {
    Question question = widget.questions[index];
    bool correct = widget.correctList[index] == true;
    var textAnswer = '';
    if (widget.questions[index].type == "single") {
      textAnswer = '\n' + widget.answersText[index];
    } else {
      widget.answersText[index].forEach((element) {
        textAnswer += '\n- ' + element;
      });
    }
    var textCorrect = '';
    if (widget.questions[index].type == "single") {
      textCorrect = question.options![question.correctIndex![0]].qOption;
    } else {
      question.correctIndex!.forEach((element) {
        if (element == 0) {
          textCorrect += '- ' + question.options![element].qOption;
        } else {
          textCorrect += '\n- ' + question.options![element].qOption;
        }
      });
    }
    bool bookmarked = widget.bookmark[index] == 1;
    bool unanswered = textAnswer.trim() == '';
    var checkType;
    if (test == 1)
      checkType = correct;
    else if (test == 0)
      checkType = !correct;
    else if (test == 2)
      checkType = bookmarked;
    else if (test == 4)
      checkType = true;
    else if (test == 3) checkType = unanswered;
    return checkType
        ? Card(
            borderOnForeground: false,
            shadowColor: Colors.white,
            margin: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            shape: /* bookmarked
                ? new RoundedRectangleBorder(
                    side: new BorderSide(color: Colors.yellow, width: 2.0),
                    borderRadius: BorderRadius.circular(4.0))
                : */
                correct
                    ? new RoundedRectangleBorder(
                        side: new BorderSide(
                            color: AppColors.greenText, width: 1.0),
                        borderRadius: BorderRadius.circular(4.0))
                    : new RoundedRectangleBorder(
                        side: new BorderSide(
                            color: AppColors.redAnswerText, width: 1.0),
                        borderRadius: BorderRadius.circular(4.0)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(top: 12, left: 12, right: 12),
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      bookmarked
                          ? Positioned(
                              left: 0,
                              top: -15,
                              child: SizedBox(
                                width: 16,
                                height: 16,
                                child: Image.asset(
                                    'assets/images/mark_question.png',
                                    fit: BoxFit.fill),
                              ),
                            )
                          : Container(),
                      Padding(
                        padding: const EdgeInsets.only(right: 30.0),
                        child: Text(
                          Helper().parseHtmlString((index + 1).toString() +
                              '. ' +
                              question.question),
                          style: AppStyles.questionText,
                          textAlign: TextAlign.left,
                        ),
                      ),
                      Positioned(
                        // will be positioned in the top right of the container
                        top: 0,
                        right: 0,
                        child: InkWell(
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () {
                            openDialog(widget.questions[index], widget.quizId,
                                index + 1);
                          },
                          child: SizedBox(
                            width: 25,
                            height: 25,
                            child: Image.asset('assets/images/report_icon.png',
                                fit: BoxFit.fill),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Divider(
                  color: AppColors.lightGreyBorder,
                  thickness: 1,
                  height: 24,
                ),
                // SizedBox(height: 5.0),
                Padding(
                  padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(AppLocalizations.of(context).answer,
                          style: AppStyles.questionText
                              .copyWith(fontWeight: FontWeight.w600)),
                      Text(
                        textAnswer.trim() == ''
                            ? AppLocalizations.of(context).noAnsSelected
                            : Helper().parseHtmlString(textAnswer),
                        style: correct
                            ? AppStyles.correctAnswerText
                            : AppStyles.incorrectAnswerText,
                      ),
                      SizedBox(height: 8.0),
                      correct
                          ? Container()
                          : Text.rich(
                              TextSpan(children: [
                                TextSpan(
                                    text: AppLocalizations.of(context).rightAns,
                                    style: AppStyles.questionText
                                        .copyWith(fontWeight: FontWeight.w600)),
                                TextSpan(text: "\n"),
                                TextSpan(
                                    text: Helper().parseHtmlString(textCorrect),
                                    style: AppStyles.correctAnswerText)
                              ]),
                            ),
                    ],
                  ),
                )
              ],
            ),
          )
        : Container();
  }

  Future openDialog(ques, quizid, num) => showDialog(
      context: context,
      builder: (context) {
        return MyDialog(questions: ques, quizId: quizid, num: num);
      });
}
