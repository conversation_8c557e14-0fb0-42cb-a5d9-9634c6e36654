import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:psm_app/view/flashcard/tutorial.dart';
import 'package:tap_debouncer/tap_debouncer.dart';
import '../../localization.dart';
import 'flashcard_favorite.dart';
import 'package:swipeable_card_stack/swipeable_card_stack.dart';
import '../../core/core.dart';
import '../../globals.dart';
import '../../helper/helper.dart';
import '../../models/flashcard_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flash_card/flash_card.dart' as flash_card;

import '../home.dart';

class FlashCard extends StatefulWidget {
  const FlashCard({Key? key}) : super(key: key);

  @override
  State<FlashCard> createState() => _FlashCardState();
}

class _FlashCardState extends State<FlashCard> {
  List<Flashcard> flashcard = [];
  List<Flashcard> flashcardFav = [];
  late Future<bool> isDataInit;
  int index = 0;
  int skipCount = 0;
  int doneCount = 0;
  bool showEnd = false;
  @override
  void initState() {
    logPage("Flashcard");
    isDataInit = Future.value(false);
    initData();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<dynamic> initData() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final json = preferences.getString('flashcard');
    final List list = jsonDecode(json ?? '[]');
    flashcard = list.map((e) => Flashcard.fromJson(e)).toList();
    isDataInit = Future.value(true);

    // Đếm card đã skip
    final skipJson = preferences.getString('flashcard_skip');
    final List listSkip = jsonDecode(skipJson ?? '[]');
    final flashcardSkip = listSkip.map((e) => Flashcard.fromJson(e)).toList();
    skipCount = flashcardSkip.length;

    // Đếm card đã done
    final doneJson = preferences.getString('flashcard_done');
    final List listDone = jsonDecode(doneJson ?? '[]');
    final flashcardDone = listDone.map((e) => Flashcard.fromJson(e)).toList();
    doneCount = flashcardDone.length;

    // Index
    final indexString = preferences.getString('flashcard_current_index');
    index = int.parse(indexString ?? "0");

    final endString = preferences.getString('flashcard_show_end');
    showEnd = endString == "1" ? true : false;
    setState(() {});

    final tutorial = preferences.getString('flashcard_tutorial');
    if (tutorial == "0" || tutorial == null) {
      await showGeneralDialog(
        context: context,
        barrierColor: Colors.black87,
        barrierLabel: 'Label',
        barrierDismissible: true,
        pageBuilder: (_, __, ___) => Center(
          child: Container(
            height: 300,
            child: const Material(
              color: Colors.transparent,
              child: FlashCardTutorial(),
            ),
          ),
        ),
      );
      preferences.setString('flashcard_tutorial', "1");
    }
    final jsonFav = preferences.getString('flashcard_favorite');
    final List listFav = jsonDecode(jsonFav ?? '[]');
    flashcardFav = listFav.map((e) => Flashcard.fromJson(e)).toList();
  }

  void skipFlashCard() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final json = preferences.getString('flashcard_skip');
    final List list = jsonDecode(json ?? '[]');
    List flashcardSkip = list.map((e) => Flashcard.fromJson(e)).toList();
    flashcardSkip.add(flashcard[index]);
    preferences.setString('flashcard_skip', jsonEncode(flashcardSkip));
    setState(() {
      if (index != flashcard.length - 1) {
        index++;
      } else {
        showEnd = true;
        preferences.setString('flashcard_show_end', "1");
      }
      skipCount++;
      preferences.setString('flashcard_current_index', index.toString());
    });
  }

  void doneFlashCard() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final json = preferences.getString('flashcard_done');
    final List list = jsonDecode(json ?? '[]');
    List flashcardDone = list.map((e) => Flashcard.fromJson(e)).toList();
    flashcardDone.add(flashcard[index]);
    preferences.setString('flashcard_done', jsonEncode(flashcardDone));
    setState(() {
      if (index != flashcard.length - 1) {
        index++;
      } else {
        preferences.setString('flashcard_show_end', "1");
        showEnd = true;
      }
      doneCount++;
      preferences.setString('flashcard_current_index', index.toString());
    });
  }

  void favoriteFlashCard() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    // preferences.remove('flashcard_favorite');
    final favIndex = flashcardFav
        .indexWhere((element) => element.fid == flashcard[index].fid);
    if (favIndex != -1) {
      flashcardFav.removeAt(favIndex);
    } else {
      flashcardFav.insert(0, flashcard[index]);
    }
    preferences.setString('flashcard_favorite', jsonEncode(flashcardFav));
    setState(() {});
  }

  Future<void> continueFlashCard() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final json = preferences.getString('flashcard_skip');
    preferences.setString('flashcard', json ?? "[]");
    preferences.remove('flashcard_done');
    preferences.remove('flashcard_skip');
    preferences.remove('flashcard_current_index');
    preferences.remove('flashcard_show_end');
    await initData();
  }

  void resetFlashCard() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.remove('flashcard_favorite');
    preferences.remove('flashcard_skip');
    preferences.remove('flashcard_done');
    preferences.remove('flashcard_current_index');
    preferences.remove('flashcard_show_end');
    await getFlashcard();
    await initData();
  }

  Future<void> getFlashcard() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "- Get Flashcard";
      String data = Common.flashcardCertId;
      String getFlashcardUrl = apiUrl + 'get_flashcard';
      try {
        final response = await dio.post(
          getFlashcardUrl,
          data: FormData.fromMap({
            'key': appkey,
            'token': token,
            'info': info,
            'data': data,
            "debugId": Common.debugId
          }),
        );
        if (response.statusCode == 200) {
          preferences.setString('flashcard', response.data);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        if (loadRemoteDatatSucceed == false) print(e);
      }
    }
  }

  String getDeviceType() {
    final data = MediaQueryData.fromWindow(WidgetsBinding.instance.window);
    return data.size.shortestSide < 550 ? 'phone' : 'tablet';
  }

  final SwipeableCardSectionController _cardController =
      SwipeableCardSectionController();

  @override
  Widget build(BuildContext context) {
    final flashcardLength = flashcard.length == 1 ? 1 : flashcard.length - 1;
    return Scaffold(
      appBar: AppBar(
        title: Text('${index + 1}/${flashcard.length}'),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new),
          iconSize: 20.0,
          color: AppColors.blackText,
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        titleTextStyle: TextStyle(
            color: AppColors.blackText,
            fontSize: 18,
            fontWeight: FontWeight.w700),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 18),
            child: GestureDetector(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute<void>(
                      builder: (BuildContext context) =>
                          const FlashCardFavorite(),
                    ));
              },
              child: SizedBox(
                width: 21,
                height: 21,
                child: Image.asset('assets/images/setting_icon.png'),
              ),
            ),
          )
        ],
      ),
      body: WillPopScope(
        onWillPop: () async => true,
        child: SafeArea(
            child: Column(children: [
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 500),
            tween: Tween<double>(
              begin: 0,
              end: flashcard.isNotEmpty
                  ? flashcard.length == 1
                      ? 1
                      : index / flashcardLength
                  : 0,
            ),
            builder: (context, value, _) => LinearProgressIndicator(
              backgroundColor: const Color(0xFFEBEBEB),
              color: const Color(0xFFAACBFD),
              value: value,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: cardCounter(),
          ),
          Expanded(
              child: FutureBuilder(
            future: isDataInit,
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                if (showEnd) {
                  return endFlashCard();
                } else {
                  List<Widget> cards = [];
                  int loopIndex = 0;
                  for (var val in flashcard) {
                    if (loopIndex < index) {
                      loopIndex++;
                      continue;
                    }
                    var card = flash_card.FlashCard(
                      key: ValueKey(val.fid),
                      backWidget: FlashCardItem(
                        text: val.term ?? "",
                      ),
                      frontWidget: FlashCardItem(
                          text: (Localizations.localeOf(context).toString() ==
                                      "vi"
                                  ? val.explanationVn == ''
                                      ? val.explanation
                                      : val.explanationVn
                                  : val.explanation) ??
                              ""),
                      width: double.infinity,
                      height: double.infinity,
                    );
                    cards.add(card);
                    loopIndex++;
                  }
                  return Padding(
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).size.height *
                            (getDeviceType() == 'phone' ? 0.05 : 0.1)),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SwipeableCardsSection(
                          cardController: _cardController,
                          enableSwipeDown: false,
                          enableSwipeUp: false,
                          items: cards,
                          context: context,
                          cardWidthTopMul: 0.925,
                          cardWidthMiddleMul: 0.7866,
                          cardWidthBottomMul: 0.717,
                          onCardSwiped: (dir, index, widget) {
                            if (cards.length > 3) {
                              _cardController.addItem(cards[3]);
                            }
                            if (dir == Direction.left) {
                              skipFlashCard();
                            } else if (dir == Direction.right) {
                              doneFlashCard();
                            }
                          },
                        ),
                      ],
                    ),
                  );
                }
              }
              return const CircularProgressIndicator();
            },
          )),
          if (!showEnd)
            Padding(
              padding: const EdgeInsets.only(bottom: 10),
              child: controllBar(context, _cardController),
            )
        ])),
      ),
    );
  }

  Widget endFlashCard() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            skipCount != 0
                ? AppLocalizations.of(context).halfWayThere
                : AppLocalizations.of(context).congratulations,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: Text(
              skipCount != 0
                  ? AppLocalizations.of(context)
                      .flashcardEndText
                      .replaceAll('{e1}', doneCount.toString())
                      .replaceAll('{e2}', skipCount.toString())
                  : AppLocalizations.of(context).learnedEverything,
              style: const TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ),
          if (skipCount != 0)
            SizedBox(
              width: 250,
              child: OutlinedButton(
                  style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.resolveWith<Color>(
                          (states) => Colors.blue),
                      side: MaterialStateProperty.all(const BorderSide(
                          color: Colors.blue,
                          width: 1.0,
                          style: BorderStyle.solid))),
                  onPressed: continueFlashCard,
                  child: Text(
                    AppLocalizations.of(context).continueStudy,
                    style: const TextStyle(fontSize: 16, color: Colors.white),
                  )),
            ),
          SizedBox(
            width: 250,
            child: TextButton(
                onPressed: _onResetPressed,
                style: ButtonStyle(
                    side: MaterialStateProperty.all(const BorderSide(
                        color: Colors.blue,
                        width: 1.0,
                        style: BorderStyle.solid))),
                child: Text(AppLocalizations.of(context).restart,
                    style: const TextStyle(fontSize: 16))),
          ),
        ],
      ),
    );
  }

  Widget cardCounter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          decoration: const BoxDecoration(
            color: Color(0xFFF1F1F1),
            boxShadow: [
              BoxShadow(
                  color: Colors.black,
                  offset: Offset(0, -1),
                  spreadRadius: 0,
                  blurRadius: 0),
              BoxShadow(
                  color: Colors.black,
                  offset: Offset(0, 1),
                  spreadRadius: 0,
                  blurRadius: 0),
              BoxShadow(
                  color: Colors.black,
                  offset: Offset(1, 0),
                  spreadRadius: 0,
                  blurRadius: 0)
            ],
            borderRadius: BorderRadius.horizontal(right: Radius.circular(8)),
          ),
          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 13),
          child: Text(
            skipCount.toString(),
            style: const TextStyle(
                fontSize: 14,
                height: 1.25,
                color: Colors.black,
                fontWeight: FontWeight.w500),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 13),
          decoration: BoxDecoration(
            color: const Color(0xFFD4FFF9),
            boxShadow: [
              BoxShadow(
                  color: AppColors.greenPrimary,
                  offset: const Offset(0, -1),
                  spreadRadius: 0,
                  blurRadius: 0),
              BoxShadow(
                  color: AppColors.greenPrimary,
                  offset: const Offset(0, 1),
                  spreadRadius: 0,
                  blurRadius: 0),
              BoxShadow(
                  color: AppColors.greenPrimary,
                  offset: const Offset(-1, 0),
                  spreadRadius: 0,
                  blurRadius: 0)
            ],
            borderRadius:
                const BorderRadius.horizontal(left: Radius.circular(8)),
          ),
          child: Text(
            doneCount.toString(),
            style: TextStyle(
                fontSize: 14,
                height: 1.25,
                color: AppColors.greenPrimary,
                fontWeight: FontWeight.w500),
          ),
        ),
      ],
    );
  }

  Widget controllBar(
      BuildContext context, SwipeableCardSectionController cardController) {
    final fav = flashcard.isNotEmpty
        ? flashcardFav.any((element) => element.fid == flashcard[index].fid)
        : false;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TapDebouncer(
          onTap: () async => cardController.triggerSwipeLeft(),
          cooldown: const Duration(milliseconds: 700),
          builder: (BuildContext context, TapDebouncerFunc? onTap) =>
              GestureDetector(
            onTap: onTap,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                  border: Border.all(width: 2, color: const Color(0xFF727272)),
                  shape: BoxShape.circle),
              child: const Icon(
                Icons.undo,
                color: Color(0xFF727272),
              ),
            ),
          ),
        ),
        GestureDetector(
          onTap: favoriteFlashCard,
          child: Container(
            width: 60,
            height: 60,
            margin: const EdgeInsets.symmetric(horizontal: 42),
            decoration: BoxDecoration(
                color: fav ? const Color(0xFFFFE8E0) : Colors.white,
                border: Border.all(width: 2, color: const Color(0xFFFF7245)),
                shape: BoxShape.circle),
            child: Icon(
              fav ? Icons.favorite : Icons.favorite_outline,
              color: const Color(0xFFFF7245),
            ),
          ),
        ),
        TapDebouncer(
          onTap: () async => cardController.triggerSwipeRight(),
          cooldown: const Duration(milliseconds: 700),
          builder: (BuildContext context, TapDebouncerFunc? onTap) =>
              GestureDetector(
            onTap: onTap,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                  border: Border.all(width: 2, color: const Color(0xFF00A690)),
                  shape: BoxShape.circle),
              child: const Icon(
                Icons.redo,
                color: Color(0xFF00A690),
              ),
            ),
          ),
        )
      ],
    );
  }

  Future<bool> _onResetPressed() async {
    return (await showDialog(
          context: context,
          builder: (BuildContext context) => Dialog(
            // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
            insetPadding: const EdgeInsets.symmetric(horizontal: 16),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
                child: Column(
                  children: [
                    Container(
                      width: 196,
                      height: 139,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                        image: AssetImage("assets/images/info_dialog.png"),
                        fit: BoxFit.fill,
                      )),
                    ),
                    Text(
                      AppLocalizations.of(context).restartFlashCardPopup,
                      style: AppStyles.dialogText,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.9,
                        child: Row(
                          children: [
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(right: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop(false);
                                },
                                child: Text(
                                  AppLocalizations.of(context).no,
                                  style: AppStyles.secondaryButton,
                                  textAlign: TextAlign.center,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.greenPrimary,
                                  backgroundColor: Colors.white,
                                  shadowColor: Colors.white,
                                  elevation: 0,
                                  minimumSize: Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            )),
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(left: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop(false);
                                  resetFlashCard();
                                },
                                child: Text(
                                  "OK",
                                  style: AppStyles.primaryButton,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor: Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            ))
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        )) ??
        false;
  }
}

class FlashCardItem extends StatelessWidget {
  const FlashCardItem({required this.text, Key? key}) : super(key: key);
  final String text;

  @override
  Widget build(BuildContext context) {
    return Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
            color: const Color(0xFF004AB9),
            borderRadius: BorderRadius.circular(8)),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
            child: Text(
              Helper().parseHtmlString(text),
              textAlign: TextAlign.center,
              style: const TextStyle(
                  color: Colors.white,
                  fontSize: 26,
                  fontWeight: FontWeight.w400),
            ),
          ),
        ));
  }
}
