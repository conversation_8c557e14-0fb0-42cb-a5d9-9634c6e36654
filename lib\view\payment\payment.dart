import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/single_child_widget.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:psm_app/data_sources/purchase_api.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/view/home.dart';
import 'package:psm_app/view/widgets/error_dialog.dart';
import 'package:psm_app/view/widgets/purchase_success.dart';
import 'package:psm_app/view/widgets/success_dialog.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';

import '../widgets/redeem_code.dart';

class PaymentPage extends StatefulWidget {
  const PaymentPage(
      {Key? key, this.fromReviewQuestion = false, this.eventLogType})
      : super(key: key);

  final bool fromReviewQuestion;
  final String? eventLogType;

  @override
  State<PaymentPage> createState() => _PaymentPageState();
}

String get url => getUserInfor().url;
String get urlPremium => getUserInfor().url_premium;

class _PaymentPageState extends State<PaymentPage> {
  int _currentIndex = 1;
  bool firstTime = true;
  Package? currentPackage;
  late Future getOffers;
  bool premium = Common.premium;
  String premiumType = Common.premiumType;
  bool available = false;
  bool packageAvailable = false;
  String device = 'phone';
  String getDeviceType() {
    final data = MediaQueryData.fromView(WidgetsBinding.instance.window);
    return data.size.shortestSide < 550 ? 'phone' : 'tablet';
  }

  @override
  void initState() {
    getOffers = fetchOffer();
    //currentPackage = getOffers[0];
    super.initState();
    logPage("Purchase");
    checkUserConnection();
    device = getDeviceType();
    //inspect(getOffers);
  }

  void setLocalPremium() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.setBool('premium', true);
  }

  Future checkUserConnection() async {
    bool result = await InternetConnectionChecker.instance.hasConnection;
    if (result == true) {
      setState(() {
        available = true;
      });
    } else {
      available = false;
    }
  }

  Future error() =>
      showDialog(context: context, builder: (context) => const ErrorDialog());
  Future success(String text) => showDialog(
      barrierColor: const Color(0xff000000).withOpacity(0.5),
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      context: context,
      builder: (context) => PurchaseSuccessDialog(
            text: text,
          ));
  Future redeemCode() => showDialog(
      barrierColor: const Color(0xff000000).withOpacity(0.5),
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      context: context,
      builder: (context) => const RedeemCodeDialog());

  Future fetchOffer() async {
    final offerings = await PurchaseApi.fetchOffer();
    if (offerings.isEmpty) {
      return null;
    } else {
      final packages = offerings
          .map((offer) => offer.availablePackages)
          .expand((pair) => pair)
          .toList();
      currentPackage = packages[1];
      setState(() {
        packageAvailable = true;
      });
      return packages;
    }
  }

  void clearLocalListQuiz() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.remove('quizList');
    preferences.remove('quizListTimer');
  }

  List paymentDuration = [
    PaymentCard(price: 89, duration: 1),
    PaymentCard(price: 199, duration: 3),
    PaymentCard(price: 499, duration: 12),
  ];

  @override
  Widget build(BuildContext context) {
    return LoaderOverlay(
      useDefaultLoading: false,
      overlayWidgetBuilder: (_) => const Center(
        child: SpinKitRing(
          color: Colors.white,
          size: 50.0,
        ),
      ),
      overlayColor: Colors.black.withOpacity(0.8),
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarBrightness: Brightness.light,
            statusBarIconBrightness: Brightness.dark),
        child: WillPopScope(
          onWillPop: () {
            Navigator.pop(context, false);
            return Future.value(false);
          },
          child: Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: widget.fromReviewQuestion == false
                ? AppBar(
                    centerTitle: false,
                    /* iconTheme: IconThemeData(
                color: Colors.black, //change your color here
              ), */
                    automaticallyImplyLeading: false,
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.close, color: Colors.black),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                    //centerTitle: true,
                    backgroundColor: Colors.white,
                    title: Text(
                      AppLocalizations.of(context).accessPremium,
                      style: AppStyles.titleBold.copyWith(
                        color: Colors.black,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    elevation: 0,
                  )
                : null,
            body: Container(
              color: Colors.white,
              child: Column(children: <Widget>[
                Padding(
                  padding:
                      const EdgeInsets.only(left: 10.0, right: 10, bottom: 0),
                  child: Container(
                    alignment: Alignment.center,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: MediaQuery.of(context).size.width,
                          height: device == 'phone'
                              ? widget.fromReviewQuestion
                                  ? MediaQuery.of(context).size.height * 0.18
                                  : MediaQuery.of(context).size.height * 0.25
                              : MediaQuery.of(context).size.height * 0.4,
                          child: SvgPicture.asset(
                            'assets/images/PaymentIcon.svg',
                            fit: device == 'phone'
                                ? BoxFit.contain
                                : BoxFit.fitHeight,
                            //width: MediaQuery.of(context).size.width * 0.6,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                              top: 5, right: 10, bottom: 5, left: 10),
                          child: Row(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(right: 10),
                                child: SvgPicture.asset(
                                  'assets/images/Checked.svg',
                                  fit: BoxFit.cover,
                                  //width: MediaQuery.of(context).size.width * 0.6,
                                ),
                              ),
                              Flexible(
                                child: RichText(
                                  text: TextSpan(
                                      style: AppStyles.body.copyWith(
                                          color: Colors.black, fontSize: 16),
                                      children: <TextSpan>[
                                        TextSpan(
                                          text:
                                              "${AppLocalizations.of(context).unlockFeature} ",
                                        ),
                                        TextSpan(
                                            text: AppLocalizations.of(context)
                                                .questionReview,
                                            style: AppStyles.bodyBold.copyWith(
                                                color: Colors.black,
                                                fontSize: 16)),
                                      ]),
                                ),
                              )
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                              top: 5, right: 10, bottom: 5, left: 10),
                          child: Row(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(right: 10),
                                child: SvgPicture.asset(
                                  'assets/images/Checked.svg',
                                  fit: BoxFit.cover,
                                  //width: MediaQuery.of(context).size.width * 0.6,
                                ),
                              ),
                              Flexible(
                                child: RichText(
                                  text: TextSpan(
                                      style: AppStyles.body.copyWith(
                                          color: Colors.black, fontSize: 16),
                                      children: <TextSpan>[
                                        TextSpan(
                                          text:
                                              "${AppLocalizations.of(context).unlock} ",
                                        ),
                                        TextSpan(
                                            text: AppLocalizations.of(context)
                                                .allTheExams,
                                            style: AppStyles.bodyBold.copyWith(
                                                color: Colors.black,
                                                fontSize: 16)),
                                      ]),
                                ),
                              )
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                              top: 5, right: 10, bottom: 5, left: 10),
                          child: Row(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(right: 10),
                                child: SvgPicture.asset(
                                  'assets/images/Checked.svg',
                                  fit: BoxFit.cover,
                                  //width: MediaQuery.of(context).size.width * 0.6,
                                ),
                              ),
                              Flexible(
                                child: RichText(
                                  text: TextSpan(
                                      style: AppStyles.body.copyWith(
                                          color: Colors.black, fontSize: 16),
                                      children: <TextSpan>[
                                        TextSpan(
                                          text:
                                              "${AppLocalizations.of(context).answersWith} ",
                                        ),
                                        TextSpan(
                                            text: AppLocalizations.of(context)
                                                .detailedExplain,
                                            style: AppStyles.bodyBold.copyWith(
                                                color: Colors.black,
                                                fontSize: 16)),
                                      ]),
                                ),
                              )
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(bottom: 10.0),
                          child: Row(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(
                                    top: 5, right: 10, bottom: 5, left: 10),
                                child: SvgPicture.asset(
                                  'assets/images/Checked.svg',
                                  fit: BoxFit.cover,
                                  //width: MediaQuery.of(context).size.width * 0.6,
                                ),
                              ),
                              Flexible(
                                child: RichText(
                                  text: TextSpan(
                                      style: AppStyles.body.copyWith(
                                          color: Colors.black, fontSize: 16),
                                      children: <TextSpan>[
                                        TextSpan(
                                          text:
                                              "${AppLocalizations.of(context).fully} ",
                                        ),
                                        TextSpan(
                                            text: AppLocalizations.of(context)
                                                .updateYear
                                                .replaceAll(
                                                    '%1',
                                                    DateTime.now()
                                                        .year
                                                        .toString()),
                                            style: AppStyles.bodyBold.copyWith(
                                                color: Colors.black,
                                                fontSize: 16)),
                                      ]),
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding:
                        const EdgeInsets.only(left: 10.0, right: 10, bottom: 0),
                    child: FutureBuilder(
                      future: getOffers,
                      builder: (BuildContext context, AsyncSnapshot snapshot) {
                        if (snapshot.hasData) {
                          return ListView.builder(
                              shrinkWrap: true,
                              itemCount: snapshot.data.length + 1,
                              itemBuilder: (BuildContext context, int index) {
                                firstTime == true
                                    ? {
                                        //currentPackage = snapshot.data[index],
                                        firstTime = false
                                      }
                                    : null;
                                return index == snapshot.data.length
                                    ? Container(
                                        margin: const EdgeInsets.fromLTRB(
                                            10, 10, 10, 0),
                                        child: Text(
                                          AppLocalizations.of(context)
                                              .trialDescription,
                                          textAlign: TextAlign.left,
                                          style: AppStyles.body.copyWith(
                                              color: AppColors.blackText,
                                              fontSize: 12,
                                              height: 1.5,
                                              fontWeight: FontWeight.w500),
                                        ),
                                      )
                                    : InkWell(
                                        onTap: () async {},
                                        child: index != _currentIndex
                                            ? GestureDetector(
                                                onTap: () {
                                                  setState(() {
                                                    _currentIndex = index;
                                                    currentPackage =
                                                        snapshot.data[index];
                                                    /* logEvent("purchase_trial_click", {
                                                "subscribse_option_time":
                                                    snapshot.data[index].storeProduct
                                                        .title
                                              }); */
                                                  });
                                                },
                                                child: PaymentOptions(
                                                  duration:
                                                      paymentDuration[index]
                                                          .duration,
                                                  price: snapshot.data[index]
                                                      .storeProduct.price,
                                                  currrency: snapshot
                                                      .data[index]
                                                      .storeProduct
                                                      .currencyCode
                                                      .toString(),
                                                ),
                                              )
                                            : PaymentOptionsSelected(
                                                duration: paymentDuration[index]
                                                    .duration,
                                                price: snapshot.data[index]
                                                    .storeProduct.price,
                                                currrency: snapshot.data[index]
                                                    .storeProduct.currencyCode
                                                    .toString(),
                                              ),
                                      );
                              });
                        } else {
                          return const Center(
                            child: SizedBox(
                              height: 50,
                              width: 50,
                              child: CircularProgressIndicator(
                                strokeWidth: 3,
                              ),
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.only(left: 20.0, right: 20, bottom: 20),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          IntrinsicHeight(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    launchUrl(Uri.parse(
                                        "https://scrumpass.com/terms-of-service/"));
                                    logEvent("purchase_term_click", {});
                                  },
                                  child: Text(
                                    AppLocalizations.of(context).term,
                                    textAlign: TextAlign.center,
                                    style: AppStyles.bodyBold.copyWith(
                                        color: available
                                            ? const Color(0xff7A8694)
                                            : const Color(0xff7A8694)
                                                .withOpacity(0.4),
                                        fontSize: 14),
                                  ),
                                ),
                                const VerticalDivider(
                                  color: Color(0xff7A8694),
                                  thickness: 2,
                                  width: 10,
                                ),
                                GestureDetector(
                                  onTap: () {
                                    launchUrl(Uri.parse(
                                        "https://scrumpass.com/privacy/"));
                                  },
                                  child: Text(
                                    AppLocalizations.of(context).privacyPolicy,
                                    textAlign: TextAlign.center,
                                    style: AppStyles.bodyBold.copyWith(
                                        color: available
                                            ? const Color(0xff7A8694)
                                            : const Color(0xff7A8694)
                                                .withOpacity(0.4),
                                        fontSize: 14),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          GestureDetector(
                            onTap: () async {
                              /* if (available && package_available) {
                                redeemCode();
                                logEvent("purchase_redeem_click", {});
                              } */
                              if (available && packageAvailable) {
                                context.loaderOverlay.show();
                                bool restore =
                                    await PurchaseApi.restorePurchase();
                                if (restore) {
                                  clearLocalListQuiz();
                                  /* showDialog<String>(
                                      context: context,
                                      builder: (BuildContext context) => AlertDialog(
                                            content: Text('Restore premium success'),
                                          )); */
                                  setState(() {
                                    premium = Common.premium;
                                    premiumType = Common.premiumType;
                                  });
                                  success(AppLocalizations.of(context)
                                      .restorePurchase);
                                  setLocalPremium();
                                  /* Navigator.push(context,
                                      MaterialPageRoute(builder: (context) => Home())); */
                                  context.loaderOverlay.hide();
                                } else {
                                  //ErrorDialog();
                                  error();
                                  context.loaderOverlay.hide();
                                }
                                context.loaderOverlay.hide();
                                logEvent("purchase_already_click", {});
                              }
                            },
                            child: Container(
                              margin: const EdgeInsets.fromLTRB(0, 10, 0, 10),
                              child: /* Text(
                                AppLocalizations.of(context).redeemCode,
                                textAlign: TextAlign.center,
                                style: AppStyles.bodyBold.copyWith(
                                    color: available
                                        ? const Color(0xff7A8694)
                                        : const Color(0xff7A8694)
                                            .withOpacity(0.4),
                                    fontSize: 14),
                              ), */
                                  Text(
                                AppLocalizations.of(context).alreadyPaid,
                                textAlign: TextAlign.center,
                                style: AppStyles.bodyBold.copyWith(
                                    color: available && packageAvailable
                                        ? AppColors.greenPrimary
                                        : AppColors.greenPrimary
                                            .withOpacity(0.4),
                                    fontSize: 14),
                              ),
                            ),
                          ),
                        ],
                      ),
                      ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            minimumSize: const Size.fromHeight(40),
                            backgroundColor: available && packageAvailable
                                ? AppColors.greenPrimary
                                : AppColors.greenPrimary.withOpacity(0.2),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Text(
                              AppLocalizations.of(context).startTrial,
                              textAlign: TextAlign.center,
                              style: AppStyles.bodyBold.copyWith(
                                  color: AppColors.white, fontSize: 18),
                            ),
                          ),
                          onPressed: () async {
                            widget.fromReviewQuestion
                                ? logEvent("review_question_try_now_click",
                                    {"question_type": widget.eventLogType})
                                : logEvent("purchase_trial_click", {
                                    "subscribse_option_time":
                                        currentPackage!.storeProduct.title
                                  });
                            if (available && packageAvailable) {
                              clearLocalListQuiz();
                              context.loaderOverlay.show();
                              var purchase = await PurchaseApi.purchasePackage(
                                  currentPackage!);
                              if (purchase == 'success') {
                                /* showDialog<String>(
                                    context: context,
                                    builder: (BuildContext context) => AlertDialog(
                                          content: Text('Subcribe Success'),
                                        )); */
                                setState(() {
                                  premium = Common.premium;
                                  premiumType = Common.premiumType;
                                  context.loaderOverlay.hide();
                                });
                                context.loaderOverlay.hide();
                                success(AppLocalizations.of(context)
                                    .thankForPurchase);
                                setLocalPremium();
                                logEvent("purchase_success", {});
                                /* Navigator.push(context,
                                    MaterialPageRoute(builder: (context) => Home())); */
                              } else if (purchase == 'canceled') {
                                logEvent("purchase_cancelled", {});
                                context.loaderOverlay.hide();
                              } else {
                                error();
                                logEvent("purchase_error",
                                    {'purchase_error_detail': purchase});
                                context.loaderOverlay.hide();
                              }
                            }
                          }),
                    ],
                  ),
                ),
              ]),
            ),
          ),
        ),
      ),
    );
  }
}

class PaymentCard {
  double price;
  int duration;
  PaymentCard({required this.price, required this.duration});
}

var formatter = NumberFormat(',000');

class PaymentOptions extends StatelessWidget {
  final double price;
  final int duration;
  final String currrency;

  const PaymentOptions(
      {Key? key,
      required this.price,
      required this.duration,
      required this.currrency})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10.0, right: 10, top: 5, bottom: 5),
      child: Container(
          //height: 70,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xffC8C8C8)),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 12.0, 16, 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: RichText(
                    text: TextSpan(
                        style: AppStyles.body
                            .copyWith(color: Colors.black, fontSize: 16),
                        children: <TextSpan>[
                          TextSpan(
                              text: price > 1000
                                  ? "${formatter.format(price)} $currrency"
                                  : "${price.toStringAsFixed(2)} $currrency",
                              style: AppStyles.bodyBold
                                  .copyWith(color: Colors.black, fontSize: 16)),
                          TextSpan(
                              text: duration > 1
                                  ? "/$duration ${AppLocalizations.of(context).months}"
                                  : "/$duration ${AppLocalizations.of(context).month}",
                              style: AppStyles.body.copyWith(
                                color: Colors.black,
                                fontSize: 16,
                              )),
                        ]),
                  ),
                ),
                SizedBox(
                  height: 25,
                  child: SvgPicture.asset(
                    'assets/images/Uncheck_Icon.svg',
                    fit: BoxFit.contain,
                    //width: MediaQuery.of(context).size.width * 0.6,
                  ),
                ),
              ],
            ),
          ) /* Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  left: 10.0, bottom: 5, top: 10, right: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    duration > 1
                        ? duration.toString() +
                            " " +
                            AppLocalizations.of(context).months
                        : duration.toString() +
                            " " +
                            AppLocalizations.of(context).month,
                    style: AppStyles.bodyBold
                        .copyWith(fontSize: 18, color: Colors.black),
                  ),
                  Text(
                    price > 1000
                        ? formatter.format(price).toString() + " " + currrency
                        : price.toStringAsFixed(2) + " " + currrency,
                    style: AppStyles.bodyBold
                        .copyWith(fontSize: 18, color: Colors.black),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(
                  left: 10.0, bottom: 10, top: 5, right: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    price > 1000
                        ? formatter
                                .format(
                                    ((price / duration) / 1000).round() * 1000)
                                .toString() +
                            " " +
                            currrency +
                            " " +
                            AppLocalizations.of(context).monthly
                        : (price / duration).toStringAsFixed(2) +
                            " " +
                            currrency +
                            " " +
                            AppLocalizations.of(context).monthly,
                    style: AppStyles.body
                        .copyWith(fontSize: 16, color: Colors.black),
                  ),
                  Text(AppLocalizations.of(context).total,
                      style: AppStyles.bodyBold
                          .copyWith(fontSize: 16, color: Colors.black)),
                ],
              ),
            ),
          ],
        ), */
          ),
    );
  }
}

class PaymentOptionsSelected extends StatelessWidget {
  final double price;
  final int duration;
  final String currrency;

  const PaymentOptionsSelected(
      {Key? key,
      required this.price,
      required this.duration,
      required this.currrency})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10.0, right: 10, top: 5, bottom: 5),
      child: Container(
        //height: 70,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: const Color(0xffDDFFFA),
          border: Border.all(color: const Color(0xff00A690), width: 3),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 12.0, 16, 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: RichText(
                  text: TextSpan(
                      style: AppStyles.body
                          .copyWith(color: Colors.black, fontSize: 16),
                      children: <TextSpan>[
                        TextSpan(
                            text: price > 1000
                                ? "${formatter.format(price)} $currrency"
                                : "${price.toStringAsFixed(2)} $currrency",
                            style: AppStyles.bodyBold.copyWith(
                                color: const Color(0xff00A690), fontSize: 16)),
                        TextSpan(
                            text: duration > 1
                                ? "/$duration ${AppLocalizations.of(context).months}"
                                : "/$duration ${AppLocalizations.of(context).month}",
                            style: AppStyles.body.copyWith(
                              color: const Color(0xff00A690),
                              fontSize: 16,
                            )),
                      ]),
                ),
              ),
              SizedBox(
                height: 25,
                child: SvgPicture.asset(
                  'assets/images/checked_Icon.svg',
                  fit: BoxFit.contain,
                  //width: MediaQuery.of(context).size.width * 0.6,
                ),
              ),
            ],
          ),
        ) /* Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  left: 10.0, bottom: 5, top: 10, right: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    duration > 1
                        ? duration.toString() +
                            " " +
                            AppLocalizations.of(context).months
                        : duration.toString() +
                            " " +
                            AppLocalizations.of(context).month,
                    style: AppStyles.bodyBold
                        .copyWith(fontSize: 18, color: Color(0xff00A690)),
                  ),
                  Text(
                    price > 1000
                        ? formatter.format(price).toString() + " " + currrency
                        : price.toStringAsFixed(2) + " " + currrency,
                    style: AppStyles.bodyBold
                        .copyWith(fontSize: 18, color: Color(0xff00A690)),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(
                  left: 10.0, bottom: 10, top: 5, right: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    price > 1000
                        ? formatter
                                .format(
                                    ((price / duration) / 1000).round() * 1000)
                                .toString() +
                            " " +
                            currrency +
                            " " +
                            AppLocalizations.of(context).monthly
                        : (price / duration).toStringAsFixed(2) +
                            " " +
                            currrency +
                            " " +
                            AppLocalizations.of(context).monthly,
                    style: AppStyles.body
                        .copyWith(fontSize: 16, color: Color(0xff00A690)),
                  ),
                  Text(AppLocalizations.of(context).total,
                      style: AppStyles.bodyBold
                          .copyWith(fontSize: 16, color: Color(0xff00A690))),
                ],
              ),
            ),
          ],
        ) */
        ,
      ),
    );
  }
}

class ReconnectingOverlay extends StatelessWidget {
  const ReconnectingOverlay({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => const Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.0),
              child: CircularProgressIndicator(),
            ),
            SizedBox(height: 12),
            Text(
              'Reconnecting...',
            ),
          ],
        ),
      );
}
