import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;

class NotificationService {
  static final NotificationService _notificationService = NotificationService._internal();

  factory NotificationService() {
    return _notificationService;
  }

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  NotificationService._internal();

  Future<void> initNotification() async {
    // Android initialization
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('app_icon');

    // ios initialization
    const initializationSettingsIOS = DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
    );

    const InitializationSettings initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid, iOS: initializationSettingsIOS);
    // the initialization settings are initialized after they are setted
    await flutterLocalNotificationsPlugin.initialize(initializationSettings);
  }

  Future<void> showNotification(
      int id, String title, String body, DateTime timeSchedule, List<int> daySchedule) async {
    await flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        //tz.TZDateTime.from(DateTime.now().add(Duration(seconds: 4)), tz.local)
        /* tz.TZDateTime.now(tz.local).add(Duration(
          seconds: 5)) */
        /* _scheduleWeekly(Time(11, 55), days: [
          DateTime.friday
        ]) */
        _scheduleWeekly(timeSchedule,
            days: daySchedule), //schedule the notification to show after 2 seconds.
        const NotificationDetails(
          // Android details
          android: AndroidNotificationDetails('CHANNEL_ID', 'CHANNEL_NAME',
              channelDescription: "CHANNEL_DESCRIPTION",
              importance: Importance.max,
              priority: Priority.max),
          // iOS details
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),

        // Type of time interpretation
        androidScheduleMode: AndroidScheduleMode.exact, // To show notification even when the app is closed
        matchDateTimeComponents: DateTimeComponents.dayOfWeekAndTime);
  }

  Future cancelNotification() async {
    await flutterLocalNotificationsPlugin.cancelAll();
  }

  static tz.TZDateTime _scheduleDaily(DateTime times) {
    final now = tz.TZDateTime.now(tz.local);
    var hourDiff = DateTime.now().timeZoneOffset.inHours;
    var hourSchedule =
        times.hour - hourDiff < 0 ? (times.hour - hourDiff) + 24 : times.hour - hourDiff;
    final scheduleDate = tz.TZDateTime(
        tz.local, now.year, now.month, now.day, hourSchedule, times.minute, times.second);
    return scheduleDate.isBefore(now) ? scheduleDate.add(const Duration(days: 1)) : scheduleDate;
  }

  static DateTime changeTime(
      DateTime dateTime, int hours, int minutes, int seconds, int milliseconds) {
    return DateTime(dateTime.year, dateTime.month, dateTime.day, hours, minutes, seconds);
  }

  static tz.TZDateTime _scheduleWeekly(DateTime time, {required List<int> days}) {
    tz.TZDateTime scheduleDate = _scheduleDaily(time);
    while (!days.contains(scheduleDate.weekday)) {
      scheduleDate = scheduleDate.add(const Duration(days: 1));
    }
    return scheduleDate;
  }
}
