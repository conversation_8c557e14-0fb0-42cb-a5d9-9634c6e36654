import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:html/parser.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:psm_app/data_sources/api_servies.dart' hide appkey;
import 'package:psm_app/globals.dart';
import 'package:psm_app/helper/helper.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/models/question_api_model.dart';
import 'package:psm_app/models/result_model.dart';
import 'package:psm_app/view/exam/exam.dart';
import 'package:psm_app/view/home.dart';
import 'package:psm_app/view/home/<USER>/level_button_widget.dart';
import 'package:psm_app/view/payment/payment.dart';
import 'package:psm_app/view/widgets/error_dialog.dart';
import 'package:psm_app/view/widgets/success_dialog.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../report_detail.dart';

Future<int> getCountQuiz(String quizname, String time) async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  // Fetch and decode data
  final String resultString = await prefs.getString('result') ?? "";
  final List<Result> musics = Result.decode(resultString);
  int count = 0;
  if (musics == "") {
    return count;
  } else {
    for (var i = 0; i < musics.length; i++) {
      if (musics[i].name == quizname) {
        count++;
      }
      if (musics[i].time == time) {
        break;
      }
    }
    return count;
  }
}

int test = 0;
int old_index = 0;
bool openPremiumPopup = false;
List avaliableFilter = [];

class DetailResult extends StatefulWidget {
  final Map<int, dynamic> answers;
  final Map<int, dynamic> answersText;
  final Map bookmark;
  final List<Question> questions;
  final String quizType;
  final String quizId;
  final String time;
  final String quizName;
  final String exam_quiz;
  double percent = 0;
  late Map correctList = {};
  var correctQuestion;
  final int passPercent;
  final int quizDuration;
  final int timeDoQuiz; //seconds
  final bool shuffleOptions;
  DetailResult({
    Key? key,
    required this.answers,
    required this.exam_quiz,
    this.quizType = "1",
    required this.questions,
    required this.answersText,
    required this.quizId,
    required this.quizName,
    required this.time,
    required this.bookmark,
    required this.passPercent,
    required this.quizDuration,
    required this.timeDoQuiz,
    this.shuffleOptions = false,
  }) : super(key: key) {
    answers.forEach((index, value) {
      if (questions[index].type == "single") {
        correctQuestion = questions[index].correct;
        if (correctQuestion == value) {
          correctList[index] = true;
          correct++;
        } else {
          correctList[index] = false;
        }
      } else if (questions[index].type == "multi") {
        correctList[index] = true;
        int selectedCount = 0;
        if (shuffleOptions) {
          if (value is Map) {
            final arr = value;
            arr.forEach((key, value) {
              if (value) selectedCount++;
            });
          }
          // for (var val in value) {
          //   if (val) selectedCount++;
          // }
          var correctIds = questions[index].correct?.split(',');
          for (int i = 0; i < (correctIds?.length ?? 0); i++) {
            if (value[correctIds?[i]] == false ||
                questions[index].correctIndex?.length != selectedCount) {
              correctList[index] = false;
              continue;
            }
          }
        } else {
          for (var val in value) {
            if (val) selectedCount++;
          }
          questions[index].correctIndex!.forEach((element) {
            if (answers[index][element] == false ||
                questions[index].correctIndex?.length != selectedCount) {
              correctList[index] = false;
              return;
            }
          });
        }

        if (correctList[index] == true) correct++;
      }
    });
    percent = correct / questions.length;
  }

  int correct = 0;
  @override
  ExamRes createState() => ExamRes();
}

class ExamRes extends State<DetailResult> {
  List<Point> myList = [];

  @override
  void initState() {
    super.initState();
    test = 0;
    old_index = 0;
    logPage("Exam Result Detail");
    for (int i = 0; i < widget.questions.length; i++) {
      myList.add(Point(qid: i, status: false));
    }
    openPremiumPopup = false;
    //print(Common.appName);
    //print(widget.questions[47].correct!.split(","));
    inspect(widget.questions);
    /* for (int i = 0; i < widget.questions[74].options!.length; i++) {
      bool check = true;
      for (int j = 0; j < widget.questions[74].correctIndex!.length; j++) {
        if (i == widget.questions[74].correctIndex![j]) {
          print('ket qua dung: ' + widget.questions[74].options![i].qOption);
          check = false;
        }
      }
      if (check) {
        if (widget.answersText[74]
            .toString()
            .contains(widget.questions[74].options![i].qOption)) {
          print('da chon ' + widget.questions[74].options![i].qOption);
        } else {
          print('Cac ket qua ' + widget.questions[74].options![i].qOption);
        }
      }
    } */
  }

  launchURL() async {
    const url = 'http://m.me/scrumpassvn';
    if (await canLaunch(url)) {
      launch(
        url,
        enableJavaScript: true,
      );
    } else if (await canLaunch(
        'mailto:<EMAIL>?subject=Reprot issue for app ${Common.appName}&body=')) {
      launch('mailto:<EMAIL>?subject=Reprot issue for app ${Common.appName}&body=');
    } else {
      return (await showDialog(
            context: context,
            builder: (BuildContext context) => AlertDialog(
              title: Text(AppLocalizations.of(context).error),
              content: Text(AppLocalizations.of(context).errorDialog),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
          )) ??
          false;
    }
  }

  Future<bool> _onRetryPressed() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final resumeQuiz = preferences.getString('resumeQuiz') ?? '';
    return (await showDialog(
          context: context,
          builder: (BuildContext context) => Dialog(
            // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
            insetPadding: const EdgeInsets.symmetric(horizontal: 16),
            child: Container(
              width: MediaQuery.of(context).size.width,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
                  child: Column(
                    children: [
                      Container(
                        width: 196,
                        height: 139,
                        decoration: const BoxDecoration(
                            image: DecorationImage(
                          image: AssetImage("assets/images/info_dialog.png"),
                          fit: BoxFit.fill,
                        )),
                      ),
                      Text(
                        AppLocalizations.of(context).confirmRetry,
                        style: AppStyles.dialogText,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 24),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          child: Row(
                            children: [
                              Expanded(
                                  child: Padding(
                                padding: const EdgeInsets.only(right: 6),
                                child: ElevatedButton(
                                  onPressed: () {
                                    Navigator.of(context).pop(false);
                                  },
                                  child: Text(
                                    AppLocalizations.of(context).no,
                                    style: AppStyles.secondaryButton,
                                    textAlign: TextAlign.center,
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    foregroundColor: AppColors
                                        .greenPrimary, backgroundColor: Colors.white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                                    shadowColor:
                                        Colors.white, //specify the button's elevation color
                                    elevation: 0, //buttons Material shadow
                                    // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                                    minimumSize: const Size(20,
                                        44), //specify the button's first: width and second: height
                                    side: BorderSide(
                                        color: AppColors.greenPrimary,
                                        width: 1.0,
                                        style: BorderStyle.solid), //set border for the button
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(4.0)),
                                  ),
                                ),
                              )),
                              Expanded(
                                  child: Padding(
                                padding: const EdgeInsets.only(left: 6),
                                child: ElevatedButton(
                                  onPressed: () {
                                    Navigator.of(context).pop(false);
                                    Navigator.pushReplacement(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) => Exam(
                                                  exam_quiz: widget.exam_quiz,
                                                  idQuiz: widget.quizId,
                                                  questionSelection: widget.quizType,
                                                  quizName: widget.quizName,
                                                  passPercent: widget.passPercent,
                                                  duration: widget.quizDuration,
                                                  resume:
                                                      resumeQuiz == widget.quizId ? true : false,
                                                )));
                                  },
                                  child: Text(
                                    "OK",
                                    style: AppStyles.primaryButton,
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    foregroundColor: AppColors.white, backgroundColor: AppColors.greenPrimary,
                                    shadowColor: const Color.fromARGB(92, 0, 166, 144),
                                    elevation: 0,
                                    minimumSize: const Size(20, 44),
                                    side: BorderSide(
                                        color: AppColors.greenPrimary,
                                        width: 1.0,
                                        style: BorderStyle.solid),
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(4.0)),
                                  ),
                                ),
                              ))
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ),
        )) ??
        false;
  }

  callbackSetState() {
    setState(() {});
  }

  /*  Future changeFilterRight() async {
    setState(() {
      int index = avaliableFilter.indexWhere((item) => item == test);
      if (index < avaliableFilter.length - 1) {
        old_index = test;
        test = avaliableFilter[index + 1];
      }
    });
    itemScrollController.scrollTo(
        index: test,
        alignment: 0.3,
        duration: const Duration(milliseconds: 500),
        curve: Curves.linear);
  }

  Future changeFilterLeft() async {
    setState(() {
      int index = avaliableFilter.indexWhere((item) => item == test);
      if (index != 0) {
        old_index = test;
        test = avaliableFilter[index - 1];
      }
    });
    itemScrollController.scrollTo(
        index: test,
        alignment: 0.3,
        duration: const Duration(milliseconds: 500),
        curve: Curves.linear);
  } */

  @override
  Widget build(BuildContext context) {
    int count = 0;
    return WillPopScope(
      onWillPop: () async => !(Navigator.of(context).userGestureInProgress),
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarBrightness: Brightness.dark,
            statusBarIconBrightness: Brightness.light), // light status bar text
        child: FutureBuilder<int>(
            future: getCountQuiz(widget.quizName, widget.time), // async work
            builder: (BuildContext context, AsyncSnapshot<int> snapshot) {
              if (snapshot.hasError) {
                count = 0;
              } else {
                count = snapshot.data ?? 0;
              }
              return LoaderOverlay(
                useDefaultLoading: false,
                overlayWidgetBuilder: (_) => const Center(
                  child: SpinKitRing(
                    color: Colors.white,
                    size: 50.0,
                  ),
                ),
                overlayColor: Colors.black.withOpacity(0.8),
                child: Scaffold(
                  /* appBar: PreferredSize(
                        preferredSize: Size.fromHeight(220),
                        child: ResultAppBarWidget(
                            correct: widget.correct,
                            incorrect: widget.answers.length - widget.correct,
                            percent: widget.percent,
                            passPercent: widget.passPercent,
                            num: count,
                            time: widget.time,
                            timeDoQuiz: widget.timeDoQuiz,
                            context: context),
                      ), */
                  appBar: AppBar(
                    centerTitle: true,
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back_ios_new_rounded, color: Colors.black),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    title: Text(
                      AppLocalizations.of(context).quizDetail,
                      style: AppStyles.bodyBold.copyWith(color: Colors.black, fontSize: 18),
                    ),
                    backgroundColor: Colors.white,
                    elevation: 0,
                  ),
                  body: WillPopScope(
                    onWillPop: () async {
                      if (Navigator.of(context).userGestureInProgress)
                        return false;
                      else
                        return true;
                    },
                    child: Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        Column(
                          children: [
                            BodyContent(
                              questions: widget.questions,
                              answers: widget.answers,
                              answersText: widget.answersText,
                              quizName: widget.quizName,
                              correctList: widget.correctList,
                              bookmark: widget.bookmark,
                              percent: widget.percent,
                              pass_percent: widget.passPercent,
                              quizId: widget.quizId,
                              callbackSetState: callbackSetState,
                              shuffleOptions: widget.shuffleOptions,
                            ),
                            /* Expanded(
                              child: ListView.builder(
                                padding: const EdgeInsets.all(10.0),
                                itemCount: widget.questions.length,
                                itemBuilder: _buildItem,
                              ),
                    ), */
                            Container(
                              width: double.infinity,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 18),
                                child: Row(
                                  children: [
                                    Expanded(
                                        child: Padding(
                                      padding: const EdgeInsets.only(top: 12, right: 6, bottom: 20),
                                      child: ElevatedButton(
                                        onPressed: () {
                                          logPage("Main");
                                          Navigator.pushReplacement(context,
                                              MaterialPageRoute(builder: (context) => Home()));
                                        },
                                        child: Text(
                                          AppLocalizations.of(context).home,
                                          style: AppStyles.secondaryButton,
                                          textAlign: TextAlign.center,
                                        ),
                                        style: ElevatedButton.styleFrom(
                                          foregroundColor: AppColors
                                              .greenPrimary, backgroundColor: Colors.white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                                          shadowColor:
                                              Colors.white, //specify the button's elevation color
                                          elevation: 0, //buttons Material shadow
                                          // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                                          minimumSize: const Size(20,
                                              48), //specify the button's first: width and second: height
                                          side: BorderSide(
                                              color: AppColors.greenPrimary,
                                              width: 1.0,
                                              style: BorderStyle.solid), //set border for the button
                                          shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(4.0)),
                                        ),
                                      ),
                                    )),
                                    Expanded(
                                        child: Padding(
                                      padding: const EdgeInsets.only(top: 12, left: 6, bottom: 20),
                                      child: ElevatedButton(
                                        onPressed: () {
                                          logEvent("exam_result_detail_retry_click", {
                                            "test_name": widget.quizName,
                                            "test_result": widget.percent >= widget.passPercent
                                                ? "Pass"
                                                : "Fail"
                                          });
                                          _onRetryPressed();
                                        },
                                        child: Text(
                                          AppLocalizations.of(context).retry,
                                          style: AppStyles.primaryButton,
                                        ),
                                        style: ElevatedButton.styleFrom(
                                          foregroundColor: AppColors.white, backgroundColor: AppColors.greenPrimary,
                                          shadowColor: const Color.fromARGB(92, 0, 166, 144),
                                          elevation: 4,
                                          minimumSize: const Size(20, 48),
                                          side: BorderSide(
                                              color: AppColors.greenPrimary,
                                              width: 1.0,
                                              style: BorderStyle.solid),
                                          shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(4.0)),
                                        ),
                                      ),
                                    ))
                                  ],
                                ),
                              ),
                            )
                          ],
                        ),
                        openPremiumPopup == false
                            ? Container()
                            : Positioned(
                                bottom: 20,
                                child: PremiumPopup2(
                                  quiz_name: widget.quizName,
                                  quiz_result:
                                      widget.percent >= widget.passPercent ? "Pass" : "Fail",
                                )),
                      ],
                    ),
                  ),
                ),
              );
            }),
      ),
    );
  }
}

class MyDialog extends StatefulWidget {
  const MyDialog({
    Key? key,
    required this.questions,
    required this.quizId,
    required this.num,
  }) : super(key: key);
  final Question questions;
  final String quizId;
  final int num;
  @override
  _MyDialogState createState() => _MyDialogState();
}

class _MyDialogState extends State<MyDialog> {
  int dropdownvalue = 1;
  late TextEditingController controltext;
  List<int> arr = [];
  @override
  void initState() {
    super.initState();
    controltext = TextEditingController();
  }

  @override
  void dispose() {
    super.dispose();
    controltext.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: new BoxConstraints(
              maxWidth: 600.0,
            ),
            child: Container(
              width: device == 'phone' ? MediaQuery.of(context).size.width : 600,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context).question + ":",
                    style: AppStyles.reportDialogTitle,
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  Container(
                    // constraints: BoxConstraints(maxHeight: 90),
                    child: Column(
                      children: <Widget>[
                        Container(
                          width: double.infinity,
                          //height: 200.0,
                          color: Colors.white.withOpacity(0.7),
                          child: Text(
                            parse(widget.questions.question).documentElement!.text,
                            style: AppStyles.reportDialogText,
                          ),
                        )
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    AppLocalizations.of(context).report + ":",
                    style: AppStyles.reportDialogTitle,
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  TextField(
                    onTap: () {
                      setState(() {
                        isEmpty = false;
                      });
                    },
                    decoration: InputDecoration(
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: isEmpty ? Colors.red : const Color(0xFFACB7C5),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                            color: isEmpty ? Colors.red : const Color(0xFFACB7C5), width: 1.0),
                      ),
                    ),
                    autofocus: false,
                    maxLines: 3,
                    // expands: true,
                    keyboardType: TextInputType.text,
                    controller: controltext,
                    textAlignVertical: TextAlignVertical.top,
                  ),
                  isEmpty == true
                      ? Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Text(
                            AppLocalizations.of(context).emptyField,
                            style: AppStyles.reportDialogText.copyWith(color: Colors.red),
                          ),
                        )
                      : Container(),
                  const SizedBox(
                    height: 24,
                  ),
                  Row(
                    children: [
                      Expanded(
                          child: Padding(
                        padding: const EdgeInsets.only(right: 6),
                        child: ElevatedButton(
                          onPressed: close,
                          child: Text(
                            AppLocalizations.of(context).cancel,
                            style: AppStyles.secondaryButton,
                            textAlign: TextAlign.center,
                          ),
                          style: ElevatedButton.styleFrom(
                            foregroundColor: AppColors
                                .greenPrimary, backgroundColor: Colors.white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                            shadowColor: Colors.white, //specify the button's elevation color
                            elevation: 0, //buttons Material shadow
                            // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                            minimumSize: const Size(
                                20, 44), //specify the button's first: width and second: height
                            side: BorderSide(
                                color: AppColors.greenPrimary,
                                width: 1.0,
                                style: BorderStyle.solid), //set border for the button
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
                          ),
                        ),
                      )),
                      Expanded(
                          child: Padding(
                        padding: const EdgeInsets.only(left: 6),
                        child: ElevatedButton(
                          onPressed: submit,
                          child: Text(
                            AppLocalizations.of(context).send,
                            style: AppStyles.primaryButton,
                          ),
                          style: ElevatedButton.styleFrom(
                            foregroundColor: AppColors.white, backgroundColor: AppColors.greenPrimary,
                            shadowColor: const Color.fromARGB(92, 0, 166, 144),
                            elevation: 0,
                            minimumSize: const Size(20, 44),
                            side: BorderSide(
                                color: AppColors.greenPrimary,
                                width: 1.0,
                                style: BorderStyle.solid),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
                          ),
                        ),
                      ))
                    ],
                  )
                ],
              ),
            ),
          ),
        ),
      ),
      // actions: [
      //   TextButton(
      //       onPressed: submit,
      //       child: Text(AppLocalizations.of(context).send)),
      //   TextButton(
      //       onPressed: close,
      //       child: Text(AppLocalizations.of(context).cancel))
      // ]
    );
  }

  Future<void> sendReport() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    String url = apiUrl + "send_report";
    String parsedstring = '';
    var doc = parse(widget.questions.question);
    if (doc.documentElement != null) {
      parsedstring = doc.documentElement!.text;
    }
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "-Quiz Report";
      Map data = {
        'appid': Common.appid,
        'qid': widget.questions.id,
        'quid': widget.quizId,
        'email': '',
        'message': controltext.text,
        'questionReport': parsedstring,
      };
      try {
        final response = await dio.post(url,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data),
              "debugId": Common.debugId
            }));
        if (response.statusCode == 200) {
          inspect(response.data);
          loadRemoteDatatSucceed = true;
          getReportedQuestion();
        }
      } catch (e) {
        // if (loadRemoteDatatSucceed == false) retryFuture(sendReport, 200);
      }
    }
  }

  bool isEmpty = false;
  void getReportedQuestion() async {
    final result = await ApiServices().listReported();
  }

  void submit() {
    //Navigator.of(context).pop(controltext);
    if (controltext.text.isEmpty) {
      //error();
      setState(() {
        isEmpty = true;
      });
    } else {
      Navigator.of(context).pop(controltext);
      sendReport();
      success();
    }
    //Navigator.of(context).pop();
    //inspect(widget.quizId);
  }

  void close() {
    Navigator.of(context).pop();
  }

  Future success() => showDialog(
      context: context,
      builder: (context) => SuccessDialog(
          text: AppLocalizations.of(context)
              .reportSuccess /* actions: [TextButton(onPressed: close, child: const Text("OK"))] */
          ));
  Future error() => showDialog(context: context, builder: (context) => const ErrorDialog());
  DropdownMenuItem<int> buildMenuItem(int e) =>
      DropdownMenuItem(value: e, child: Text(e.toString()));
}

class BodyContent extends StatefulWidget {
  const BodyContent({
    Key? key,
    required this.questions,
    required this.answers,
    required this.answersText,
    required this.bookmark,
    required this.quizId,
    required this.quizName,
    required this.percent,
    required this.pass_percent,
    required this.correctList,
    required this.callbackSetState,
    this.shuffleOptions = false,
  }) : super(key: key);
  final Map<int, dynamic> answers;
  final Map<int, dynamic> answersText;
  final Map bookmark;
  final List<Question> questions;
  final String quizName;
  final double percent;
  final int pass_percent;
  final String quizId;
  final Map correctList;
  final Function callbackSetState;
  final bool shuffleOptions;
  @override
  _BodyContentState createState() => _BodyContentState();
}

class _BodyContentState extends State<BodyContent> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late List<AnimationController> dataCtrl = [];
  bool statusFilter = true;
  late TabController _tabController;
  @override
  void initState() {
    super.initState();
    getNumMarked();
    /* dataCtrl.add(AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )); */
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.fastLinearToSlowEaseIn,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  _toggleContainer() {
    //print(index);
    //dataCtrl[0].forward();
    //print(_animation.status);
    if (_animation.status != AnimationStatus.completed) {
      _controller.forward();
    } else {
      _controller.animateBack(0, duration: const Duration(milliseconds: 500));
    }
  }

  Future<List> getNumMarked() async {
    int marked = 0;
    int correct = 0;
    int wrong = 0;
    int unAnswered = 0;
    int allAns = widget.questions.length;
    List<int> arr;
    for (var i = 0; i < widget.questions.length; i++) {
      if (widget.bookmark[i] == 1) {
        marked++;
      }
      if (widget.correctList[i]) {
        correct++;
      }
      if (widget.answersText[i] == '' || widget.answersText[i].toString() == "[]") {
        unAnswered++;
      }
    }
    wrong = widget.questions.length - correct - unAnswered;
    arr = [allAns, marked, wrong, correct, unAnswered];
    if (statusFilter) {
      avaliableFilter = [0];
      if (marked != 0) {
        avaliableFilter.add(1);
      }
      if (wrong != 0) {
        avaliableFilter.add(2);
      }
      if (correct != 0) {
        avaliableFilter.add(3);
      }
      if (unAnswered != 0) {
        avaliableFilter.add(4);
      }
      statusFilter = false;
      _tabController = new TabController(vsync: this, length: avaliableFilter.length);
      _tabController.addListener(() {
        var index = avaliableFilter[_tabController.index];
        logEvent("exam_result_detail_filter_click", {
          "test_name": widget.quizName,
          "test_result": widget.percent >= widget.pass_percent ? "Pass" : "Fail",
          "filter_select": index == 0
              ? "All Answers"
              : index == 1
                  ? "Marked Answers"
                  : index == 2
                      ? "Wrong Answers"
                      : index == 3
                          ? "Right Answers"
                          : "No Answers"
        });
      });
    }
    return arr;
  }

  bool _expanded = false;
  ItemScrollController itemScrollController = ItemScrollController();
  ItemScrollController itemScrollController2 = ItemScrollController();
  ItemScrollController itemScrollController3 = ItemScrollController();
  ItemScrollController itemScrollController4 = ItemScrollController();
  ItemScrollController itemScrollController5 = ItemScrollController();
  final ValueNotifier<double> notifier = ValueNotifier(0);
  @override
  Widget build(BuildContext context) {
    ScrollController _scrollController = ScrollController();
    final ItemPositionsListener itemPositionsListener = ItemPositionsListener.create();
    return Expanded(
        child: Container(
            // ADDED "child:"
            alignment: Alignment.center,
            child: FutureBuilder<List<dynamic>>(
                future: getNumMarked(), // async work
                builder: (context, snapshot) {
                  if (!snapshot.hasData) {
                    return const CircularProgressIndicator();
                  }
                  return ValueListenableBuilder<double>(
                      valueListenable: notifier,
                      builder: (context, value, child) {
                        return DefaultTabController(
                          length: snapshot.data!.length,
                          child: Column(children: <Widget>[
                            Container(
                              width: MediaQuery.of(context).size.width,
                              decoration: BoxDecoration(
                                  color: value == 0 ? const Color(0xFFFAFAFA) : Colors.white,
                                  border: value == 1
                                      ? const Border(
                                          top: BorderSide(width: 1, color: Color(0xFFD5D7DB)),
                                          bottom: BorderSide(width: 1, color: Color(0xFFD5D7DB)),
                                        )
                                      : null),
                              child: Center(
                                child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20),
                                  child: TabBar(
                                    controller: _tabController,
                                    indicator: BoxDecoration(
                                      color: AppColors.scoreCardText,
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.fromBorderSide(
                                        BorderSide(color: AppColors.scoreCardText),
                                      ),
                                    ),
                                    unselectedLabelColor: AppColors.blackText,
                                    labelColor: AppColors.lightBlueText,
                                    isScrollable: true,
                                    tabs: [
                                      for (var i = 0; i < avaliableFilter.length; i++)
                                        LevelButtonWidget(
                                          label: "NotSelect",
                                          type: avaliableFilter[i] == 0
                                              ? AppLocalizations.of(context).allAns
                                              : avaliableFilter[i] == 1
                                                  ? "${AppLocalizations.of(context).marked} (${snapshot.data![avaliableFilter[i]]})"
                                                  : avaliableFilter[i] == 2
                                                      ? "${AppLocalizations.of(context).wrongAns} (${snapshot.data![avaliableFilter[i]]})"
                                                      : avaliableFilter[i] == 3
                                                          ? "${AppLocalizations.of(context).rightAns} (${snapshot.data![avaliableFilter[i]]})"
                                                          : "${AppLocalizations.of(context).noAnsSelected} (${snapshot.data![avaliableFilter[i]]})",
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: NotificationListener<ScrollNotification>(
                                onNotification: (n) {
                                  if (n.metrics.pixels >= 50) {
                                    notifier.value = 1;
                                  } else if (n.metrics.pixels == 0) {
                                    notifier.value = 0;
                                  }
                                  return false;
                                },
                                child: TabBarView(
                                  controller: _tabController,
                                  children: [
                                    for (var i = 0; i < avaliableFilter.length; i++)
                                      ScrollablePositionedList.builder(
                                        physics: const ClampingScrollPhysics(),
                                        //key: ObjectKey(type),
                                        itemScrollController: i == 0
                                            ? itemScrollController
                                            : i == 1
                                                ? itemScrollController2
                                                : i == 2
                                                    ? itemScrollController3
                                                    : i == 3
                                                        ? itemScrollController4
                                                        : itemScrollController5,
                                        //controller: _scrollController,
                                        padding: const EdgeInsets.only(
                                            left: 10.0, right: 10, bottom: 20, top: 10),
                                        itemCount: widget.questions.length,
                                        itemBuilder: (ctxt, Index) =>
                                            _buildItem(ctxt, Index, avaliableFilter[i], i),
                                        shrinkWrap: true,
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ]),
                        );
                      });
                })));
  }

  callback(index, position, typeFilter) {
    /* var test = MediaQuery.of(context).size.height;
    if (double.parse(newAbc) > MediaQuery.of(context).size.height - 100) {
      setState(() {
        _scrollController.animateTo(
            double.parse(newAbc) + MediaQuery.of(context).size.height,
            curve: Curves.easeOut,
            duration: Duration(seconds: 2));
      });
    } else {
    } */
    if (double.parse(position) > MediaQuery.of(context).size.height - 100) {
      switch (typeFilter) {
        case 0:
          {
            Timer(const Duration(milliseconds: 400), () {
              itemScrollController.scrollTo(
                  index: index,
                  alignment: -0.3,
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeInOut);
            });
            break;
          }
        case 1:
          {
            Timer(const Duration(milliseconds: 400), () {
              itemScrollController2.scrollTo(
                  index: index,
                  alignment: 0,
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeInOut);
            });
            break;
          }
        case 2:
          {
            Timer(const Duration(milliseconds: 400), () {
              itemScrollController3.scrollTo(
                  index: index,
                  alignment: -0.3,
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeInOut);
            });
            break;
          }
        case 3:
          {
            Timer(const Duration(milliseconds: 400), () {
              itemScrollController4.scrollTo(
                  index: index,
                  alignment: -0.3,
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeInOut);
            });
            break;
          }
        default:
          {
            Timer(const Duration(milliseconds: 400), () {
              itemScrollController5.scrollTo(
                  index: index,
                  alignment: -0.3,
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeInOut);
            });
            break;
          }
      }
      setState(() {});
    }
  }

  Widget rowResultAns(String aphabet, String optionDetail, bool green, bool TraLoiDung,
      String typeQuestion, bool userSelect) {
    optionDetail = replaceSpecialCharacters(optionDetail);
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 10,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (typeQuestion == 'single') ...[
            if (green) ...[
              if (userSelect) ...[
                Container(
                  width: 20,
                  child:
                      SvgPicture.asset('assets/images/radioboxCheckRight.svg', fit: BoxFit.contain),
                )
              ] else ...[
                Container(
                    width: 20,
                    child: SvgPicture.asset('assets/images/radioboxUncheckRight.svg',
                        fit: BoxFit.contain))
              ]
            ] else ...[
              if (userSelect) ...[
                Container(
                  width: 20,
                  child:
                      SvgPicture.asset('assets/images/radioboxCheckWrong.svg', fit: BoxFit.contain),
                )
              ] else ...[
                Container(
                    width: 20,
                    child: SvgPicture.asset('assets/images/radioboxUncheckWrong.svg',
                        fit: BoxFit.contain)),
              ]
            ]
            //Multi
          ] else ...[
            if (green) ...[
              if (userSelect) ...[
                Container(
                  width: 20,
                  child:
                      SvgPicture.asset('assets/images/checkboxRightCheck.svg', fit: BoxFit.contain),
                )
              ] else ...[
                Container(
                    width: 20,
                    child: SvgPicture.asset('assets/images/checkboxRightUnCheck.svg',
                        fit: BoxFit.contain))
              ]
            ] else ...[
              if (userSelect) ...[
                Container(
                  width: 20,
                  child:
                      SvgPicture.asset('assets/images/checkboxWrongCheck.svg', fit: BoxFit.contain),
                )
              ] else ...[
                Container(
                    width: 20,
                    child: SvgPicture.asset('assets/images/checkboxWrongUnCheck.svg',
                        fit: BoxFit.contain)),
              ]
            ]
          ],
          if (typeQuestion == 'single') ...[
            if (TraLoiDung) ...[
              if (userSelect) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Text(aphabet + ".",
                      style: AppStyles.questionText
                          .copyWith(fontWeight: FontWeight.w700, color: const Color(0xff139C29))),
                ),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8.0, top: 0),
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: buildOptionItem(optionDetail: optionDetail, type: "correct"),
                    ),
                  ),
                ),
                Container(
                  width: 20,
                  child: SvgPicture.asset('assets/images/RightAnsIcon.svg', fit: BoxFit.contain),
                )
              ] else ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Text(aphabet + ".",
                      style: AppStyles.questionText.copyWith(fontWeight: FontWeight.w700)),
                ),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8.0, top: 0),
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: buildOptionItem(optionDetail: optionDetail, type: "default"),
                    ),
                  ),
                ),
                Container(
                    width: 20,
                    child: SvgPicture.asset('assets/images/radioboxRightNotAns.svg',
                        fit: BoxFit.contain))
              ]
            ] else ...[
              if (userSelect == true) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Text(aphabet + ".",
                      style: AppStyles.questionText
                          .copyWith(fontWeight: FontWeight.w700, color: const Color(0xffFF040F))),
                ),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8.0, top: 0),
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: buildOptionItem(optionDetail: optionDetail, type: "wrong"),
                    ),
                  ),
                ),
                Container(
                  width: 20,
                  child: SvgPicture.asset('assets/images/WrongAnsIcon.svg', fit: BoxFit.contain),
                )
              ] else ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Text(aphabet + ".",
                      style: AppStyles.questionText.copyWith(fontWeight: FontWeight.w700)),
                ),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8.0, top: 0),
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: buildOptionItem(optionDetail: optionDetail, type: "default"),
                    ),
                  ),
                ),
                Container(
                  width: 20,
                )
              ]
            ]
            //Multi
          ] else ...[
            if (TraLoiDung) ...[
              if (userSelect) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Text(aphabet + ".",
                      style: AppStyles.questionText
                          .copyWith(fontWeight: FontWeight.w700, color: const Color(0xff139C29))),
                ),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8.0, top: 0),
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: buildOptionItem(optionDetail: optionDetail, type: "correct"),
                    ),
                  ),
                ),
                Container(
                  width: 20,
                  child: SvgPicture.asset('assets/images/MultiRightAns.svg', fit: BoxFit.contain),
                )
              ] else ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Text(aphabet + ".",
                      style: AppStyles.questionText.copyWith(fontWeight: FontWeight.w700)),
                ),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8.0, top: 0),
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: buildOptionItem(optionDetail: optionDetail, type: "default"),
                    ),
                  ),
                ),
                Container(
                    width: 20,
                    child:
                        SvgPicture.asset('assets/images/MultiRightNotAns.svg', fit: BoxFit.contain))
              ]
            ] else ...[
              if (userSelect == true) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Text(aphabet + ".",
                      style: AppStyles.questionText
                          .copyWith(fontWeight: FontWeight.w700, color: const Color(0xffFF040F))),
                ),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8.0, top: 0),
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: buildOptionItem(optionDetail: optionDetail, type: "wrong"),
                    ),
                  ),
                ),
                Container(
                  width: 20,
                  child: SvgPicture.asset('assets/images/MultiWrongAns.svg', fit: BoxFit.contain),
                )
              ] else ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Text(aphabet + ".",
                      style: AppStyles.questionText.copyWith(fontWeight: FontWeight.w700)),
                ),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8.0, top: 0),
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: buildOptionItem(optionDetail: optionDetail, type: "default"),
                    ),
                  ),
                ),
                Container(
                  width: 20,
                )
              ]
            ]
          ]
        ],
      ),
    );
  }

  Widget buildOptionItem({required String optionDetail, required String type}) {
    if (optionDetail.contains("<img")) {
      return Html(
        data: replaceLinkImg(optionDetail),
        style: optionHtmlStyle(
            context,
            type == "wrong"
                ? const Color(0xffFF040F)
                : type == "correct"
                    ? const Color(0xff139C29)
                    : AppColors.blackText),
      );
    } else {
      RegExp exp = RegExp(r"<[^>]*>", multiLine: true, caseSensitive: true);
      switch (type) {
        case "correct":
          return Text(optionDetail.replaceAll(exp, ''),
              style: AppStyles.questionText.copyWith(
                fontWeight: FontWeight.w500,
                color: const Color(0xff139C29),
              ));
        case "wrong":
          return Text(optionDetail.replaceAll(exp, ''),
              style: AppStyles.questionText.copyWith(
                fontWeight: FontWeight.w500,
                color: const Color(0xffFF040F),
              ));
        default:
          return Text(optionDetail.replaceAll(exp, ''),
              style: AppStyles.questionText.copyWith(
                fontWeight: FontWeight.w500,
              ));
      }
    }
  }

  Widget _buildItem(BuildContext context, int index, int test2, int typeFilter) {
    Question question = widget.questions[index];
    bool correct = widget.correctList[index] == true;
    var textAnswer = '';
    var testAns = '';
    late List<Widget> list = <Widget>[];
    var alphabet = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P'];
    RegExp exp = RegExp(r"<[^>]*>", multiLine: true, caseSensitive: true);
    /* if(widget.questions[index].options != null){
      inspect(widget.questions[index].options!.length);
    } */
    //inspect(widget.questions[index].correct);
    /* for (int i = 0; i < widget.questions[index].options!.length; i++) {
      testAns += '<div style="color:red;margin-bottom:10px">' +
          widget.questions[index].options![i].qOption +
          '</div>';
      //inspect(widget.answers);
      //print(widget.questions[index].correctIndex);
      /* print(
          "Cau tra loi " + i.toString() + widget.answersText[index].toString()); */
    } */
    for (int i = 0; i < widget.questions[index].options!.length; i++) {
      bool check = true;
      for (int j = 0; j < widget.questions[index].correctIndex!.length; j++) {
        if (widget.shuffleOptions) {
          final id = widget.questions[index].options?[i].oid;
          final correctIds = widget.questions[index].correct?.split(',');
          if (id == correctIds![j] &&
              widget.answersText[index]
                      .toString()
                      .contains(widget.questions[index].options![i].qOption) ==
                  false) {
            //print('ket qua dung: ' + widget.questions[15].options![i].qOption);
            //Câu đúng nhưng ko đc chọn
            list.add(rowResultAns(
                alphabet[i],
                widget.questions[index].options![i].qOption
                    .replaceAll('&nbsp;', ' ')
                    .replaceAll("&rsquo;", "'"),
                correct,
                widget.questions[index].correct!
                        .split(',')
                        .contains(widget.questions[index].options![i].oid)
                    ? true
                    : false,
                widget.questions[index].type == "single" ? "single" : "multi",
                false));
            testAns +=
                '<div style="color:green;margin-bottom:10px"><p style="font-weight:500"><b>' +
                    alphabet[i] +
                    ". </b>" +
                    widget.questions[index].options![i].qOption
                        .replaceAll('&nbsp;', ' ')
                        .replaceAll("&rsquo;", "'") +
                    '</p></div>';
            check = false;
          }
        } else {
          if (i == widget.questions[index].correctIndex![j] &&
              widget.answersText[index]
                      .toString()
                      .contains(widget.questions[index].options![i].qOption) ==
                  false) {
            //print('ket qua dung: ' + widget.questions[15].options![i].qOption);
            //Câu đúng nhưng ko đc chọn
            list.add(rowResultAns(
                alphabet[i],
                widget.questions[index].options![i].qOption
                    .replaceAll('&nbsp;', ' ')
                    .replaceAll("&rsquo;", "'"),
                correct,
                widget.questions[index].correct!
                        .split(',')
                        .contains(widget.questions[index].options![i].oid)
                    ? true
                    : false,
                widget.questions[index].type == "single" ? "single" : "multi",
                false));
            testAns +=
                '<div style="color:green;margin-bottom:10px"><p style="font-weight:500"><b>' +
                    alphabet[i] +
                    ". </b>" +
                    widget.questions[index].options![i].qOption
                        .replaceAll('&nbsp;', ' ')
                        .replaceAll("&rsquo;", "'") +
                    '</p></div>';
            check = false;
          }
        }
      }
      if (check) {
        if (widget.questions[index].type == "multi"
            ? widget.answersText[index]
                .map((value) => value)
                .toList()
                .contains(widget.questions[index].options![i].qOption)
            : widget.answersText[index].toString() ==
                (widget.questions[index].options![i].qOption)) {
          if (widget.questions[index].correct!
              .split(',')
              .contains(widget.questions[index].options![i].oid)) {
            // Câu đc chọn đúng multi
            list.add(rowResultAns(
                alphabet[i],
                widget.questions[index].options![i].qOption
                    .replaceAll('&nbsp;', ' ')
                    .replaceAll("&rsquo;", "'"),
                correct,
                widget.questions[index].correct!
                        .split(',')
                        .contains(widget.questions[index].options![i].oid)
                    ? true
                    : false,
                widget.questions[index].type == "single" ? "single" : "multi",
                true));
            testAns +=
                '<div style="color:green;margin-bottom:10px"><p style="font-weight:500"><b>' +
                    alphabet[i] +
                    ". </b>" +
                    widget.questions[index].options![i].qOption
                        .replaceAll('&nbsp;', ' ')
                        .replaceAll("&rsquo;", "'") +
                    '</p></div>';
          } else {
            //print('da chon ' + widget.questions[15].options![i].qOption);
            //đã chọn nhưng sai
            list.add(rowResultAns(
                alphabet[i],
                widget.questions[index].options![i].qOption,
                correct,
                widget.questions[index].correct!
                        .split(',')
                        .contains(widget.questions[index].options![i].oid)
                    ? true
                    : false,
                widget.questions[index].type == "single" ? "single" : "multi",
                true));
            testAns += '<div style="color:red;margin-bottom:10px"><p style="font-weight:500"><b>' +
                alphabet[i] +
                ". </b>" +
                widget.questions[index].options![i].qOption
                    .replaceAll('&nbsp;', ' ')
                    .replaceAll("&rsquo;", "'") +
                '</p></div>';
          }
        } else {
          //print('Cac ket qua ' + widget.questions[15].options![i].qOption);
          //Cac cau ko duoc chon
          if (widget.correctList[index] == true) {
            list.add(rowResultAns(
                alphabet[i],
                widget.questions[index].options![i].qOption
                    .replaceAll('&nbsp;', ' ')
                    .replaceAll("&rsquo;", "'"),
                correct,
                widget.questions[index].correct!
                        .split(',')
                        .contains(widget.questions[index].options![i].oid)
                    ? true
                    : false,
                widget.questions[index].type == "single" ? "single" : "multi",
                false));
          } else {
            list.add(rowResultAns(
                alphabet[i],
                widget.questions[index].options![i].qOption
                    .replaceAll('&nbsp;', ' ')
                    .replaceAll("&rsquo;", "'"),
                correct,
                widget.questions[index].correct!
                        .split(',')
                        .contains(widget.questions[index].options![i].oid)
                    ? true
                    : false,
                widget.questions[index].type == "single" ? "single" : "multi",
                false));
          }

          testAns += '<div style="color:black;margin-bottom:10px"><p style="font-weight:500"><b>' +
              alphabet[i] +
              ". </b>" +
              widget.questions[index].options![i].qOption
                  .replaceAll('&nbsp;', ' ')
                  .replaceAll("&rsquo;", "'") +
              '</p></div>';
        }
      }
    }
    if (widget.questions[index].type == "single") {
      textAnswer = '\n' + widget.answersText[index];
    } else {
      widget.answersText[index].forEach((element) {
        textAnswer += '\n- ' + element;
      });
    }
    var textCorrect = '';
    if (widget.questions[index].type == "single") {
      textCorrect = question.options![question.correctIndex![0]].qOption;
    } else {
      question.correctIndex!.forEach((element) {
        if (element == 0) {
          textCorrect += '- ' + question.options![element].qOption;
        } else {
          textCorrect += '\n- ' + question.options![element].qOption;
        }
      });
    }
    bool bookmarked = widget.bookmark[index] == 1;
    bool unanswered = textAnswer.trim() == '';
    var checkType;
    if (test2 == 3)
      checkType = correct;
    else if (test2 == 2)
      checkType = !correct && !unanswered;
    else if (test2 == 1)
      checkType = bookmarked;
    else if (test2 == 0)
      checkType = true;
    else if (test2 == 4) checkType = unanswered;
    if (checkType) {
      final questionText = getQuestionText(question);
      return Card(
        color: correct ? const Color(0xffF9FFFB) : const Color(0xffFFF8F8),
        borderOnForeground: false,
        shadowColor: Colors.white,
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        shape: /* bookmarked
                ? new RoundedRectangleBorder(
                    side: new BorderSide(color: Colors.yellow, width: 2.0),
                    borderRadius: BorderRadius.circular(4.0))
                : */
            correct
                ? new RoundedRectangleBorder(
                    side: new BorderSide(color: AppColors.greenText, width: 1.0),
                    borderRadius: BorderRadius.circular(4.0))
                : new RoundedRectangleBorder(
                    side: new BorderSide(color: AppColors.redAnswerText, width: 1.0),
                    borderRadius: BorderRadius.circular(4.0)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.only(top: 12, left: 12, right: 12),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  bookmarked
                      ? Positioned(
                          left: 0,
                          top: -15,
                          child: SizedBox(
                            width: 16,
                            height: 16,
                            child: Image.asset(
                              'assets/images/mark_question.png',
                              fit: BoxFit.fill,
                            ),
                          ),
                        )
                      : Container(),
                  GestureDetector(
                    onTap: () {
                      String imagePath = extractImagePathFromHtml(questionText);
                      if (imagePath != "") {
                        showImagePopup(context, imagePath);
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(right: 30.0),
                      child: Html(
                        data: (index + 1).toString() + '. ' + questionText,
                        style: {
                          "body": Style(
                              fontFamily: 'Roboto',
                              color: Colors.black,
                              margin: Margins.zero,
                              padding: HtmlPaddings.zero,
                              fontWeight: FontWeight.bold,
                              lineHeight: const LineHeight(1.5)),
                          "img":
                              Style(width: Width(MediaQuery.of(context).size.width - 50, Unit.px))
                        },
                      ),
                    ),
                  ),
                  Positioned(
                    // will be positioned in the top right of the container
                    top: 0,
                    right: 0,
                    child: InkWell(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                        logEvent("exam_result_detail_report_click", {
                          "question_id": widget.questions[index].id,
                          "test_name": widget.quizName
                        });
                        final data = {'debugId': Common.debugId, 'qid': widget.questions[index].id};
                        try {
                          context.loaderOverlay.show();
                          final report = await ApiServices().reportByQuestion(data);
                          context.loaderOverlay.hide();
                          if (report.reportId != null) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => ReportDetail(
                                          report: report,
                                        ))).then((value) {});
                          } else {
                            openDialog(widget.questions[index], widget.quizId, index + 1);
                          }
                        } catch (e) {
                          context.loaderOverlay.hide();
                          showDialog(
                              context: context,
                              builder: (builder) => ErrorDialog(
                                    error: e.toString(),
                                  ));
                        }
                      },
                      child: SizedBox(
                        width: 25,
                        height: 25,
                        child: Image.asset('assets/images/report_icon.png', fit: BoxFit.fill),
                      ),
                    ),
                  )
                ],
              ),
            ),
            Divider(
              color: correct ? const Color(0xff139C29) : const Color(0xffFF9C7D),
              thickness: 1,
              height: 24,
            ),
            // SizedBox(height: 5.0),
            Padding(
              padding: widget.questions[index].description != null &&
                      widget.questions[index].description != ''
                  ? const EdgeInsets.fromLTRB(12, 0, 12, 2)
                  : const EdgeInsets.fromLTRB(12, 0, 12, 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /* Text(AppLocalizations.of(context).answer,
                      style: AppStyles.questionText
                          .copyWith(fontWeight: FontWeight.w600)), */
                  /* Text(
                        textAnswer.trim() == ''
                            ? AppLocalizations.of(context).noAnsSelected
                            : Helper().parseHtmlString(textAnswer),
                        style: correct
                            ? AppStyles.correctAnswerText
                            : AppStyles.incorrectAnswerText,
                      ), */
                  /* Html(data: testAns), */
                  Column(children: [
                    ...list,
                    const SizedBox(
                      height: 10,
                    )
                  ]),
                  /* Html(
                    data: widget.questions[index].description.toString(),
                    style: {"body": Style(fontWeight: FontWeight.bold)},
                  ), */
                  /* Column(
                    children: [
                      TextButton(
                        onPressed: () => _toggleContainer(),
                        child: Text("Toggle container visibility"),
                      ),
                      SizeTransition(
                        sizeFactor: _animation,
                        axis: Axis.vertical,
                        child: Container(
                            child: Column(
                          children: [
                            const MySeparator(color: Colors.grey),
                            SizedBox(height: 8.0),
                            Html(
                              data: widget.questions[index].description
                                  .toString(),
                              style: {
                                "body": Style(fontWeight: FontWeight.bold)
                              },
                            ),
                          ],
                        )),
                      ),
                      Text("This is below the above container"),
                    ],
                  ), */
                  if (widget.questions[index].description != null &&
                      widget.questions[index].description != '') ...{
                    correct
                        ? const MySeparator(color: Color(0xff139C29))
                        : const MySeparator(color: Color(0xffFF9C7D)),
                    TestDescription(
                        description: widget.questions[index].description.toString(),
                        question: widget.questions[index].id.toString(),
                        callback: callback,
                        index: index,
                        callbackSetState: widget.callbackSetState,
                        typeFilter: typeFilter)
                  } else ...{
                    Container()
                  }

                  /* correct
                          ? Container()
                          : Text.rich(
                              TextSpan(children: [
                                TextSpan(
                                    text: AppLocalizations.of(context).rightAns,
                                    style: AppStyles.questionText
                                        .copyWith(fontWeight: FontWeight.w600)),
                                TextSpan(text: "\n"),
                                TextSpan(
                                    text: Helper().parseHtmlString(textCorrect),
                                    style: AppStyles.correctAnswerText)
                              ]),
                            ), */
                ],
              ),
            )
          ],
        ),
      );
    } else {
      return Container();
    }
  }

  String getQuestionText(question) {
    String questionText = question.question;
    if (RegExp(r'^<p><br />.*').hasMatch(questionText)) {
      questionText = questionText.substring(9, questionText.length - 4);
    } else if (RegExp(r'<p>.*?</p>').hasMatch(questionText)) {
      questionText = questionText.substring(3, questionText.length - 4);
    } else if (RegExp(r'^<br>.*').hasMatch(questionText)) {
      questionText = questionText.substring(4);
    }
    return questionText.replaceAll('../../', Common.apiDomain);
  }

  Future openDialog(ques, quizid, num) => showDialog(
      context: context,
      builder: (context) {
        return MyDialog(questions: ques, quizId: quizid, num: num);
      });
}

class MySeparator extends StatelessWidget {
  const MySeparator({Key? key, this.height = 1, this.color = Colors.black}) : super(key: key);
  final double height;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final boxWidth = constraints.constrainWidth();
        const dashWidth = 5.0;
        final dashHeight = height;
        final dashCount = (boxWidth / (2 * dashWidth)).floor();
        return Padding(
          padding: const EdgeInsets.only(left: 3, right: 3),
          child: Flex(
            children: List.generate(dashCount, (_) {
              return SizedBox(
                width: dashWidth,
                height: dashHeight,
                child: DecoratedBox(
                  decoration: BoxDecoration(color: color),
                ),
              );
            }),
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            direction: Axis.horizontal,
          ),
        );
      },
    );
  }
}

class Point {
  int qid;
  bool status;

  Point({required this.qid, required this.status});
}

class TestDescription extends StatefulWidget {
  const TestDescription({
    Key? key,
    required this.description,
    required this.question,
    required this.index,
    required this.callback,
    required this.callbackSetState,
    required this.typeFilter,
  }) : super(key: key);

  final String description;
  final String question;
  final Function callback;
  final Function callbackSetState;
  final int index;
  final int typeFilter;

  @override
  _VariableSizeContainerExampleState createState() => _VariableSizeContainerExampleState();
}

class _VariableSizeContainerExampleState extends State<TestDescription>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool opened = false;
  final keyText = GlobalKey();
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.fastLinearToSlowEaseIn,
    );
  }

  _toggleContainer() {
    logEvent("exam_result_detail_explanation_click", {"question_id": widget.question});

    if (_animation.status != AnimationStatus.completed) {
      _controller.forward();
      setState(() {
        opened = true;
      });
    } else {
      _controller.animateBack(0, duration: const Duration(milliseconds: 300));
      opened = false;
      Future.delayed(const Duration(milliseconds: 300), () {
        setState(() {
          opened = false;
        });
      });
    }
  }

  _onTapDown(TapDownDetails details) {
    var x = details.globalPosition.dx;
    var y = details.globalPosition.dy;
    final RenderBox renderBox = keyText.currentContext?.findRenderObject() as RenderBox;

    final Size size = renderBox.size;

    logEvent("exam_result_detail_explanation_click", {"question_id": widget.question});
    if (Common.premium) {
      if (_animation.status != AnimationStatus.completed) {
        _controller.forward();
        widget.callback(widget.index, (y + size.height).toString(), widget.typeFilter);
        setState(() {
          opened = true;
        });
      } else {
        _controller.animateBack(0, duration: const Duration(milliseconds: 300));
        opened = false;
        Future.delayed(const Duration(milliseconds: 300), () {
          setState(() {
            opened = false;
          });
        });
      }
    } else {
      openPremiumPopup = true;
      setState(() {
        _ignoring = false;
      });
      widget.callbackSetState();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizeTransition(
          sizeFactor: _animation,
          axis: Axis.vertical,
          child: Container(
              key: keyText,
              child: Column(
                children: [
                  Html(
                    data: Helper().parseHtmlString(widget.description),
                    style: {
                      "body": Style(fontWeight: FontWeight.w500, lineHeight: LineHeight.number(1.5))
                    },
                  ),
                ],
              )),
        ),
        /* TextButton(
          style: ButtonStyle(
            overlayColor: MaterialStateProperty.all(Colors.transparent),
          ),
          onPressed: () => _toggleContainer(), */
        GestureDetector(
          onTapDown: (TapDownDetails details) => _onTapDown(details),
          child: opened == false
              ? Padding(
                  padding: const EdgeInsets.symmetric(vertical: 15.0),
                  child: Text(
                    AppLocalizations.of(context).viewExplanation,
                    style: AppStyles.bodyBold.copyWith(color: AppColors.greenPrimary, fontSize: 14),
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.symmetric(vertical: 15.0),
                  child: Text(
                    AppLocalizations.of(context).hide,
                    style: AppStyles.bodyBold.copyWith(color: AppColors.greenPrimary, fontSize: 14),
                  ),
                ),
        ),
      ],
    );
  }
}

bool _ignoring = false;

//Popup
class PremiumPopup2 extends StatefulWidget {
  final String quiz_name;
  final String quiz_result;
  PremiumPopup2({Key? key, required this.quiz_name, required this.quiz_result}) : super(key: key);
  @override
  State<StatefulWidget> createState() {
    return _MyAppState();
  }
}

String device = 'phone';
String getDeviceType() {
  final data = MediaQueryData.fromWindow(WidgetsBinding.instance!.window);
  return data.size.shortestSide < 550 ? 'phone' : 'tablet';
}

class _MyAppState extends State<PremiumPopup2> {
  @override
  void initState() {
    device = getDeviceType();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(0 /* left: 20.0, right: 20, bottom: 20 */),
      child: IgnorePointer(
        ignoring: _ignoring,
        child: AnimatedOpacity(
          opacity: _ignoring ? 0.0 : 1.0,
          duration: const Duration(milliseconds: 500),
          child: Container(
            //height: 260.0,
            width: device == 'phone' ? MediaQuery.of(context).size.width : 600,
            child: Padding(
              padding: const EdgeInsets.only(right: 18.0, left: 18),
              child: GestureDetector(
                onTap: () {
                  logEvent("exam_result_detail_try_now_click",
                      {"test_name": widget.quiz_name, "test_result": widget.quiz_result});
                  Navigator.push(context, MaterialPageRoute(builder: (context) => PaymentPage()));
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.popUpBannerBG,
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                        bottomLeft: Radius.circular(10),
                        bottomRight: Radius.circular(10)),
                    boxShadow: [
                      const BoxShadow(
                        color: Color.fromRGBO(138, 138, 138, 0.25),
                        spreadRadius: 5,
                        blurRadius: 7,
                        offset: Offset(4, -4), // changes position of shadow
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12.0,
                    ),
                    child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 11.0, bottom: 11),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              AppLocalizations.of(context).premiumVersion,
                              style: AppStyles.bodyBold.copyWith(color: Colors.white, fontSize: 16),
                            ),
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  _ignoring = true;
                                });
                              },
                              child: const Icon(
                                Icons.close_outlined,
                                color: Color(0xFF7C9ABC),
                              ),
                            )
                          ],
                        ),
                      ),
                      new Divider(
                        color: const Color(0xff7C9ABC),
                        height: 1,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 12.0, bottom: 12),
                        child: Text(
                          AppLocalizations.of(context).trialTitle,
                          style: AppStyles.bodyBold.copyWith(color: Colors.white, fontSize: 14),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 0.0),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.lens_rounded,
                              color: Color(0xffFEA319),
                              size: 8,
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Text(
                              AppLocalizations.of(context).unlockQR,
                              style: AppStyles.body.copyWith(
                                  color: Colors.white, fontSize: 16, fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 14.0),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.lens_rounded,
                              color: Color(0xffFEA319),
                              size: 8,
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Text(
                              AppLocalizations.of(context).unlockAllExam,
                              style: AppStyles.body.copyWith(
                                  color: Colors.white, fontSize: 16, fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 14.0),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.lens_rounded,
                              color: Color(0xffFEA319),
                              size: 8,
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Text(
                              AppLocalizations.of(context).withExplain,
                              style: AppStyles.body.copyWith(
                                  color: Colors.white, fontSize: 16, fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 14.0),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.lens_rounded,
                              color: Color(0xffFEA319),
                              size: 8,
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Text(
                              AppLocalizations.of(context)
                                  .fullyIUpdated
                                  .replaceAll('%1', DateTime.now().year.toString()),
                              style: AppStyles.body.copyWith(
                                  color: Colors.white, fontSize: 16, fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              AppLocalizations.of(context).upgrade,
                              style: AppStyles.body.copyWith(
                                  color: const Color(0xff00F9D7),
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(
                              width: 6,
                            ),
                            const Padding(
                              padding: EdgeInsets.only(top: 2),
                              child: Icon(
                                Icons.arrow_forward_rounded,
                                color: Color(0xff00F9D7),
                                size: 20,
                              ),
                            )
                          ],
                        ),
                      )
                    ]),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
