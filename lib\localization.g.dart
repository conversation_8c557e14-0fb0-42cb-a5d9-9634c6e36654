// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'localization.dart';

// **************************************************************************
// SheetLocalizationGenerator
// **************************************************************************

final localizedLabels = <Locale, AppLocalizationsData>{
  const Locale.fromSubtags(languageCode: 'en'): const AppLocalizationsData(
    finishEarly:
        'You still have unanswered questions. Are you sure to finish the exam?',
    redeemCodeError: 'Invalid Code',
    introduceDescription:
        '<p><b>Common.appName</b> is your one-stop shop for mastering a wide range of internationally recognized certifications. With our comprehensive tools and resources, you can <b>elevate your knowledge</b> and <b>confidently ace your exams</b>.</p><br><p><b>Embark on a personalized learning journey:</b></p><p><b>• Dive into a sea of practice questions:</b> Explore a vast collection of practice questions, carefully curated by industry experts, to cover all essential syllabus areas.</p><p><b>• Track your progress with precision:</b> Monitor your performance and identify areas for improvement with detailed result summaries and personalized insights.</p><p><b>• Gain in-depth understanding:</b> Delve into clear explanations for every question and answer, ensuring a thorough comprehension of each topic.</p><p><b>• Simulate the real exam experience:</b> Immerse yourself in authentic exam conditions with timed mock exams, replicating the actual testing environment.</p><p><b>• Stay ahead of the curve:</b> Access regularly updated question sets that reflect the latest exam content, ensuring you\'re always prepared.</p><br><p><b>With Common.appName, you have everything you need to conquer your international certification goals.</b></p>',
    introduceTitle:
        'Welcome to Your Gateway to International Certification Success',
    then: 'then',
    emptyExamTips: 'There are no exam tips currently available',
    afterTrial: 'after trial ends',
    trialDescription:
        '- The free trial is available only once.\n- Subscription begins automatically after the free trial ends.\n- You can cancel your subscription in the App Store or Play Store.\n- If the subscription is canceled 24 hours before the end of the current use period, the subscription will end automatically after the use period ends.',
    connectServerError: 'Can\'t connect to server. Please try again later',
    hour: 'Hour',
    hours: 'Hours',
    noReport: 'You didn\'t send any report',
    sendResponseSuccess: 'Send response success',
    sendResponse: 'Send response',
    closeReport: 'Close report',
    scrumpassResponse: 'ScrumPass\'s Response',
    yourResponse: 'Your response',
    reportContent: 'Report Content',
    closed: 'Closed',
    reviewing: 'Reviewing',
    status: 'Status',
    reportedQuestion: 'Reported Question',
    resultText: 'Result',
    listQuizShowcase2:
        'The questions in the test are fixed and do not change, helping you speed up and prepare for the actual exam.',
    listQuizShowcase1:
        'Each time you take a test, you will encounter different questions, which is suitable for your daily practice and review.',
    homeShowcase5: 'Quickly learn theory and glossary with flashcards.',
    homeShowcase4:
        'Helps you review more effectively with a list of questions you got wrong and marked with full explanations.',
    homeShowcase3: 'History of your test results.',
    homeShowcase2: 'List of available tests that you can take.',
    homeShowcase1:
        'Your ability to pass the test is based on your test-taking history. You can click to view your detailed results statistics.',
    descriptionContent:
        'Exam Simulator is a leading developer of mobile apps designed to empower learners to ace their international certification exams. We\'re a passionate team, dedicated to creating a flexible and effective learning platform that caters to your individual needs.\n\nWe\'re passionate about empowering learners to achieve their international certification goals. As a leading developer of mobile apps designed for exam preparation, we understand the unique challenges and aspirations of individuals seeking to advance their careers through professional certifications.\n\nJoin us and unlock a world of personalized learning tools, in-depth explanations, and targeted practice to maximize your knowledge and confidence on exam day.',
    aboutUsContent:
        'ScrumPass is a professional organization specializing in providing mentoring and exam preparation services for Agile certifications such as PSM, PSPO, and PMI-ACP. With highly committed courses, we have helped hundreds of students pass the exams every year. Besides, our consulting services, exam simulator, and Scrum tools have been trusted by many companies and individuals.',
    copyToClipboard: 'Copied to clipboard',
    androidLink:
        'Link to download android app: https://play.google.com/store/apps/details?id=',
    iosLink: 'Link to download ios app: https://apps.apple.com/app/id',
    getCertificate:
        'Getting a [certificate_name] certificate is easier with [app_name].',
    shareApp: 'Share App with your friend',
    tutorialTap: 'Tap to view definition',
    tutorialSwipeRight: 'Swipe right to next word',
    tutorialSwipeLeft: 'Swipe left to skip word',
    flashcardEndText:
        'You just learned {e1} terms! Keep practicing to master the remaining {e2}.',
    learnedEverything: 'You\'ve learned everything',
    halfWayThere: 'You\'re halfway there!',
    congratulations: 'Congratulations!',
    continueStudy: 'Continue study',
    restart: 'Restart',
    restartFlashCardPopup: 'Are you sure want to restart flashcard?',
    favoriteWords: 'Favorite Words',
    score: 'Score',
    testNumber: 'Number of Exams',
    disclamer:
        'The Exam Pass Rate is calculated based on your exam results and the data in the system. The index is for reference only.',
    goodToGo: 'Good to go!!! You’re totally ready to take part in the exam.',
    keepGoing: 'Keep going. You’re pretty ready to take part in exam.',
    notEnough:
        'You’ve not finished at least 10 exams yet. To obtain the most accurate pass rate, kindly take some additional exams.',
    examPassRateDetail: 'Statistics',
    noData: 'No Data',
    timeDetail: 'Time Details',
    questionDetail: 'Question Details',
    eachQuestion: 'Each Question',
    totalTime: 'Total Time',
    questionDone: 'Question Done',
    streak: 'Streak Passed Exam',
    latestScore: 'Latest Score',
    passedExam: 'Passed Exam',
    quizDone: 'Exam Done',
    avgScore: 'Avg. Score',
    quizFlashCardDetail: 'Exam Details',
    flashCard: 'Flashcard',
    notReady:
        'You’re not ready. Please spend more time to train for better result.',
    examPassRate: 'Exam Pass Rate',
    emptyField: 'Field can\'t be empty',
    notificationDetail: 'Come back and do your exam',
    notification: 'Notification',
    questionrule: 'The questions in the exam are fixed',
    randomRule: 'The questions are different across the exams',
    getSupport: 'Get Support from Us',
    premiumVersion: 'Premium Version',
    someTips4U: 'SOME TIPS FOR YOU',
    yourCode: 'Your code',
    enterRedeemCode: 'Please enter your redeem code',
    redeemCode: 'Redeem Code',
    updateYear: 'updated for %1',
    fully: 'Fully',
    detailedExplain: 'detailed explanations',
    answersWith: 'Answers with',
    allTheExams: 'all the exams',
    unlock: 'Unlock',
    startTrial: 'Start 3-Day Free Trial',
    cancelAnyTime:
        'Start your 3 day free trial, then {xxx}/{yyy}. Cancel your subscription at any time.',
    fullyIUpdated: 'Fully updated for %1',
    trialTitle: '3-Day Trial with unlimited access:',
    sunday: 'Sunday',
    saturday: 'Saturday',
    friday: 'Friday',
    thursday: 'Thursday',
    wednesday: 'Wednesday',
    tuesday: 'Tuesday',
    monday: 'Monday',
    notificationStatus: 'Study Reminder',
    notificationDays: 'Reminder Days',
    notificationTime: 'Reminder Time',
    expiresToday: 'Expires today',
    trial: 'Trial',
    dayLeft: 'day left',
    otherApp: 'Other Apps',
    randomSet: 'Random set',
    questionSet: 'Question set',
    doing: 'Doing',
    premium: 'Premium',
    free: 'Free',
    resultDetail: 'Detail',
    version: 'Version',
    fail: 'Fail',
    pass: 'Pass',
    secondShort: 'secs',
    term: 'Terms',
    support: 'Support',
    privacyPolicy: 'Privacy Policy',
    termOfUse: 'Terms of Use',
    percentageRequireToPass: 'Passing Score',
    plan: 'Plan',
    resetData: 'Reset Data',
    upgradeToGetFeature: '3-Day Free Trial with Premium version',
    deleteSuccess: 'Successfully deleted',
    confirmDeleteQuestionReview:
        'This action will erase all your app data. You will need to start over. Do you wish to proceed?',
    upgrade: 'Try Now',
    unlockAllExams: 'Unlock all the exams',
    unlockQR: 'Unlock feature Review Question',
    premiumFeature: 'Premium Feature',
    hide: 'Hide',
    delete: 'Delete',
    viewExplanation: 'View Explanation',
    confirmDeleteQuestion:
        'Are you sure want to delete all selected questions?',
    yes: 'Yes',
    restorePurchase: 'Restore purchase',
    thankForPurchase: 'You have unlocked premium feature',
    purchase: 'Purchase',
    alreadyPaid: 'Already Paid?',
    month: 'Month',
    months: 'Months',
    total: 'Total',
    monthly: 'Monthly',
    withExplain: 'Answers with detailed explanations',
    unlockAllExam: 'Unlock all the exams',
    unlockFeature: 'Unlock feature',
    accessPremium: 'Pass The Exam Easily',
    statistics: 'Statistics',
    noMarkedQuestion: 'You don\'t have any marked question',
    noWrongQuestion: 'You don\'t have any wrong question',
    questionReview: 'Review Question',
    numberWrong: 'Wrong: {e} Times',
    markedQuestion: 'Marked Question',
    wrongQuestion: 'Wrong Question',
    unanswered: 'Not Answered',
    wrong: 'Wrong',
    correct: 'Correct',
    totalQuestion: 'Total: {e} questions',
    yourName: 'Your Name',
    retryInternet: 'Retry',
    viewExamList: 'View Exam List',
    minutesShort: 'mins',
    left: 'left',
    numberQuestion: 'questions',
    reportDetail: 'Report details',
    numReport: 'Question number',
    cancel: 'Cancel',
    send: 'Send',
    reportSuccess: 'Your report has been sent.',
    report: 'Report',
    notAnswered: 'Not Answered',
    forceUpdateDialogContent:
        'New update is available. You need to update this application to continue.',
    rateUs: 'Rate Us',
    updateDialogContent: 'New update is available. Do you want to update?',
    noInternet: 'No Internet Connection',
    totalQuizDone: 'Taken Exams',
    recentScore: 'Last Exam Score',
    highScore: 'Highest Consecutive Passes',
    fontExam: 'Exam Font Size',
    averageExamResult: 'Average Exam Score',
    serviceSatisfactionRate: 'Service Satisfaction Rate',
    certificationPassRate: 'Certification Pass Rate',
    yourMessage: 'Let us know how we can assist you better?',
    writeUsDirectly: 'Write Us Directly',
    contact: 'Contact us',
    callUs: 'Call Us',
    connectUs: 'Connect Us',
    quizNumber: 'This is your exam number {e}',
    confirmRetry: 'Are you ready to retry?',
    noUserName: 'Please enter your name',
    noQuizResult: 'You didn\'t complete any exam',
    noInfo: 'No Infomation',
    errorDialog: 'An error occurred, please try again later',
    error: 'Error',
    confirmDoQuiz: 'Are you ready to do the exam?',
    resumeQuizDialog: 'You didn\'t finish an exam, still want to continue?',
    bookmarked: 'Bookmarked',
    questionList: 'Question List',
    allQuestion: 'All Questions',
    previous: 'Previous',
    finish: 'Finish',
    next: 'Next',
    questionOf: 'of',
    question: 'Question',
    timeUp: 'Time is over',
    remainTime: 'Remaining Time',
    quitQuizDialog: 'Are you sure to quit the exam?',
    timeUpDialog: 'Time is up',
    no: 'No',
    confirmEndQuiz: 'Are you sure to finish the exam?',
    mustSelectAnswer: 'Please select all that apply',
    attention: 'Attention!',
    productOfScrumPass: 'A product of ScrumPass',
    wellcomeEnterName: 'Please enter your name to start',
    allAns: 'All Answers',
    save: 'Save',
    noLimit: 'No Limit',
    answered: 'Answered',
    noAnsSelected: 'No answer selected',
    continueQ: 'Continue',
    time: 'Time',
    back: 'Back',
    intruction: 'Instruction',
    minPercentage: 'Passing score',
    timeEnd: 'End time',
    timeStart: 'Start time',
    quizDetail: 'Exam Details',
    userDetail: 'Setting',
    noQuizAvaliable: 'No exam avaliable',
    minutes: 'minutes',
    timeLeft: 'Time left',
    duration: 'Duration',
    numOfQuestion: 'Questions',
    begin: 'Take Exam',
    answer: 'Answer',
    retry: 'Retry',
    home: 'Home Screen',
    wrongAns: 'Wrong Answers',
    rightAns: 'Correct Answer',
    marked: 'Marked Questions',
    finishDate: 'Date',
    numHasDone: 'Number of tries',
    numWrongAns: 'Number of wrong answers',
    numRightAns: 'Number of correct answers',
    passRequirement: 'Need {e}% correct to pass',
    detail: 'Account Details',
    resultList: 'Result List',
    quizList: 'Exam List',
    aboutUs: 'About Us',
    scrumguide: 'Scrum Guide',
    result: 'Exam Result',
    examPractice: 'Take Exam',
    numFailQuiz: 'Failed',
    numPassQuiz: 'Passed',
    passPercentage: 'Pass percentage',
    chooseLang: 'Choose Language',
    lang: 'Language',
    name: 'Name',
    hello: 'Hello',
    wellcomeTitlePrince2: 'Welcome to Prince2 Foundation Exam Simulator',
    wellcomeTitleCissp: 'Welcome to CISSP Exam Simulator',
    wellcomeTitleComptiaLinux: 'Welcome to CompTIA Linux+ Exam Simulator',
    wellcomeTitleComptiaCloud: 'Welcome to CompTIA Cloud+ Exam Simulator',
    wellcomeTitleComptiaProject: 'Welcome to CompTIA Project+ Exam Simulator',
    wellcomeTitleComptiaServer: 'Welcome to CompTIA Server+ Exam Simulator',
    wellcomeTitleSAFePOPM: 'Welcome to SAFe POPM Exam Simulator',
    wellcomeTitleSAFeSSM: 'Welcome to SAFe SSM Exam Simulator',
    wellcomeTitleComptiaSecurity: 'Welcome to CompTIA Security+ Exam Simulator',
    wellcomeTitleComptiaNetwork: 'Welcome to CompTIA Network+ Exam Simulator',
    wellcomeTitleComptiaITF: 'Welcome to CompTIA ITF+ Exam Simulator',
    wellcomeTitleComptiaA: 'Welcome to AWS CompTIA A+ Exam Simulator',
    wellcomeTitleAwsMlsIos: 'Welcome to Machine Learning Exam Simulator',
    wellcomeTitleAwsMls:
        'Welcome to AWS Machine Learning Specialty Exam Simulator',
    wellcomeTitleAwsSap:
        'Welcome to AWS Solutions Architect Pro Exam Simulator',
    wellcomeTitleAwsDop: 'Welcome to AWS DevOps Pro Exam Simulator',
    wellcomeTitleItil: 'Welcome to ITIL Foundation Exam Simulator',
    wellcomeTitleAwsDva: 'Welcome to AWS Developer Exam Simulator',
    wellcomeTitleAwsClf: 'Welcome to AWS Cloud Exam Simulator',
    wellcomeTitleAwsSoa: 'Welcome to AWS SysOps Exam Simulator',
    wellcomeTitleRmp: 'Welcome to PMI-RMP Exam Simulator',
    wellcomeTitleAwsSaa: 'Welcome to AWS-SAA Exam Simulator',
    wellcomeTitlePba: 'Welcome to PMI-PBA Exam Simulator',
    wellcomeTitlePfmp: 'Welcome to PfMP Exam Simulator',
    wellcomeTitleEcba: 'Welcome to ECBA Exam Simulator',
    wellcomeTitleISTQB: 'Welcome to ISTQB Exam Simulator',
    wellcomeTitlePgmp: 'Welcome to PgMP Exam Simulator',
    wellcomeTitleCapm: 'Welcome to CAPM Exam Simulator',
    wellcomeTitleCbap: 'Welcome to CBAP Exam Simulator',
    wellcomeTitleCCBA: 'Welcome to CCBA Exam Simulator',
    wellcomeTitlePmp: 'Welcome to PMP Exam Simulator',
    wellcomeTitlePsd: 'Welcome to PSD Exam Simulator',
    wellcomeTitleAcp: 'Welcome to ACP Exam Simulator',
    wellcomeTitlePsk: 'Welcome to PSK Exam Simulator',
    wellcomeTitleSps: 'Welcome to SPS Exam Simulator',
    wellcomeTitlePal: 'Welcome to PAL-I Exam Simulator',
    wellcomeTitlePsm2: 'Welcome to PSM-II Exam Simulator',
    wellcomeTitlePspo2: 'Welcome to PSPO-II Exam Simulator',
    wellcomeTitlePspo: 'Welcome to PSPO Exam Simulator',
    wellcomeTitle: 'Welcome to PSM Exam Simulator',
    appDescriptionPrince2:
        'Simulation test to get prepared for your PRINCE2® 7 Foundation exam. Use this app daily to improve your knowledge.',
    appDescriptionCISSP:
        'Simulation test to get prepared for your CISSP exam. Use this app daily to improve your knowledge.',
    appDescriptionComptiaLinux:
        'Simulation test to get prepared for your CompTIA Linux+ exam. Use this app daily to improve your knowledge.',
    appDescriptionComptiaCloud:
        'Simulation test to get prepared for your CompTIA Cloud Essentials+ exam. Use this app daily to improve your knowledge.',
    appDescriptionComptiaProject:
        'Simulation test to get prepared for your CompTIA Project+ exam. Use this app daily to improve your knowledge.',
    appDescriptionComptiaServer:
        'Simulation test to get prepared for your CompTIA Server+ exam. Use this app daily to improve your knowledge.',
    appDescriptionSAFePOPM:
        'Simulation test to get prepared for your SAFe POPM exam. Use this app daily to improve your knowledge.',
    appDescriptionSAFeSSM:
        'Simulation test to get prepared for your SAFe SSM exam. Use this app daily to improve your knowledge.',
    appDescriptionComptiaSecurity:
        'Simulation test to get prepared for your CompTIA Security+ exam. Use this app daily to improve your knowledge.',
    appDescriptionComptiaNetwork:
        'Simulation test to get prepared for your CompTIA Network+ exam. Use this app daily to improve your knowledge.',
    appDescriptionComptiaITF:
        'Simulation test to get prepared for your CompTIA ITF+ exam. Use this app daily to improve your knowledge.',
    appDescriptionComptiaA:
        'Simulation test to get prepared for your CompTIA A+ exam. Use this app daily to improve your knowledge.',
    appDescriptionAwsMls:
        'Simulation test to get prepared for your AWS Machine Learning Specialty Exam. Use this app daily to improve your knowledge.',
    appDescriptionAwsSap:
        'Simulation test to get prepared for your AWS Solution Architect Pro exam. Use this app daily to improve your knowledge.',
    appDescriptionAwsDop:
        'Simulation test to get prepared for your AWS DevOps Pro exam. Use this app daily to improve your knowledge.',
    appDescriptionItil:
        'Simulation test to get prepared for your ITIL Foundation exam. Use this app daily to improve your knowledge.',
    appDescriptionAwsDva:
        'Simulation test to get prepared for your AWS Developer exam. Use this app daily to improve your knowledge.',
    appDescriptionAwsClf:
        'Simulation test to get prepared for your AWS Cloud exam. Use this app daily to improve your knowledge.',
    appDescriptionAwsSoa:
        'Simulation test to get prepared for your AWS SysOps exam. Use this app daily to improve your knowledge.',
    appDescriptionRmp:
        'Simulation test to get prepared for your PMI-RMP exam. Use this app daily to improve your knowledge.',
    appDescriptionAwsSaa:
        'Simulation test to get prepared  for your AWS-SAA exam. Use this app daily to improve your knowledge.',
    appDescriptionPba:
        'Simulation test to get prepared for your PMI-PBA exam. Use this app daily to improve your knowledge.',
    appDescriptionPfmp:
        'Simulation test to get prepared for your PfMP exam. Use this app daily to improve your knowledge.',
    appDescriptionEcba:
        'Simulation test to get prepared for your ECBA exam. Use this app daily to improve your knowledge.',
    appDescriptionISTQB:
        'Simulation test to get prepared for your ISTQB Foundation exam. Use this app daily to improve your knowledge.',
    appDescriptionPgmp:
        'Simulation test to prepare for your PgMP exam. Use this app daily to improve your knowledge.',
    appDescriptionCapm:
        'Simulation test to prepare for your CAPM exam. Use this app daily to improve your knowledge.',
    appDescriptionCbap:
        'Simulation test to prepare for your CBAP exam. Use this app daily to improve your knowledge.',
    appDescriptionCCBA:
        'Simulation test to get prepared for your CCBA exam. Use this app daily to improve your knowledge.',
    appDescriptionPmp:
        'Simulation test to prepare for your PMI-PMP exam. Use this app daily to improve your knowledge.',
    appDescriptionPsd:
        'Simulation test to prepare for your PSD exam. Use this app daily to improve your knowledge.',
    appDescriptionAcp:
        'Simulation test to prepare for your PMI-ACP exam. Use this app daily to improve your knowledge.',
    appDescriptionPsk:
        'Simulation test to prepare for your PSK I exam. Use this app daily to improve your knowledge.',
    appDescriptionSps:
        'Simulation tests to prepare for your SPS exam. Use this app daily to improve your knowledge.',
    appDescriptionPal:
        'Simulation tests to prepare for your PAL-I exam. Use this app daily to improve your knowledge.',
    appDescriptionPspo2:
        'Simulation tests to prepare for your PSPO-II exam. Use this app daily to improve your knowledge.',
    appDescriptionPspo:
        'Simulation tests to prepare for your PSPO exam. Use this app daily to improve your knowledge.',
    appDescriptionPsm2:
        'Simulation tests to prepare for your PSM-II exam. Use this app daily to improve your knowledge.',
    appDescription:
        'Simulation tests to prepare for your PSM exam. Use this app daily to improve your knowledge.',
    appTitlePrince2: 'PRINCE2® 7 Foundation Exam Simulator',
    appTitleCissp: 'CISSP Exam Simulator',
    appTitleComptiaLinux: 'CompTIA Linux+ Exam Simulator',
    appTitleComptiaCloud: 'CompTIA Cloud Essentials+ Exam Simulator',
    appTitleComptiaProject: 'CompTIA Project+ Exam Simulator',
    appTitleComptiaServer: 'CompTIA Server+ Exam Simulator',
    appTitleSAFePOPM: 'SAFe® Product Owner/Product Manager Exam Simulator',
    appTitleSAFeSSM: 'SAFe® Scrum Master Exam Simulator',
    appTitleComptiaSecurity: 'CompTIA Security+ Exam Simulator',
    appTitleComptiaNetwork: 'CompTIA Network+ Exam Simulator',
    appTitleComptiaITF: 'CompTIA ITF+ Exam Simulator',
    appTitleComptiaA: 'CompTIA A+ Exam Simulator',
    appTitleAwsMls: 'AWS Certified Machine Learning - Specialty Exam Simulator',
    appTitleAwsSap:
        'AWS Certified Solution Architect - Professional Exam Simulator',
    appTitleAwsDop:
        'AWS Certified DevOps Engineer - Professional Exam Simulator',
    appTitleItil:
        'Information Technology Infrastructure Library ITIL Foundation Exam Simulator',
    appTitleAwsDva:
        'AWS Certified Developer Associate (AWS Developer) Certification Exam Simulator',
    appTitleAwsClf:
        'AWS Certified Cloud Practitioner (AWS Cloud) Certification Exam Simulator',
    appTitleAwsSoa:
        'AWS Certified Sysops Administrator - Associate (AWS SysOps) Certification Exam Simulator',
    appTitleRmp:
        'PMI Risk Management Professional (PMI RMP) Certification Exam Simulator',
    appTitleAwsSaa:
        'AWS Certified Solutions Architect - Associate SAA-C03 Exam Simulator',
    appTitlePba:
        'PMI Professional in Business Analysis (PMI-PBA) Certification Exam Simulator',
    appTitlePfmp:
        'Portfolio Management Professional (PfMP) Certification Exam Simulator',
    appTitleEcba:
        'Entry Certificate in Business Analysis (ECBA) Exam Simulator',
    appTitleISTQB:
        'International Software Testing Qualifications Board (ISTQB) Foundation Level',
    appTitlePgmp:
        'Program Management Professional (PgMP) Certification Exam Simulator',
    appTitleCapm:
        'Certified Associate in Project Management (CAPM) Certification Exam Simulator',
    appTitleCbap:
        'Certified Business Analysis Professional (CBAP) Certification Exam Simulator',
    appTitleCCBA:
        'Certification of Capability in Business Analysis (CCBA) Exam Simulator',
    appTitlePmp:
        'Project Management Professional (PMI-PMP) Certification Exam Simulator',
    appTitlePsd:
        'Professional Scrum Developer (PSD) Certification Exam Simulator',
    appTitleAcp:
        'PMI Agile Certified Practitioner (PMI-ACP) Certification Exam Simulator',
    appTitlePsk:
        'Professional Scrum with Kanban (PSK I) Certification Exam Simulator',
    appTitleSps: 'Scaled Professional Scrum (SPS) Certification Exam Simulator',
    appTitlePal:
        'Professional Agile Leadership (PAL-I) Certification Exam Simulator',
    appTitlePspo2:
        'Professional Scrum Product Owner II (PSPO-II) Certification Exam Simulator',
    appTitlePspo:
        'Professional Scrum Product Owner (PSPO) Certification Exam Simulator',
    appTitlePsm2:
        'Professional Scrum Master II (PSM-II) Certification Exam Simulator',
    appTitle: 'Professional Scrum Master (PSM) Certification Exam Simulator',
    appNamePrince2: 'Prince2 Foundation Exam Simulator',
    appNameCissp: 'CISSP Exam Simulator',
    appNameComptiaLinux: 'CompTIA Linux+ Exam Simulator',
    appNameComptiaCloud: 'CompTIA Cloud Essentials+ Exam Simulator',
    appNameComptiaProject: 'CompTIA Project+ Exam Simulator',
    appNameComptiaServer: 'CompTIA Server+ Exam Simulator',
    appNameSAFePOPM: 'SAFe POPM Exam Simulator',
    appNameSAFeSSM: 'SAFe SSM Exam Simulator',
    appNameComptiaSecurity: 'CompTIA Security+ Exam Simulator',
    appNameComptiaNetwork: 'CompTIA Network+ Exam Simulator',
    appNameComptiaITF: 'CompTIA ITF+ Exam Simulator',
    appNameComptiaA: 'CompTIA A+ Exam Simulator',
    appNameAwsMls: 'AWS Machine Learning Specialty Exam Simulator',
    appNameAwsSap: 'AWS Solutions Architect Pro Exam Simulator',
    appNameAwsDop: 'AWS DevOps Pro Exam Simulator',
    appNameItil: 'ITIL Foundation Exam Simulator',
    appNameAwsDva: 'AWS Developer Exam Simulator',
    appNameAwsClf: 'AWS Cloud Exam Simulator',
    appNameAwsSoa: 'AWS SysOps Exam Simulator',
    appNameRmp: 'PMI-RMP Exam Simulator',
    appNameAwsSaa: 'AWS Solutions Architect Exam Simulator',
    appNamePba: 'PMI-PBA Exam Simulator',
    appNamePfmp: 'PfMP Exam Simulator',
    appNameEcba: 'ECBA Exam Simulator',
    appNameISTQB: 'ISTQB Exam Simulator',
    appNamePgmp: 'PgMP Exam Simulator',
    appNameCapm: 'CAPM Exam Simulator',
    appNameCbap: 'CBAP Exam Simulator',
    appNameCCBA: 'CCBA Exam Simulator',
    appNamePmp: 'PMP Exam Simulator',
    appNamePsd: 'PSD Exam Simulator',
    appNameAcp: 'ACP Exam Simulator',
    appNamePsk: 'PSK Exam Simulator',
    appNameSps: 'SPS Exam Simulator',
    appNamePal: 'PAL-I Exam Simulator',
    appNamePspo2: 'PSPO-II Exam Simulator',
    appNamePsm2: 'PSM-II Exam Simulator',
    appNamePspo: 'PSPO Exam Simulator',
    appName: 'PSM Exam Simulator',
  ),
  const Locale.fromSubtags(languageCode: 'vi'): const AppLocalizationsData(
    finishEarly:
        'Bạn vẫn có những câu hỏi chưa có đáp án. Bạn có chắc muốn kết thúc bài kiểm tra?',
    redeemCodeError: 'Mã không hợp lệ',
    introduceDescription:
        '<p><b>Common.appName</b> là nơi lý tưởng để bạn nắm vững nhiều loại chứng chỉ được quốc tế công nhận. Với các công cụ và tài nguyên toàn diện của chúng tôi, bạn có thể <b>nâng cao kiến ​​thức</b> và <b>tự tin đạt thành tích cao trong các kỳ thi</b>.</p><br><p><b>Bắt tay vào quá trình học tập được cá nhân hóa hành trình:</b></p><p><b>• Đi sâu vào biển câu hỏi thực hành:</b> Khám phá bộ sưu tập lớn các câu hỏi thực hành được các chuyên gia trong ngành tuyển chọn cẩn thận để bao quát tất cả các lĩnh vực giáo trình thiết yếu. </p><p><b>• Theo dõi tiến trình của bạn một cách chính xác:</b> Theo dõi hiệu suất của bạn và xác định các lĩnh vực cần cải thiện bằng bản tóm tắt kết quả chi tiết và thông tin chi tiết được cá nhân hóa.</p><p><b>•  Đạt được lợi ích hiểu sâu:</b> Đi sâu vào giải thích rõ ràng cho từng câu hỏi và trả lời, đảm bảo hiểu sâu sắc từng chủ đề.</p><p><b>• Mô phỏng trải nghiệm thi thật:</b> Đắm chìm trong điều kiện thi đích thực với các bài thi thử được tính giờ, mô phỏng môi trường thi thực tế.</p><p><b>•  Luôn dẫn đầu xu hướng:</b> Truy cập các bộ câu hỏi được cập nhật thường xuyên phản ánh nội dung bài thi mới nhất, đảm bảo bạn luôn được chuẩn bị sẵn sàng.</p><br><p><b>Với Common.appName, bạn có mọi thứ cần thiết để chinh phục các mục tiêu chứng nhận quốc tế của mình.</b></p>',
    introduceTitle: 'Chào mừng bạn đến với Cổng chứng chỉ quốc tế của bạn',
    then: 'sau đó',
    emptyExamTips: 'Chưa có exam tips nào hiện tại',
    afterTrial: 'sau dùng thử',
    trialDescription:
        '- Bản dùng thử miễn phí chỉ được cung cấp một lần.\n- Đăng ký sẽ tự động bắt đầu sau khi thời gian dùng thử miễn phí kết thúc.\n- Bạn có thể hủy đăng ký của mình trong App Store hoặc Play Store.\n- Nếu đăng ký bị hủy 24 giờ trước khi kết thúc của thời gian sử dụng hiện tại, đăng ký sẽ tự động kết thúc sau khi thời gian sử dụng kết thúc.',
    connectServerError: 'Không thể kết nối đến máy chủ. Vui lòng thử lại sau',
    hour: 'Giờ',
    hours: 'Giờ',
    noReport: 'Bạn chưa gửi báo lỗi nào',
    sendResponseSuccess: 'Gửi phản hồi thành công',
    sendResponse: 'Gửi phản hồi',
    closeReport: 'Đóng báo lỗi',
    scrumpassResponse: 'Phản hồi của ScrumPass',
    yourResponse: 'Phản hồi của bạn',
    reportContent: 'Nội dung báo lỗi',
    closed: 'Đã đóng',
    reviewing: 'Đang xử lý',
    status: 'Trạng thái',
    reportedQuestion: 'Câu hỏi báo lỗi',
    resultText: 'Kết quả',
    listQuizShowcase2:
        'Các câu hỏi trong bài test là cố định, không thay đổi. Giúp bạn tăng tốc, chuẩn bị trước lúc thi thật.',
    listQuizShowcase1:
        'Mỗi lần làm bài bạn sẽ gặp những câu hỏi khác nhau. Phù hợp để bạn luyện tập, ôn tập hàng ngày.',
    homeShowcase5: 'Học lý thuyết và từ vựng nhanh chóng với flashcard.',
    homeShowcase4:
        'Giúp bạn ôn tập hiệu quả hơn với danh sách các câu bạn làm sai và  đánh dấu với đầy đủ giải thích.',
    homeShowcase3: 'Lịch sử kết quả làm các bài test của bạn.',
    homeShowcase2: 'Danh sách các bài test sẵn sàng bạn có thể làm.',
    homeShowcase1:
        'Khả năng pass kỳ thi của bạn dựa theo lịch sử làm bài của bạn. Bạn có thể bấm vào để xem chi tiết thống kê kết quả của bạn.',
    descriptionContent:
        'Exam Simulator là nhà phát triển ứng dụng di động hàng đầu được thiết kế để hỗ trợ người học đạt thành tích cao trong các kỳ thi chứng chỉ quốc tế. Chúng tôi là một nhóm đầy nhiệt huyết, tận tâm tạo ra một nền tảng học tập linh hoạt và hiệu quả đáp ứng nhu cầu cá nhân của bạn.\n\nChúng tôi rất mong muốn trao quyền cho người học để đạt được mục tiêu chứng nhận quốc tế của họ. Là nhà phát triển ứng dụng di động hàng đầu được thiết kế để luyện thi, chúng tôi hiểu những thách thức và nguyện vọng đặc biệt của những cá nhân muốn thăng tiến nghề nghiệp thông qua các chứng chỉ chuyên môn.\n\nHãy tham gia cùng chúng tôi và khám phá thế giới các công cụ học tập được cá nhân hóa, những giải thích chuyên sâu và thực hành có mục tiêu để tối đa hóa kiến ​​thức và sự tự tin của bạn trong ngày thi.',
    aboutUsContent:
        'ScrumPass là một tổ chức chuyên nghiệp chuyên cung cấp dịch vụ cố vấn và luyện thi các chứng chỉ Agile như PSM, PSPO, PMI-ACP. Với các khóa học được cam kết cao, chúng tôi đã giúp hàng trăm học viên vượt qua các kỳ thi mỗi năm. Bên cạnh đó, các dịch vụ tư vấn, mô phỏng bài thi, công cụ dành cho Scrum Master của chúng tôi đã được nhiều công ty và cá nhân tin tưởng.',
    copyToClipboard: 'Đã sao chép vào clipboard',
    androidLink:
        'Link download app android: https://play.google.com/store/apps/details?id=',
    iosLink: 'Link download app ios: https://apps.apple.com/app/id',
    getCertificate:
        'Lấy chứng chỉ [certificate_name] dễ dàng hơn với [app_name].',
    shareApp: 'Chia sẻ ứng dụng với bạn bè của bạn',
    tutorialTap: 'Nhấn để xem định nghĩa',
    tutorialSwipeRight: 'Vuốt sang phải để chuyển sang từ tiếp theo',
    tutorialSwipeLeft: 'Vuốt sang trái để bỏ qua từ',
    flashcardEndText:
        'Bạn đã học {e1} từ! Hãy tiếp tục để nắm vững {e2} từ còn lại.',
    learnedEverything: 'Bạn đã học được mọi thứ',
    halfWayThere: 'Bạn đã được nửa chặng đường!',
    congratulations: 'Chúc mừng!',
    continueStudy: 'Tiếp tục học',
    restart: 'Bắt đầu lại',
    restartFlashCardPopup: 'Bạn chắc chắn muốn bắt đầu lại flashcard?',
    favoriteWords: 'Từ yêu thích',
    score: 'Điểm',
    testNumber: 'Số lần làm bài',
    disclamer:
        'Tỷ lệ vượt qua kỳ thi được tính dựa trên kết quả kiểm tra của bạn và dữ liệu trong hệ thống. Chỉ số này chỉ mang tính chất tham khảo',
    goodToGo: 'Rất tốt!! Bạn đã hoàn toàn sẵn sàng tham gia kỳ thi.',
    keepGoing: 'Tiếp tục đi. Bạn đã khá sẵn sàng để tham gia kỳ thi.',
    notEnough:
        'Bạn chưa hoàn thành ít nhất 10 bài kiểm tra. Để có được tỷ lệ đậu chính xác nhất, vui lòng thực hiện một số bài kiểm tra bổ sung.',
    examPassRateDetail: 'Thống kê',
    noData: 'Không có dữ liệu',
    timeDetail: 'Chi tiết thời gian',
    questionDetail: 'Chi tiết câu hỏi',
    eachQuestion: 'Mỗi câu hỏi',
    totalTime: 'Tổng thời gian',
    questionDone: 'Câu hỏi đã làm',
    streak: 'Chuỗi bài kiểm tra đã đạt',
    latestScore: 'Điểm số gần nhất',
    passedExam: 'Bài kiểm tra đã vượt qua ',
    quizDone: 'Bài kiểm tra đã làm',
    avgScore: 'Điểm trung bình',
    quizFlashCardDetail: 'Chi tiết bài kiểm tra',
    flashCard: 'Flashcard',
    notReady:
        'Bạn chưa sẵn sàng. Hãy dành nhiều thời gian hơn để luyện tập để có kết quả tốt hơn.',
    examPassRate: 'Tỷ lệ vượt qua kỳ thi',
    emptyField: 'Thông tin không được để trống',
    notificationDetail: 'Hãy quay lại và làm bài kiểm tra của bạn',
    notification: 'Thông báo',
    questionrule: 'Các câu hỏi trong bài kiểm tra là cố định',
    randomRule: 'Các câu hỏi là khác nhau qua các lần làm bài',
    getSupport: 'Nhận hỗ trợ từ chúng tôi',
    premiumVersion: 'Phiên bản Premium',
    someTips4U: 'Một vài tips cho bạn',
    yourCode: 'Mã của bạn',
    enterRedeemCode: 'Vui lòng nhập mã đổi quà của bạn',
    redeemCode: 'Đổi mã',
    updateYear: 'mới nhất cho năm %1',
    fully: 'Cập nhật',
    detailedExplain: 'kèm lời giải chi tiết',
    answersWith: 'Đáp án',
    allTheExams: 'tất cả bài kiểm tra',
    unlock: 'Mở khoá',
    startTrial: 'Bắt đầu dùng thử miễn phí 3 ngày',
    cancelAnyTime:
        'Dùng thử 3 ngày miễn phí sau đó gói đăng ký {xxx}/{yyy} sẽ tự động bắt đầu. Có thể huỷ đăng ký bất cứ lúc nào.',
    fullyIUpdated: 'Cập nhật đầy đủ cho năm %1',
    trialTitle: '3-Ngày dùng thử với các tính năng: ',
    sunday: 'Chủ nhật',
    saturday: 'Thứ bảy',
    friday: 'Thứ sáu',
    thursday: 'Thứ năm',
    wednesday: 'Thứ tư',
    tuesday: 'Thứ ba',
    monday: 'Thứ hai',
    notificationStatus: 'Nhắc nhở học tập',
    notificationDays: 'Thông báo trong các ngày',
    notificationTime: 'Thời gian thông báo',
    expiresToday: 'Hết hạn hôm nay',
    trial: 'Dùng thử',
    dayLeft: 'ngày còn lại',
    otherApp: 'App liên quan',
    randomSet: 'Câu hỏi ngẫu nhiên',
    questionSet: 'Câu hỏi cố định',
    doing: 'Đang làm',
    premium: 'Premium',
    free: 'Free',
    resultDetail: 'Chi tiết kết quả',
    version: 'Phiên bản',
    fail: 'Không Đạt',
    pass: 'Đạt',
    secondShort: 'giây',
    term: 'Điều khoản',
    support: 'Hỗ trợ',
    privacyPolicy: 'Chính sách bảo mật',
    termOfUse: 'Điều khoản sử dụng',
    percentageRequireToPass: 'Điểm để pass',
    plan: 'Gói',
    resetData: 'Thiết lập lại dữ liệu',
    upgradeToGetFeature: 'Dùng thử miễn phí 3 ngày với phiên bản Premium',
    deleteSuccess: 'Đã xóa thành công',
    confirmDeleteQuestionReview:
        'Bạn có chắc chắn muốn xóa toàn bộ dữ liệu lịch sử làm bài không?',
    upgrade: 'Thử ngay',
    unlockAllExams: 'Mở khóa tất cả bài kiểm tra',
    unlockQR: 'Mở khóa tính năng Review Question',
    premiumFeature: 'Các tính năng Premium',
    hide: 'Ẩn',
    delete: 'Xoá',
    viewExplanation: 'Xem giải thích',
    confirmDeleteQuestion:
        'Bạn có chắc chắn muốn xóa tất cả các câu hỏi đã chọn không?',
    yes: 'Có',
    restorePurchase: 'Khôi phục đăng ký',
    thankForPurchase: 'Bạn đã mở khóa tính năng premium',
    purchase: 'Đăng Ký',
    alreadyPaid: 'Đã Đăng Ký?',
    month: 'Tháng',
    months: 'Tháng',
    total: 'Tổng',
    monthly: 'Hàng tháng',
    withExplain: 'Đáp án kèm lời giải chi tiết',
    unlockAllExam: 'Mở khóa toàn bộ bài kiểm tra',
    unlockFeature: 'Mở khóa tính năng',
    accessPremium: 'Vượt qua kì thi dễ dàng',
    statistics: 'Thống kê',
    noMarkedQuestion: 'Bạn chưa đánh dấu câu hỏi nào',
    noWrongQuestion: 'Bạn chưa có câu hỏi sai nào',
    questionReview: 'Xem lại câu hỏi',
    numberWrong: 'Sai: {e} lần',
    markedQuestion: 'Câu hỏi đánh dấu',
    wrongQuestion: 'Câu hỏi sai',
    unanswered: 'Chưa trả lời',
    wrong: 'Sai',
    correct: 'Đúng',
    totalQuestion: 'Tổng cộng: {e} câu hỏi',
    yourName: 'Tên của bạn',
    retryInternet: 'Thử lại',
    viewExamList: 'Xem Danh Sách Bài Kiểm Tra',
    minutesShort: 'phút',
    left: 'còn lại',
    numberQuestion: 'câu hỏi',
    reportDetail: 'Chi tiết báo lỗi',
    numReport: 'Câu hỏi báo lỗi',
    cancel: 'Bỏ qua',
    send: 'Gửi',
    reportSuccess: 'Báo cáo của bạn đã được gửi.',
    report: 'Báo lỗi',
    notAnswered: 'Chưa trả lời',
    forceUpdateDialogContent:
        'Đã có bản cập nhật mới. Bạn cần cập nhật để tiếp tục sử dụng.',
    rateUs: 'Đánh giá app',
    updateDialogContent: 'Đã có bản cập nhật mới. Bạn có muốn cập nhật?',
    noInternet: 'Không có kết nối mạng',
    totalQuizDone: 'Tổng số bài kiểm tra đã làm',
    recentScore: 'Điểm bài kiểm tra gần nhất',
    highScore: 'Số lần pass liên tiếp dài nhất',
    fontExam: 'Kích cỡ chữ màn hình làm bài kiểm tra',
    averageExamResult: 'Điểm trung bình của bạn',
    serviceSatisfactionRate: 'Tỷ lệ hài lòng về dịch vụ',
    certificationPassRate: 'Tỷ lệ đạt chứng chỉ',
    yourMessage:
        'Hãy cho chúng tôi biết cách chúng tôi có thể hỗ trợ bạn tốt hơn?',
    writeUsDirectly: 'Gửi tin nhắn trực tiếp cho chúng tôi',
    contact: 'Liên hệ với chúng tôi',
    callUs: 'Gọi điện cho chúng tôi',
    connectUs: 'Liên hệ với chúng tôi',
    quizNumber: 'Đây là lần làm bài thứ {e} của bạn',
    confirmRetry: 'Bạn đã sẵn sàng làm lại?',
    noUserName: 'Hãy nhập tên của bạn',
    noQuizResult: 'Bạn chưa có hoàn thành bài kiểm tra nào',
    noInfo: 'Không có thông tin',
    errorDialog: 'Có lỗi xảy ra, vui lòng thử lại sau',
    error: 'Lỗi',
    confirmDoQuiz: 'Bạn đã sẵn sàng làm bài?',
    resumeQuizDialog: 'Bạn đang có bài kiểm tra chưa hoàn thành, vẫn tiếp tục?',
    bookmarked: 'Đã đánh dấu',
    questionList: 'Danh sách câu hỏi',
    allQuestion: 'Tất cả câu hỏi',
    previous: 'Trước',
    finish: 'Hoàn thành',
    next: 'Tiếp',
    questionOf: 'trên',
    question: 'Câu hỏi',
    timeUp: 'Hết giờ',
    remainTime: 'Thời gian còn lại',
    quitQuizDialog: 'Bạn chắc chắn muốn bỏ dở bài kiểm tra?',
    timeUpDialog: 'Bạn đã hết thời gian làm bài',
    no: 'Không',
    confirmEndQuiz: 'Bạn chắc chắn muốn kết thúc bài kiểm tra?',
    mustSelectAnswer: 'Bạn hãy chọn đáp án để tiếp tục',
    attention: 'Chú ý!',
    productOfScrumPass: 'Một sản phẩm của ScrumPass',
    wellcomeEnterName: 'Bạn hãy nhập tên để bắt đầu',
    allAns: 'Tất cả đáp án',
    save: 'Lưu',
    noLimit: 'Không giới hạn',
    answered: 'Đã trả lời',
    noAnsSelected: 'Chưa chọn đáp án',
    continueQ: 'Làm tiếp',
    time: 'Thời gian làm',
    back: 'Quay lại',
    intruction: 'Hướng dẫn',
    minPercentage: 'Phần trăm tối thiểu để vượt qua',
    timeEnd: 'Thời gian kết thúc',
    timeStart: 'Thời gian bắt đầu',
    quizDetail: 'Thông tin bài kiểm tra',
    userDetail: 'Cài đặt',
    noQuizAvaliable: 'Chưa có bài kiểm tra nào để làm',
    minutes: 'phút',
    timeLeft: 'Thời gian còn lại',
    duration: 'Thời lượng',
    numOfQuestion: 'Số câu hỏi',
    begin: 'Làm bài',
    answer: 'Đáp án',
    retry: 'Làm lại',
    home: 'Màn hình chính',
    wrongAns: 'Câu trả lời sai',
    rightAns: 'Câu trả lời đúng',
    marked: 'Câu đánh dấu',
    finishDate: 'Ngày làm',
    numHasDone: 'Số lần đã làm',
    numWrongAns: 'Số câu trả lời sai',
    numRightAns: 'Số câu trả lời đúng',
    passRequirement: 'cần đạt hơn {e}% để vượt qua',
    detail: 'Chi tiết',
    resultList: 'Danh sách kết quả',
    quizList: 'Danh sách bài kiểm tra',
    aboutUs: 'Về chúng tôi',
    scrumguide: 'Scrum Guide',
    result: 'Kết quả',
    examPractice: 'Làm bài kiểm tra',
    numFailQuiz: 'Số bài kiểm tra đã fail',
    numPassQuiz: 'Số bài kiểm tra đã pass',
    passPercentage: 'Tỷ lệ đạt',
    chooseLang: 'Chọn ngôn ngữ',
    lang: 'Ngôn ngữ',
    name: 'Tên',
    hello: 'Xin chào',
    wellcomeTitlePrince2: 'Chào mừng đến với Prince2 Foundation Exam Simulator',
    wellcomeTitleCissp: 'Chào mừng đến với CISSP Exam Simulator',
    wellcomeTitleComptiaLinux:
        'Chào mừng đến với CompTIA Linux+ Exam Simulator',
    wellcomeTitleComptiaCloud:
        'Chào mừng đến với CompTIA Cloud+ Exam Simulator',
    wellcomeTitleComptiaProject:
        'Chào mừng đến với CompTIA Project+ Exam Simulator',
    wellcomeTitleComptiaServer:
        'Chào mừng đến với CompTIA Server+ Exam Simulator',
    wellcomeTitleSAFePOPM: 'Chào mừng đến với SAFe POPM Exam Simulator',
    wellcomeTitleSAFeSSM: 'Chào mừng đến với SAFe SSM Exam Simulator',
    wellcomeTitleComptiaSecurity:
        'Chào mừng đến với CompTIA Security+ Exam Simulator',
    wellcomeTitleComptiaNetwork:
        'Chào mừng đến với CompTIA Network+ Exam Simulator',
    wellcomeTitleComptiaITF: 'Chào mừng đến với CompTIA ITF+ Exam Simulator',
    wellcomeTitleComptiaA: 'Chào mừng đến với CompTIA A+ Exam Simulator',
    wellcomeTitleAwsMlsIos: 'Chào mừng đến với Machine Learning Exam Simulator',
    wellcomeTitleAwsMls:
        'Chào mừng đến với AWS Machine Learning Specialty Exam Simulator',
    wellcomeTitleAwsSap:
        'Chào mừng đến với AWS Solutions Architect Pro Exam Simulator',
    wellcomeTitleAwsDop: 'Chào mừng đến với AWS DevOps Pro Exam Simulator',
    wellcomeTitleItil: 'Chào mừng đến với ITIL Foundation Exam Simulator',
    wellcomeTitleAwsDva: 'Chào mừng đến với AWS Developer Exam Simulator',
    wellcomeTitleAwsClf: 'Chào mừng đến với AWS Cloud Exam Simulator',
    wellcomeTitleAwsSoa: 'Chào mừng đến với AWS SysOps Exam Simulator',
    wellcomeTitleRmp: 'Chào mừng đến với PMI-RMP Exam Simulator',
    wellcomeTitleAwsSaa: 'Chào mừng đến với AWS-SAA Exam Simulator',
    wellcomeTitlePba: 'Chào mừng đến với PMI-PBA Exam Simulator',
    wellcomeTitlePfmp: 'Chào mừng đến với PfMP Exam Simulator',
    wellcomeTitleEcba: 'Chào mừng đến với ECBA Exam Simulator',
    wellcomeTitleISTQB: 'Chào mừng đến với ISTQB Exam Simulator',
    wellcomeTitlePgmp: 'Chào mừng đến với PgMP Exam Simulator',
    wellcomeTitleCapm: 'Chào mừng đến với CAPM Exam Simulator',
    wellcomeTitleCbap: 'Chào mừng đến với CBAP Exam Simulator',
    wellcomeTitleCCBA: 'Chào mừng đến với CCBA Exam Simulator',
    wellcomeTitlePmp: 'Chào mừng đến với PMP Exam Simulator',
    wellcomeTitlePsd: 'Chào mừng đến với PSD Exam Simulator',
    wellcomeTitleAcp: 'Chào mừng đến với ACP Exam Simulator',
    wellcomeTitlePsk: 'Chào mừng đến với PSK Exam Simulator',
    wellcomeTitleSps: 'Chào mừng đến với SPS Exam Simulator',
    wellcomeTitlePal: 'Chào mừng đến với PAL-I Exam Simulator',
    wellcomeTitlePsm2: 'Chào mừng đến với PSM-II Exam Simulator',
    wellcomeTitlePspo2: 'Chào mừng đến với PSPO-II Exam Simulator',
    wellcomeTitlePspo: 'Chào mừng đến với PSPO Exam Simulator',
    wellcomeTitle: 'Chào mừng đến với PSM Exam Simulator',
    appDescriptionPrince2:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ PRINCE2® 7 Foundation Exam của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionCISSP:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ CISSP  Exam của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionComptiaLinux:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ CompTIA Linux+ Exam của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionComptiaCloud:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ CompTIA Cloud Essentials+ Exam của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionComptiaProject:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ CompTIA Project+ Exam của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionComptiaServer:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ CompTIA Server+ Exam của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionSAFePOPM:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ SAFe POPM Exam của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionSAFeSSM:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ SAFe SSM Exam của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionComptiaSecurity:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ CompTIA Security+ Exam của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionComptiaNetwork:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ CompTIA Network+ Exam của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionComptiaITF:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ CompTIA ITF+ Exam của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionComptiaA:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ CompTIA A+ của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionAwsMls:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ AWS Machine Learning Specialty của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionAwsSap:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ AWS Solution Architect Pro của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionAwsDop:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ AWS DevOps Pro của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionItil:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ ITIL Foundation của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionAwsDva:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ AWS Developer của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionAwsClf:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ AWS Cloud của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionAwsSoa:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ AWS SysOps của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionRmp:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ PMI-RMP của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionAwsSaa:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ AWS-SAA của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionPba:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ PMI-PBA của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionPfmp:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ PfMP của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionEcba:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ ECBA của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionISTQB:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ ISTQB Foundation của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionPgmp:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Program Management Professional (PgMP) của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionCapm:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Certified Associate in Project Management (CAPM) của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionCbap:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Certified Business Analysis Professional (CBAP) của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionCCBA:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Certification of Capability in Business Analysis (CCBA) của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionPmp:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Project Management Professional (PMI-PMP) của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionPsd:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Professional Scrum Developer (PSD) của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionAcp:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ PMI Agile Certified Practitioner (PMI-ACP) của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionPsk:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Professional Scrum with Kanban (PSK-I) của bạn. Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionSps:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ The Scaled Professional Scrum (SPS). Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionPal:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Professional Agile Leadership (PAL-I). Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionPspo2:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Professional Scrum Product Owner II (PSPO-II). Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionPspo:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Professional Scrum Product Owner (PSPO). Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescriptionPsm2:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Professional Scrum Master II (PSM-II). Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appDescription:
        'Ứng dụng hỗ trợ ôn tập và luyện thi chứng chỉ Professional Scrum Master (PSM). Hãy làm bài kiểm tra hàng ngày để nâng cao kiến thức của bạn.',
    appTitlePrince2: 'PRINCE2® 7 Foundation Exam Simulator',
    appTitleCissp: 'CISSP Exam Simulator',
    appTitleComptiaLinux: 'CompTIA Linux+ Exam Simulator',
    appTitleComptiaCloud: 'CompTIA Cloud Essentials+ Exam Simulator',
    appTitleComptiaProject: 'CompTIA Project+ Exam Simulator',
    appTitleComptiaServer: 'CompTIA Server+ Exam Simulator',
    appTitleSAFePOPM: 'SAFe® Product Owner/Product Manager Exam Simulator',
    appTitleSAFeSSM: 'SAFe® Scrum Master Exam Simulator',
    appTitleComptiaSecurity: 'CompTIA Security+ Exam Simulator',
    appTitleComptiaNetwork: 'CompTIA Network+ Exam Simulator',
    appTitleComptiaITF: 'CompTIA ITF+ Exam Simulator',
    appTitleComptiaA: 'CompTIA A+ Exam Simulator',
    appTitleAwsMls: 'AWS Certified Machine Learning - Specialty Exam Simulator',
    appTitleAwsSap:
        'AWS Certified Solution Architect - Professional Exam Simulator',
    appTitleAwsDop:
        'AWS Certified DevOps Engineer - Professional Exam Simulator',
    appTitleItil:
        'Information Technology Infrastructure Library ITIL Foundation Exam Simulator',
    appTitleAwsDva:
        'AWS Certified Developer Associate (AWS Developer) Certification Exam Simulator',
    appTitleAwsClf:
        'AWS Certified Cloud Practitioner (AWS Cloud) Certification Exam Simulator',
    appTitleAwsSoa:
        'AWS Certified Sysops Administrator - Associate (AWS SysOps) Certification Exam Simulator',
    appTitleRmp:
        'PMI Risk Management Professional (PMI RMP) Certification Exam Simulator',
    appTitleAwsSaa:
        'AWS Certified Solutions Architect - Associate SAA-C03 Exam Simulator',
    appTitlePba:
        'PMI Professional in Business Analysis (PMI-PBA) Certification Exam Simulator',
    appTitlePfmp:
        'Portfolio Management Professional (PfMP) Certification Exam Simulator',
    appTitleEcba:
        'Entry Certificate in Business Analysis (ECBA) Exam Simulator',
    appTitleISTQB:
        'International Software Testing Qualifications Board (ISTQB) Foundation Level',
    appTitlePgmp:
        'Program Management Professional (PgMP) Certification Exam Simulator',
    appTitleCapm:
        'Certified Associate in Project Management (CAPM) Certification Exam Simulator',
    appTitleCbap:
        'Certified Business Analysis Professional Certification (CBAP) Exam Simulator',
    appTitleCCBA:
        'Certification of Capability in Business Analysis (CCBA) Exam Simulator',
    appTitlePmp:
        'Project Management Professional (PMI-PMP) Certification Exam Simulator',
    appTitlePsd:
        'Professional Scrum Developer (PSD) Certification Exam Simulator',
    appTitleAcp:
        'PMI Agile Certified Practitioner (PMI-ACP) Certification Exam Simulator',
    appTitlePsk:
        'Professional Scrum with Kanban (PSK-I) Certification Exam Simulator',
    appTitleSps: 'Scaled Professional Scrum (SPS) Certification Exam Simulator',
    appTitlePal:
        'Professional Agile Leadership (PAL-I) Certification Exam Simulator',
    appTitlePspo2:
        'Professional Scrum Product Owner II (PSPO-II) Certification Exam Simulator',
    appTitlePspo:
        'Professional Scrum Product Owner (PSPO) Certification Exam Simulator',
    appTitlePsm2:
        'Professional Scrum Master II (PSM-II) Certification Exam Simulator',
    appTitle: 'Professional Scrum Master (PSM) Certification Exam Simulator',
    appNamePrince2: 'Prince2 Foundation Exam Simulator',
    appNameCissp: 'CISSP Exam Simulator',
    appNameComptiaLinux: 'CompTIA Linux+ Exam Simulator',
    appNameComptiaCloud: 'CompTIA Cloud Essentials+ Exam Simulator',
    appNameComptiaProject: 'CompTIA Project+ Exam Simulator',
    appNameComptiaServer: 'CompTIA Server+ Exam Simulator',
    appNameSAFePOPM: 'SAFe POPM Exam Simulator',
    appNameSAFeSSM: 'SAFe SSM Exam Simulator',
    appNameComptiaSecurity: 'CompTIA Security+ Exam Simulator',
    appNameComptiaNetwork: 'CompTIA Network+ Exam Simulator',
    appNameComptiaITF: 'CompTIA ITF+ Exam Simulator',
    appNameComptiaA: 'CompTIA A+ Exam Simulator',
    appNameAwsMls: 'AWS Machine Learning Specialty Exam Simulator',
    appNameAwsSap: 'AWS Solutions Architect Pro Exam Simulator',
    appNameAwsDop: 'AWS DevOps Pro Exam Simulator',
    appNameItil: 'ITIL Foundation Exam Simulator',
    appNameAwsDva: 'AWS Developer Exam Simulator',
    appNameAwsClf: 'AWS Cloud Exam Simulator',
    appNameAwsSoa: 'AWS SysOps Exam Simulator',
    appNameRmp: 'PMI-RMP Exam Simulator',
    appNameAwsSaa: 'AWS Solutions Architect Exam Simulator',
    appNamePba: 'PMI-PBA Exam Simulator',
    appNamePfmp: 'PfMP Exam Simulator',
    appNameEcba: 'ECBA Exam Simulator',
    appNameISTQB: 'ISTQB Exam Simulator',
    appNamePgmp: 'PgMP Exam Simulator',
    appNameCapm: 'CAPM Exam Simulator',
    appNameCbap: 'CBAP Exam Simulator',
    appNameCCBA: 'CCBA Exam Simulator',
    appNamePmp: 'PMP Exam Simulator',
    appNamePsd: 'PSD Exam Simulator',
    appNameAcp: 'ACP Exam Simulator',
    appNamePsk: 'PSK Exam Simulator',
    appNameSps: 'SPS Exam Simulator',
    appNamePal: 'PAL-I Exam Simulator',
    appNamePspo2: 'PSPO-II Exam Simulator',
    appNamePsm2: 'PSM-II Exam Simulator',
    appNamePspo: 'PSPO Exam Simulator',
    appName: 'PSM Exam Simulator',
  ),
  const Locale.fromSubtags(languageCode: 'de'): const AppLocalizationsData(
    finishEarly:
        'Es ist wichtig, dass Sie nichts falsch machen. Warum ist das so?',
    redeemCodeError: 'Ungültiger Code',
    introduceDescription:
        '<p><b>Common.appName</b> ist Ihr One-Stop-Shop für die Beherrschung einer breiten Palette international anerkannter Zertifizierungen. Mit unseren umfassenden Tools und Ressourcen können Sie <b>Ihr Wissen erweitern</b> und <b>Ihre Prüfungen sicher bestehen</b>.</p><br><p><b>Begeben Sie sich auf ein personalisiertes Lernen Reise:</b></p><p><b>• Tauchen Sie ein in ein Meer von Übungsfragen:</b> Entdecken Sie eine umfangreiche Sammlung von Übungsfragen, die sorgfältig von Branchenexperten zusammengestellt wurden, um alle wesentlichen Lehrplanbereiche abzudecken. </p><p><b>• Verfolgen Sie Ihre Fortschritte präzise:</b> Überwachen Sie Ihre Leistung und identifizieren Sie Verbesserungsmöglichkeiten mit detaillierten Ergebniszusammenfassungen und personalisierten Erkenntnissen.</p><p><b>• Machen Sie mit -tiefes Verständnis:</b> Tauchen Sie ein in klare Erklärungen zu jeder Frage und Antwort und stellen Sie so ein umfassendes Verständnis jedes Themas sicher.</p><p><b>• Simulieren Sie das echte Prüfungserlebnis:</b> Tauchen Sie ein Authentische Prüfungsbedingungen mit zeitgesteuerten Probeprüfungen, die die tatsächliche Prüfungsumgebung nachbilden.</p><p><b>• Bleiben Sie auf dem Laufenden:</b> Greifen Sie auf regelmäßig aktualisierte Fragensätze zu, die die neuesten Prüfungsinhalte widerspiegeln, und stellen Sie so sicher, dass Sie Wir sind immer vorbereitet.</p><br><p><b>Mit Common.appName haben Sie alles, was Sie brauchen, um Ihre internationalen Zertifizierungsziele zu erreichen.</b></p>',
    introduceTitle:
        'Willkommen bei Ihrem Tor zum internationalen Zertifizierungserfolg',
    then: 'Dann',
    emptyExamTips: 'Derzeit sind keine Prüfungstipps verfügbar',
    afterTrial: 'nach Ende des Prozesses',
    trialDescription:
        '- Die kostenlose Testversion ist nur einmal verfügbar.\n- Das Abonnement beginnt automatisch, nachdem die kostenlose Testversion endet.\n- Sie können Ihr Abonnement im App Store oder Play Store kündigen.\n- Wenn das Abonnement 24 Stunden vor Ablauf gekündigt wird des aktuellen Nutzungszeitraums endet das Abonnement automatisch nach Ende des Nutzungszeitraums.',
    connectServerError:
        'Es kann keine Verbindung zum Server hergestellt werden. Bitte versuchen Sie es später noch einmal',
    hour: 'Stunde',
    hours: 'Stunde',
    noReport: 'Sie haben keinen Bericht gesendet',
    sendResponseSuccess: 'Antwort erfolgreich senden',
    sendResponse: 'Antwort senden',
    closeReport: 'Bericht schließen',
    scrumpassResponse: 'Antwort von ScrumPass',
    yourResponse: 'Deine Antwort',
    reportContent: 'Inhalt melden',
    closed: 'Gesperrt',
    reviewing: 'Überprüfung',
    status: 'Status',
    reportedQuestion: 'Gemeldete Frage',
    resultText: 'Prüfungsergebnis',
    listQuizShowcase2:
        'Die Fragen im Test sind festgelegt und ändern sich nicht, was Ihnen hilft, die Prüfung zu beschleunigen und sich auf die eigentliche Prüfung vorzubereiten.',
    listQuizShowcase1:
        'Jedes Mal, wenn Sie einen Test machen, werden Sie auf verschiedene Fragen stoßen, die für Ihre tägliche Übung und Überprüfung geeignet sind.',
    homeShowcase5: 'Lerne schnell Theorie und Glossar mit Karteikarten.',
    homeShowcase4:
        'Hilft Ihnen, die Fragen effektiver zu überprüfen, mit einer Liste von Fragen, die Sie falsch beantwortet haben, und die mit vollständigen Erklärungen gekennzeichnet sind.',
    homeShowcase3: 'Verlauf Ihrer Testergebnisse.',
    homeShowcase2: 'Liste der verfügbaren Tests, die Sie absolvieren können.',
    homeShowcase1:
        'Ihre Fähigkeit, den Test zu bestehen, basiert auf Ihrem Testverlauf. Sie können auf klicken, um Ihre detaillierten Ergebnisstatistiken anzuzeigen.',
    descriptionContent:
        'Exam Simulator ist ein führender Entwickler mobiler Apps, mit denen Lernende ihre internationalen Zertifizierungsprüfungen bestehen können. Wir sind ein leidenschaftliches Team, das sich der Schaffung einer flexiblen und effektiven Lernplattform verschrieben hat, die auf Ihre individuellen Bedürfnisse zugeschnitten ist.\n\nEs liegt uns am Herzen, Lernende dabei zu unterstützen, ihre internationalen Zertifizierungsziele zu erreichen. Als führender Entwickler mobiler Apps für die Prüfungsvorbereitung verstehen wir die einzigartigen Herausforderungen und Wünsche von Personen, die ihre Karriere durch professionelle Zertifizierungen vorantreiben möchten.\n\nSchließen Sie sich uns an und entdecken Sie eine Welt personalisierter Lerntools, ausführlicher Erklärungen usw Gezieltes Üben, um Ihr Wissen und Ihr Selbstvertrauen am Prüfungstag zu maximieren.',
    aboutUsContent:
        'ScrumPass ist eine professionelle Organisation, die sich auf die Bereitstellung von Mentoring- und Prüfungsvorbereitungsdiensten für Agile-Zertifizierungen wie PSM, PSPO, PMI-ACP spezialisiert hat. Mit engagierten Kursen haben wir jedes Jahr Hunderten von Studenten geholfen, die Prüfungen zu bestehen. Außerdem vertrauen viele Unternehmen und Einzelpersonen auf unsere Beratungsdienste, den Prüfungssimulator und die Scrum-Tools.',
    copyToClipboard: 'In die Zwischenablage kopiert',
    androidLink:
        'Link zum Herunterladen der Android-App: https://play.google.com/store/apps/details?id=',
    iosLink:
        'Link zum Herunterladen der iOS-App: https://apps.apple.com/app/id',
    getCertificate:
        'Mit [app_name] ist es einfacher, ein [certificate_name]-Zertifikat zu erhalten.',
    shareApp: 'Teilen Sie die App mit Ihrem Freund',
    tutorialTap: 'Tippen Sie hier, um die Definition anzuzeigen',
    tutorialSwipeRight: 'Zum nächsten Wort nach rechts wischen',
    tutorialSwipeLeft: 'Wischen Sie nach links, um das Wort zu überspringen',
    flashcardEndText:
        'Sie haben gerade {e1} Begriffe gelernt! Üben Sie weiter, um das verbleibende {e2} zu meistern.',
    learnedEverything: 'Du hast alles gelernt',
    halfWayThere: 'Du bist auf halbem Weg!',
    congratulations: 'Herzliche Glückwünsche!',
    continueStudy: 'Studium fortsetzen',
    restart: 'neu starten',
    restartFlashCardPopup: 'Möchten Sie Flashcard wirklich neu starten?',
    favoriteWords: 'Lieblingswörter',
    score: 'Punktzahl',
    testNumber: 'Anzahl der Prüfungen',
    disclamer:
        'Die Exam Pass Rate wird basierend auf Ihren Prüfungsergebnissen und den Daten im System berechnet. Der Index dient nur als Referenz.',
    goodToGo: 'Gut zu gehen!!! Sie sind bereit, an der Prüfung teilzunehmen.',
    keepGoing:
        'Weitermachen. Sie sind ziemlich bereit, an der Prüfung teilzunehmen.',
    notEnough:
        'Sie haben noch nicht mindestens 10 Prüfungen abgeschlossen. Um die genaueste Bestehensquote zu erhalten, nehmen Sie bitte an einigen zusätzlichen Prüfungen teil.',
    examPassRateDetail: 'Statistiken',
    noData: 'Keine Daten',
    timeDetail: 'Zeitangaben',
    questionDetail: 'Fragendetails',
    eachQuestion: 'Jede Frage',
    totalTime: 'Gesamtzeit',
    questionDone: 'Frage erledigt',
    streak: 'Streak Prüfung bestanden',
    latestScore: 'Neueste Punktzahl',
    passedExam: 'Bestandene Prüfung',
    quizDone: 'Abgelegte Prüfungen',
    avgScore: 'Mittlere Punktzahl',
    quizFlashCardDetail: 'Prüfungsdetails',
    flashCard: 'Speicherkarte',
    notReady:
        'Du bist nicht bereit. Bitte verbringen Sie mehr Zeit mit dem Training, um bessere Ergebnisse zu erzielen.',
    examPassRate: 'Bestehensquote der Prüfung',
    emptyField: 'Das Feld darf nicht leer sein',
    notificationDetail: 'Komm zurück und mach deinen Test',
    notification: 'Benachrichtigung',
    questionrule: 'Die Fragen im Test sind festgelegt',
    randomRule: 'Die Fragen sind in den Prüfungen unterschiedlich',
    getSupport: 'Holen Sie sich Unterstützung von uns',
    premiumVersion: 'Premium-Version',
    someTips4U: 'QUELQUES CONSEILS POUR VOUS',
    yourCode: 'Dein Code',
    enterRedeemCode: 'Bitte geben Sie Ihren Einlösecode ein',
    redeemCode: 'Code einlösen',
    updateYear: 'aktualisiert für %1',
    fully: 'Völlig',
    detailedExplain: 'ausführliche Erläuterungen',
    answersWith: 'Antworten mit',
    allTheExams: 'alle Prüfungen',
    unlock: 'Freischalten',
    startTrial: 'Starten Sie die kostenlose 3-Tage-Testversion',
    cancelAnyTime: 'Kündigen Sie Ihr Abonnement jederzeit',
    fullyIUpdated: 'Vollständig aktualisiert für %1',
    trialTitle: '3-tägige Testversion mit unbegrenztem Zugriff:',
    sunday: 'Sonntag',
    saturday: 'Samstag',
    friday: 'Freitag',
    thursday: 'Donnerstag',
    wednesday: 'Mittwoch',
    tuesday: 'Dienstag',
    monday: 'Montag',
    notificationStatus: 'Lernerinnerung',
    notificationDays: 'Hinweis der Tage',
    notificationTime: 'Benachrichtigungszeit',
    expiresToday: 'Läuft heute ab',
    trial: 'Studie',
    dayLeft: 'tag übrig',
    otherApp: 'Other Apps',
    randomSet: 'Zufällige Frage',
    questionSet: 'Feste Frage',
    doing: 'Tun',
    premium: 'Premium',
    free: 'Free',
    resultDetail: 'Ergebnisdetail',
    version: 'Ausführung',
    fail: 'Scheitern',
    pass: 'Passieren',
    secondShort: 'Sek',
    term: 'Bedingungen',
    support: 'Die Unterstützung (Support)',
    privacyPolicy: 'Datenschutz-Bestimmungen',
    termOfUse: 'Nutzungsbedingungen',
    percentageRequireToPass: 'Passpunktzahl (Punktzahl um zu bestehen)',
    plan: 'Planen',
    resetData: 'Daten zurücksetzen',
    upgradeToGetFeature: '3-tägige kostenlose Testversion mit Premium-Version',
    deleteSuccess: 'Erfolgreich gelöscht',
    confirmDeleteQuestionReview:
        'Möchten Sie wirklich alle Überprüfungsdaten für Fragen löschen?',
    upgrade: 'Versuch\'s jetzt',
    unlockAllExams: 'Schalte alle Prüfungen frei',
    unlockQR: 'Entsperren Sie die Funktion Review Question',
    premiumFeature: 'Premium-Funktion',
    hide: 'Ausblenden',
    delete: 'Löschen',
    viewExplanation: 'Erläuterung anzeigen',
    confirmDeleteQuestion:
        'Möchten Sie wirklich alle ausgewählten Fragen löschen?',
    yes: 'Ja',
    restorePurchase: 'Kauf wiederherstellen',
    thankForPurchase: 'Sie haben die Premium-Funktion freigeschaltet',
    purchase: 'Kaufen',
    alreadyPaid: 'Bereits bezahlt?',
    month: 'Monat',
    months: 'Monate',
    total: 'Gesamt',
    monthly: 'Monatlich',
    withExplain: 'Antworten mit ausführlichen Erklärungen',
    unlockAllExam: 'Schalte alle Prüfungen frei',
    unlockFeature: 'Entsperrfunktion',
    accessPremium: 'Bestehen Sie die Prüfung problemlos',
    statistics: 'Statistiken',
    noMarkedQuestion: 'Sie haben keine markierte Frage',
    noWrongQuestion: 'Sie haben keine falsche Frage',
    questionReview: 'Überprüfungsfrage',
    numberWrong: 'Falsch: {e} mal',
    markedQuestion: 'Markierte Frage',
    wrongQuestion: 'Falsche Frage',
    unanswered: 'Nicht beantwortet',
    wrong: 'Falsch',
    correct: 'Richtig',
    totalQuestion: 'Insgesamt: {e} Fragen',
    yourName: 'Ihr Name',
    retryInternet: 'Wiederholen',
    viewExamList: 'Prüfungsliste anzeigen',
    minutesShort: 'Min',
    left: 'links',
    numberQuestion: 'Fragen',
    reportDetail: 'Einzelheiten melden',
    numReport: 'Fragennummer',
    cancel: 'Abbrechen',
    send: 'Schicken',
    reportSuccess: 'Ihr Bericht wurde gesendet.',
    report: 'Prüfbericht',
    notAnswered: 'Nicht beantwortet',
    forceUpdateDialogContent:
        'Neues Update ist verfügbar. Sie müssen diese Anwendung aktualisieren, um fortzufahren.',
    rateUs: 'Bewerten Sie uns',
    updateDialogContent:
        'Neues Update ist verfügbar. Möchten Sie aktualisieren?',
    noInternet: 'Keine Internetverbindung',
    totalQuizDone: 'Abgelegte Prüfungen',
    recentScore: 'Ergebnis der letzten Prüfung',
    highScore: 'Höchste aufeinanderfolgende Pässe',
    fontExam: 'Schriftgröße der Prüfung',
    averageExamResult: 'Durchschnittliches Prüfungsergebnis',
    serviceSatisfactionRate: 'Service-Zufriedenheitsrate',
    certificationPassRate: 'Bestehensquote der Zertifizierung',
    yourMessage: 'Lassen Sie uns wissen, wie wir Ihnen besser helfen können?',
    writeUsDirectly: 'Schreiben Sie uns direkt',
    contact: 'Kontakt',
    callUs: 'Rufen Sie uns an',
    connectUs: 'Verbinden Sie uns',
    quizNumber: 'Dies ist Ihre Prüfungsnummer {e}',
    confirmRetry: 'Sind Sie bereit, es erneut zu versuchen?',
    noUserName: 'Bitte geben Sie Ihren Namen ein',
    noQuizResult: 'Sie haben keine Prüfung abgelegt',
    noInfo: 'Keine Informationen',
    errorDialog:
        'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut',
    error: 'Fehler',
    confirmDoQuiz: 'Bist du bereit für die Prüfung?',
    resumeQuizDialog:
        'Du hast eine Prüfung nicht beendet, möchtest aber trotzdem weitermachen?',
    bookmarked: 'Markiert',
    questionList: 'Fragenliste',
    allQuestion: 'Alle Fragen',
    previous: 'Bisherige',
    finish: 'Beenden',
    next: 'Nächste',
    questionOf: 'von',
    question: 'Frage',
    timeUp: 'Die Zeit ist um',
    remainTime: 'Verbleibende Zeit',
    quitQuizDialog: 'Sind Sie sicher, dass Sie die Prüfung abbrechen möchten?',
    timeUpDialog: 'Zeit ist um',
    no: 'Nein',
    confirmEndQuiz:
        'Sind Sie sicher, dass Sie die Prüfung abschließen möchten?',
    mustSelectAnswer: 'Bitte wählen Sie alle zutreffenden aus',
    attention: 'Achtung!',
    productOfScrumPass: 'Ein Produkt von ScrumPass',
    wellcomeEnterName: 'Bitte geben Sie Ihren Namen ein, um zu beginnen',
    allAns: 'Alle Antworten',
    save: 'Speichern',
    noLimit: 'Keine Begrenzung',
    answered: 'Beantwortete',
    noAnsSelected: 'Keine Antwort ausgewählt',
    continueQ: 'Fortsetzen',
    time: 'Zeit',
    back: 'Zurück',
    intruction: 'Anweisung',
    minPercentage: 'Passpunktzahl',
    timeEnd: 'Endzeit',
    timeStart: 'Startzeit',
    quizDetail: 'Einzelheiten (Details)',
    userDetail: 'Einstellung',
    noQuizAvaliable: 'Keine Prüfung verfügbar',
    minutes: 'Protokoll (Minuten)',
    timeLeft: 'Übrige Zeit',
    duration: 'Dauer',
    numOfQuestion: 'Fragen',
    begin: 'Prüfung ablegen',
    answer: 'Antworten',
    retry: 'Wiederholen',
    home: 'Startbildschirm',
    wrongAns: 'Falsche Antworten',
    rightAns: 'Richtige Antwort',
    marked: 'Markierte Fragen',
    finishDate: 'Datum',
    numHasDone: 'Anzahl der Versuche',
    numWrongAns: 'Anzahl falscher Antworten',
    numRightAns: 'Anzahl richtiger Antworten',
    passRequirement: 'Zum Bestehen werden {e} % benötigt',
    detail: 'Kontodetails',
    resultList: 'Ergebnisliste',
    quizList: 'Prüfungsliste',
    aboutUs: 'Über uns',
    scrumguide: 'Scrum-Leitfaden',
    result: 'Prüfungsergebnis',
    examPractice: 'Prüfung ablegen',
    numFailQuiz: 'Gescheitert',
    numPassQuiz: 'Bestanden',
    passPercentage: 'Pass-Prozentsatz',
    chooseLang: 'Sprache wählen',
    lang: 'Sprache',
    name: 'Name',
    hello: 'Hallo',
    wellcomeTitlePrince2: 'Willkommen beim Prince2 Foundation Exam Simulator',
    wellcomeTitleCissp: 'Willkommen beim CISSP Exam Simulator',
    wellcomeTitleComptiaLinux: 'Willkommen beim CompTIA Linux+ Exam Simulator',
    wellcomeTitleComptiaCloud: 'Willkommen beim CompTIA Cloud+ Exam Simulator',
    wellcomeTitleComptiaProject:
        'Willkommen beim CompTIA Project+ Exam Simulator',
    wellcomeTitleComptiaServer:
        'Willkommen beim CompTIA Server+ Exam Simulator',
    wellcomeTitleSAFePOPM: 'Willkommen beim SAFe POPM Exam Simulator',
    wellcomeTitleSAFeSSM: 'Willkommen beim SAFe SSM Exam Simulator',
    wellcomeTitleComptiaSecurity:
        'Willkommen beim CompTIA Security+ Exam Simulator',
    wellcomeTitleComptiaNetwork:
        'Willkommen beim CompTIA Network+ Exam Simulator',
    wellcomeTitleComptiaITF: 'Willkommen beim CompTIA ITF+ Exam Simulator',
    wellcomeTitleComptiaA: 'Willkommen beim CompTIA A+ Exam Simulator',
    wellcomeTitleAwsMlsIos: 'Willkommen beim Machine Learning Exam Simulator',
    wellcomeTitleAwsMls:
        'Willkommen beim AWS Machine Learning Specialty Exam Simulator',
    wellcomeTitleAwsSap:
        'Willkommen beim AWS Solutions Architect Pro Exam Simulator',
    wellcomeTitleAwsDop: 'Willkommen beim AWS DevOps Pro Exam Simulator',
    wellcomeTitleItil: 'Willkommen beim ITIL Foundation-Prüfungssimulator',
    wellcomeTitleAwsDva: 'Willkommen beim AWS Developer-Prüfungssimulator',
    wellcomeTitleAwsClf: 'Willkommen beim AWS Cloud-Prüfungssimulator',
    wellcomeTitleAwsSoa: 'Willkommen beim AWS SysOps-Prüfungssimulator',
    wellcomeTitleRmp: 'Willkommen beim PMI-RMP-Prüfungssimulator',
    wellcomeTitleAwsSaa: 'Willkommen beim AWS-SAA-Prüfungssimulator',
    wellcomeTitlePba: 'Willkommen beim PMI-PBA-Prüfungssimulator',
    wellcomeTitlePfmp: 'Willkommen beim PfMP-Prüfungssimulator',
    wellcomeTitleEcba: 'Willkommen beim ECBA-Prüfungssimulator',
    wellcomeTitleISTQB: 'Willkommen beim ISTQB-Prüfungssimulator',
    wellcomeTitlePgmp: 'Willkommen beim PgMP-Prüfungssimulator',
    wellcomeTitleCapm: 'Willkommen beim CAPM-Prüfungssimulator',
    wellcomeTitleCbap: 'Willkommen beim CBAP-Prüfungssimulator',
    wellcomeTitleCCBA: 'Willkommen beim CCBA-Prüfungssimulator',
    wellcomeTitlePmp: 'Willkommen beim PMP-Prüfungssimulator',
    wellcomeTitlePsd: 'Willkommen beim PSD-Prüfungssimulator',
    wellcomeTitleAcp: 'Willkommen beim ACP-Prüfungssimulator',
    wellcomeTitlePsk: 'Willkommen beim PSK-Prüfungssimulator',
    wellcomeTitleSps: 'Willkommen beim SPS-Prüfungssimulator',
    wellcomeTitlePal: 'Willkommen beim PAL-I-Prüfungssimulator',
    wellcomeTitlePsm2: 'Willkommen beim PSM-II-Prüfungssimulator',
    wellcomeTitlePspo2: 'Willkommen beim PSPO-II-Prüfungssimulator',
    wellcomeTitlePspo: 'Willkommen beim PSPO-Prüfungssimulator',
    wellcomeTitle: 'Willkommen beim PSM-Prüfungssimulator',
    appDescriptionPrince2:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr PRINCE2® 7 Foundation Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionCISSP:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr CISSP  Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionComptiaLinux:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr CompTIA Linux+ Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionComptiaCloud:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr CompTIA Cloud Essentials+ Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionComptiaProject:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr CompTIA Project+ Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionComptiaServer:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr CompTIA Server+ Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionSAFePOPM:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr SAFe POPM Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionSAFeSSM:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr SAFe SSM Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionComptiaSecurity:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr CompTIA Security+ Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionComptiaNetwork:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr CompTIA Network+ Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionComptiaITF:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr CompTIA ITF+ Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionComptiaA:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr AWS CompTIA A+ Exam. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionAwsMls:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr AWS Machine Learning Specialty. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionAwsSap:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr AWS Solution Architect Pro. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionAwsDop:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr AWS DevOps Pro. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionItil:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr ITIL Foundation. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionAwsDva:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr AWS Developer. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionAwsClf:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr AWS Cloud. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionAwsSoa:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr AWS SysOps. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionRmp:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr PMI-RMP. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionAwsSaa:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr AWS-SAA. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionPba:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr PMI-PBA. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionPfmp:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr PfMP. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionEcba:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr ECBA. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionISTQB:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr ISTQB Foundation. Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionPgmp:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr Program Management Professional (PgMP). Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionCapm:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr Certified Associate in Project Management (CAPM). Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionCbap:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr Certified Business Analysis Professional (CBAP). Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionCCBA:
        'Simulationstests zur Unterstützung Ihrer Prüfungsvorbereitung und Überprüfung für Ihr Certificate of Competency in Business Analytics (CCBA). Nehmen Sie am täglichen Quiz teil, um Ihr Wissen zu verbessern.',
    appDescriptionPmp:
        'Simulationstest zur Vorbereitung auf Ihre PMI-PMP-Prüfung. Verwenden Sie diese App täglich, um Ihr Wissen zu verbessern.',
    appDescriptionPsd:
        'Simulationstest zur Vorbereitung auf Ihre PSD-Prüfung. Verwenden Sie diese App täglich, um Ihr Wissen zu verbessern.',
    appDescriptionAcp:
        'Simulationstest zur Vorbereitung auf Ihre PMI-ACP-Prüfung. Verwenden Sie diese App täglich, um Ihr Wissen zu verbessern.',
    appDescriptionPsk:
        'Simulationstest zur Vorbereitung auf Ihre PSK I Prüfung. Verwenden Sie diese App täglich, um Ihr Wissen zu verbessern.',
    appDescriptionSps:
        'Simulationstests zur Vorbereitung auf Ihre SPS-Prüfung. Verwenden Sie diese App täglich, um Ihr Wissen zu verbessern.',
    appDescriptionPal:
        'Simulationstests zur Vorbereitung auf Ihre PAL-I-Prüfung. Verwenden Sie diese App täglich, um Ihr Wissen zu verbessern.',
    appDescriptionPspo2:
        'Simulationstests zur Vorbereitung auf Ihre PSPO-Prüfung. Verwenden Sie diese App täglich, um Ihr Wissen zu verbessern.',
    appDescriptionPspo:
        'Simulationstests zur Vorbereitung auf Ihre PSPO-Prüfung. Verwenden Sie diese App täglich, um Ihr Wissen zu verbessern.',
    appDescriptionPsm2:
        'Simulationstests zur Vorbereitung auf Ihre PSM-Prüfung. Verwenden Sie diese App täglich, um Ihr Wissen zu verbessern.',
    appDescription:
        'Simulationstests zur Vorbereitung auf Ihre PSM-Prüfung. Verwenden Sie diese App täglich, um Ihr Wissen zu verbessern.',
    appTitlePrince2: 'PRINCE2® 7 Foundation Prüfungssimulator',
    appTitleCissp: 'CISSP-Prüfungssimulator',
    appTitleComptiaLinux: 'CompTIA Linux+ Prüfungssimulator',
    appTitleComptiaCloud: 'CompTIA Cloud Essentials+ Prüfungssimulator',
    appTitleComptiaProject: 'CompTIA Project+ Prüfungssimulator',
    appTitleComptiaServer: 'CompTIA Server+ Prüfungssimulator',
    appTitleSAFePOPM: 'SAFe® Product Owner/Produktmanager-Prüfungssimulator',
    appTitleSAFeSSM: 'SAFe® Scrum Master Prüfungssimulator',
    appTitleComptiaSecurity: 'CompTIA Security+ Prüfungssimulator',
    appTitleComptiaNetwork: 'CompTIA Network+ Prüfungssimulator',
    appTitleComptiaITF: 'CompTIA ITF+ Prüfungssimulator',
    appTitleComptiaA: 'CompTIA A+ Prüfungssimulator',
    appTitleAwsMls: 'AWS Certified Machine Learning – Spezialprüfungssimulator',
    appTitleAwsSap:
        'AWS Certified Solution Architect – Professioneller Prüfungssimulator',
    appTitleAwsDop:
        'AWS Certified DevOps Engineer – Professioneller Prüfungssimulator',
    appTitleItil:
        'ITIL Foundation-Prüfungssimulator der Bibliothek für Informationstechnologie-Infrastruktur',
    appTitleAwsDva:
        'AWS Certified Developer Associate (AWS Developer) Zertifizierungsprüfungssimulator',
    appTitleAwsClf:
        'AWS Certified Cloud Practitioner (AWS Cloud) Zertifizierungsprüfungssimulator',
    appTitleAwsSoa:
        'AWS Certified Sysops Administrator – Associate (AWS SysOps) Zertifizierungsprüfungssimulator',
    appTitleRmp:
        'PMI Risk Management Professional (PMI RMP) Zertifizierungsprüfungssimulator',
    appTitleAwsSaa:
        'AWS Certified Solutions Architect – Associate SAA-C03 Prüfungssimulator',
    appTitlePba:
        'PMI Professional in Business Analysis (PMI-PBA) Zertifizierungsprüfungssimulator',
    appTitlePfmp:
        'Simulator für die Portfolio Management Professional (PfMP)-Zertifizierungsprüfung',
    appTitleEcba:
        'Prüfungssimulator für das Entry Certificate in Business Analysis (ECBA).',
    appTitleISTQB:
        'International Software Testing Qualifications Board (ISTQB) Foundation Level',
    appTitlePgmp:
        'Program Management Professional (PgMP) Zertifizierungsprüfungssimulator',
    appTitleCapm:
        'Certified Associate in Project Management (CAPM) Zertifizierungsprüfungssimulator',
    appTitleCbap:
        'Certified Business Analysis Professional (CBAP) Zertifizierungsprüfungssimulator',
    appTitleCCBA:
        'Certification of Capability in Business Analysis (CCBA) Zertifizierungsprüfungssimulator',
    appTitlePmp:
        'Project Management Professional (PMI-PMP) Zertifizierungsprüfungssimulator',
    appTitlePsd:
        'Professional Scrum Developer (PSD) Zertifizierungsprüfungssimulator',
    appTitleAcp:
        'PMI Agile Certified Practitioner (PMI-ACP)-Zertifizierungsprüfungssimulator',
    appTitlePsk:
        'Professional Scrum mit Kanban (PSK I) Zertifizierungsprüfungssimulator',
    appTitleSps:
        'Scaled Professional Scrum (SPS) Zertifizierungsprüfungssimulator',
    appTitlePal:
        'Professional Agile Leadership (PAL-I) Zertifizierungsprüfungssimulator',
    appTitlePspo2:
        'Professional Scrum Product Owner II (PSPO-II) Zertifizierungsprüfungssimulator',
    appTitlePspo:
        'Professional Scrum Product Owner (PSPO) Zertifizierungsprüfungssimulator',
    appTitlePsm2:
        'Professional Scrum Master II (PSM-II) Zertifizierungsprüfungssimulator',
    appTitle:
        'Professional Scrum Master (PSM) Zertifizierungsprüfungssimulator',
    appNamePrince2: 'Prince2 Foundation Prüfungssimulator',
    appNameCissp: 'CISSP Prüfungssimulator',
    appNameComptiaLinux: 'CompTIA Linux+ Prüfungssimulator',
    appNameComptiaCloud: 'CompTIA Cloud Essentials+ Prüfungssimulator',
    appNameComptiaProject: 'CompTIA Project+ Prüfungssimulator',
    appNameComptiaServer: 'CompTIA Server+ Prüfungssimulator',
    appNameSAFePOPM: 'SAFe POPM Prüfungssimulator',
    appNameSAFeSSM: 'SAFe SSM Prüfungssimulator',
    appNameComptiaSecurity: 'CompTIA Security+ Prüfungssimulator',
    appNameComptiaNetwork: 'CompTIA Network+ Prüfungssimulator',
    appNameComptiaITF: 'CompTIA ITF+ Prüfungssimulator',
    appNameComptiaA: 'CompTIA A+ Prüfungssimulator',
    appNameAwsMls: 'AWS Machine Learning Specialty Prüfungssimulator',
    appNameAwsSap: 'AWS Solutions Architect Pro Prüfungssimulator',
    appNameAwsDop: 'AWS DevOps Pro Prüfungssimulator',
    appNameItil: 'ITIL Foundation Prüfungssimulator',
    appNameAwsDva: 'AWS Developer Prüfungssimulator',
    appNameAwsClf: 'AWS Cloud Prüfungssimulator',
    appNameAwsSoa: 'AWS SysOps Prüfungssimulator',
    appNameRmp: 'PMI-RMP Prüfungssimulator',
    appNameAwsSaa: 'AWS Solutions Architect Prüfungssimulator',
    appNamePba: 'PMI-PBA Prüfungssimulator',
    appNamePfmp: 'PfMP Prüfungssimulator',
    appNameEcba: 'ECBA Prüfungssimulator',
    appNameISTQB: 'ISTQB Prüfungssimulator',
    appNamePgmp: 'PgMP Prüfungssimulator',
    appNameCapm: 'CAPM Prüfungssimulator',
    appNameCbap: 'CBAP Prüfungssimulator',
    appNameCCBA: 'CCBA Prüfungssimulator',
    appNamePmp: 'PMP Prüfungssimulator',
    appNamePsd: 'PSD Prüfungssimulator',
    appNameAcp: 'ACP Prüfungssimulator',
    appNamePsk: 'PSK Prüfungssimulator',
    appNameSps: 'SPS Prüfungssimulator',
    appNamePal: 'PAL-I Prüfungssimulator',
    appNamePspo2: 'PSPO-II Prüfungssimulator',
    appNamePsm2: 'PSM-II Prüfungssimulator',
    appNamePspo: 'PSPO-Prüfungssimulator',
    appName: 'PSM-Prüfungssimulator',
  ),
  const Locale.fromSubtags(languageCode: 'fr'): const AppLocalizationsData(
    finishEarly:
        'C\'est ce qui fait que vous n\'avez rien de mal à faire. Qu\'est-ce que c\'est ?',
    redeemCodeError: 'Code invalide',
    introduceDescription:
        '<p><b>Common.appName</b> est votre guichet unique pour maîtriser un large éventail de certifications reconnues au niveau international. Grâce à nos outils et ressources complets, vous pouvez <b>élever vos connaissances</b> et <b>réussir vos examens en toute confiance</b>.</p><br><p><b>Embarquez pour un apprentissage personnalisé parcours :</b></p><p><b>• Plongez dans un océan de questions pratiques :</b> Explorez une vaste collection de questions pratiques, soigneusement organisées par des experts du secteur, pour couvrir tous les domaines essentiels du programme. </p><p><b>• Suivez vos progrès avec précision :</b> Surveillez vos performances et identifiez les domaines à améliorer grâce à des résumés de résultats détaillés et des informations personnalisées.</p><p><b>• Gagnez en -Compréhension approfondie :</b> Plongez dans des explications claires pour chaque question et réponse, garantissant une compréhension approfondie de chaque sujet.</p><p><b>• Simulez l\'expérience réelle de l\'examen :</b> Plongez-vous dans conditions d\'examen authentiques avec des examens blancs chronométrés, reproduisant l\'environnement de test réel.</p><p><b>• Gardez une longueur d\'avance :</b> accédez à des ensembles de questions régulièrement mis à jour qui reflètent le dernier contenu de l\'examen, vous garantissant ainsi soyez toujours prêt.</p><br><p><b>Avec Common.appName, vous disposez de tout ce dont vous avez besoin pour atteindre vos objectifs de certification internationale.</b></p>',
    introduceTitle:
        'Bienvenue sur votre passerelle vers la réussite de la certification internationale',
    then: 'sau dó',
    emptyExamTips: 'Il n\'y a actuellement aucun conseil d\'examen disponible',
    afterTrial: 'après la fin du procès',
    trialDescription:
        '- L\'essai gratuit n\'est disponible qu\'une seule fois.\n- L\'abonnement commence automatiquement après la fin de l\'essai gratuit.\n- Vous pouvez annuler votre abonnement dans l\'App Store ou le Play Store.\n- Si l\'abonnement est annulé 24 heures avant la fin. de la période d\'utilisation en cours, l\'abonnement prendra fin automatiquement après la fin de la période d\'utilisation.',
    connectServerError:
        'Impossible de se connecter au serveur. Veuillez réessayer plus tard',
    hour: 'Heure',
    hours: 'Heures',
    noReport: 'Vous n\'avez envoyé aucun rapport',
    sendResponseSuccess: 'Envoyer la réponse réussie',
    sendResponse: 'Envoyer une réponse',
    closeReport: 'Fermer le rapport',
    scrumpassResponse: 'Réponse de ScrumPass',
    yourResponse: 'Votre réponse',
    reportContent: 'Contenu du rapport',
    closed: 'fermé',
    reviewing: 'Révision',
    status: 'Statut',
    reportedQuestion: 'Question signalée',
    resultText: 'Résultat d\'éxamen',
    listQuizShowcase2:
        'Les questions du test sont fixes et ne changent pas, ce qui vous aide à accélérer et à vous préparer à l\'examen proprement dit.',
    listQuizShowcase1:
        'Chaque fois que vous passez un test, vous rencontrerez différentes questions, adaptées à votre pratique et à votre révision quotidiennes.',
    homeShowcase5:
        'Apprenez rapidement la théorie et le glossaire avec des flashcards.',
    homeShowcase4:
        'Vous aide à réviser plus efficacement avec une liste de questions que vous vous êtes trompées et accompagnées d\'explications complètes.',
    homeShowcase3: 'Historique des résultats de vos tests.',
    homeShowcase2: 'Liste des tests disponibles que vous pouvez passer.',
    homeShowcase1:
        'Votre capacité à réussir le test est basée sur vos antécédents de test. Vous pouvez cliquer pour afficher vos statistiques de résultats détaillées.',
    descriptionContent:
        'Exam Simulator est l\'un des principaux développeurs d\'applications mobiles conçues pour permettre aux apprenants de réussir leurs examens de certification internationaux. Nous sommes une équipe passionnée, dédiée à la création d\'une plate-forme d\'apprentissage flexible et efficace qui répond à vos besoins individuels.\n\nNous sommes passionnés par l\'idée de donner aux apprenants les moyens d\'atteindre leurs objectifs de certification internationale. En tant que développeur leader d\'applications mobiles conçues pour la préparation aux examens, nous comprenons les défis et les aspirations uniques des personnes cherchant à faire progresser leur carrière grâce à des certifications professionnelles.\n\nRejoignez-nous et débloquez un monde d\'outils d\'apprentissage personnalisés, d\'explications détaillées et pratique ciblée pour maximiser vos connaissances et votre confiance le jour de l’examen.',
    aboutUsContent:
        'ScrumPass est une organisation professionnelle spécialisée dans la fourniture de services de mentorat et de préparation aux examens pour les certifications Agiles telles que PSM, PSPO, PMI-ACP. Avec des cours très engagés, nous avons aidé des centaines d\'étudiants à réussir les examens chaque année. De plus, nos services de conseil, simulateur d\'examen, outils Scrum ont été approuvés par de nombreuses entreprises et particuliers.',
    copyToClipboard: 'Copié dans le presse-papier',
    androidLink:
        'Lien pour télécharger l\'application Android : https://play.google.com/store/apps/details?id=',
    iosLink:
        'Lien pour télécharger l\'application ios : https://apps.apple.com/app/id',
    getCertificate:
        'Obtenir un certificat [certificate_name] est plus facile avec [app_name].',
    shareApp: 'Partager l\'application avec votre ami',
    tutorialTap: 'Appuyez pour afficher la définition',
    tutorialSwipeRight: 'Balayez vers la droite jusqu\'au mot suivant',
    tutorialSwipeLeft: 'Balayez vers la gauche pour sauter un mot',
    flashcardEndText:
        'Sie haben gerade {e1} Begriffe gelernt! Üben Sie weiter, um das verbleibende {e2} zu meistern.',
    learnedEverything: 'Tu as tout appris',
    halfWayThere: 'Vous êtes à mi-chemin !',
    congratulations: 'Toutes nos félicitations!',
    continueStudy: 'Continuer l\'étude',
    restart: 'redémarrer',
    restartFlashCardPopup: 'Voulez-vous vraiment redémarrer la carte mémoire ?',
    favoriteWords: 'mots préférés',
    score: 'Score',
    testNumber: 'Nombre d\'examens',
    disclamer:
        'The Exam Pass Rate is calculated based on your exam results and the data in the system. The index is for reference only.',
    goodToGo:
        'C\'est bon d\'y aller !!! Vous êtes totalement prêt à participer à l\'examen.',
    keepGoing: 'Continue. Vous êtes presque prêt à participer à l\'examen.',
    notEnough:
        'Vous n\'avez pas encore terminé au moins 10 examens. Pour obtenir le taux de réussite le plus précis, veuillez passer quelques examens supplémentaires.',
    examPassRateDetail: 'Statistiques',
    noData: 'Pas de données',
    timeDetail: 'Détails de l\'heure',
    questionDetail: 'Détails des questions',
    eachQuestion: 'Chaque question',
    totalTime: 'Temps total',
    questionDone: 'Question terminée',
    streak: 'Examen réussi de série',
    latestScore: 'Dernier score',
    passedExam: 'Examen réussi',
    quizDone: 'Examens passés',
    avgScore: 'Note moyenne',
    quizFlashCardDetail: 'Détails de l\'examen',
    flashCard: 'Flashcard',
    notReady:
        'Vous n\'êtes pas prêt. Veuillez passer plus de temps à vous entraîner pour un meilleur résultat.',
    examPassRate: 'Taux de réussite aux examens',
    emptyField: 'Le champ ne peut pas être vide',
    notificationDetail: 'reviens faire ton test',
    notification: 'Notification',
    questionrule: 'Les questions du test sont fixes',
    randomRule: 'Les questions sont différentes d\'un examen à l\'autre',
    getSupport: 'Obtenez de l\'aide de notre part',
    premiumVersion: 'Version Premium',
    someTips4U: 'EINIGE TIPPS FÜR SIE',
    yourCode: 'Votre code',
    enterRedeemCode: 'Veuillez entrer votre code d\'échange',
    redeemCode: 'Utiliser le code',
    updateYear: 'mis à jour pour %1',
    fully: 'Pleinement',
    detailedExplain: 'explications détaillées',
    answersWith: 'Répond avec',
    allTheExams: 'tous les examens',
    unlock: 'Ouvrir',
    startTrial: 'Commencer l\'essai gratuit de 3 jours',
    cancelAnyTime: 'Résiliez votre abonnement à tout moment',
    fullyIUpdated: 'Entièrement mis à jour pour %1',
    trialTitle: 'Essai de 3 jours avec accès illimité :',
    sunday: 'Dimanche',
    saturday: 'Samedi',
    friday: 'Vendredi',
    thursday: 'Jeudi',
    wednesday: 'Mercredi',
    tuesday: 'Mardi',
    monday: 'Lundi',
    notificationStatus: 'Rappel d\'étude',
    notificationDays: 'Avis des jours',
    notificationTime: 'Heure de notification',
    expiresToday: 'Expire aujourd\'hui',
    trial: 'Essai',
    dayLeft: 'jour restant',
    otherApp: 'Other Apps',
    randomSet: 'Question aléatoire',
    questionSet: 'Question fixe',
    doing: 'Action',
    premium: 'Premium',
    free: 'Free',
    resultDetail: 'Détail du résulta',
    version: 'Version',
    fail: 'Échouer',
    pass: 'Passer',
    secondShort: 'secondes',
    term: 'Termes',
    support: 'Soutien',
    privacyPolicy: 'Politique de confidentialité',
    termOfUse: 'Conditions d\'utilisation',
    percentageRequireToPass: 'Note de passage',
    plan: 'Plan',
    resetData: 'Réinitialiser les données',
    upgradeToGetFeature: 'Essai gratuit de 3 jours avec la version Premium',
    deleteSuccess: 'Supprimé avec succès',
    confirmDeleteQuestionReview:
        'Voulez-vous vraiment supprimer toutes les données d\'examen des questions ?',
    upgrade: 'Essayez maintenant',
    unlockAllExams: 'Débloquez tous les examens',
    unlockQR: 'Déverrouiller la fonctionnalité Examen des questions',
    premiumFeature: 'Fonction Premium',
    hide: 'Cacher',
    delete: 'Supprimer',
    viewExplanation: 'Voir l\'explication',
    confirmDeleteQuestion:
        'Voulez-vous vraiment supprimer toutes les questions sélectionnées ?',
    yes: 'Oui',
    restorePurchase: 'Restaurer l\'achat',
    thankForPurchase: 'Vous avez déverrouillé la fonctionnalité premium',
    purchase: 'Acheter',
    alreadyPaid: 'Déjà payé?',
    month: 'Mois',
    months: 'Mois',
    total: 'Total',
    monthly: 'Mensuel',
    withExplain: 'Des réponses avec des explications détaillées',
    unlockAllExam: 'Débloquez tous les examens',
    unlockFeature: 'Déverrouiller la fonction',
    accessPremium: 'Passez l\'examen facilement',
    statistics: 'Statistiques',
    noMarkedQuestion: 'Vous n\'avez pas de question marquée',
    noWrongQuestion: 'Vous n\'avez pas de mauvaise question',
    questionReview: 'Question de révision',
    numberWrong: 'Faux : {e} fois',
    markedQuestion: 'Question marquée',
    wrongQuestion: 'Mauvaise question',
    unanswered: 'Non répondu',
    wrong: 'Mauvais',
    correct: 'Corriger',
    totalQuestion: 'Totale: {e} des questions',
    yourName: 'votre nom',
    retryInternet: 'Retenter',
    viewExamList: 'Afficher la liste des examens',
    minutesShort: 'minutes',
    left: 'la gauche',
    numberQuestion: 'des questions',
    reportDetail: 'Détails du rapport',
    numReport: 'Numéro de question',
    cancel: 'Annuler',
    send: 'Envoyer',
    reportSuccess: 'Votre rapport a été envoyé.',
    report: 'Signaler',
    notAnswered: 'Non répondu',
    forceUpdateDialogContent:
        'Une nouvelle mise à jour est disponible. Vous devez mettre à jour cette application pour continuer.',
    rateUs: 'Évaluez nous',
    updateDialogContent:
        'Une nouvelle mise à jour est disponible. Voulez-vous mettre à jour ?',
    noInternet: 'Pas de connexion Internet',
    totalQuizDone: 'Examens passés',
    recentScore: 'Résultat du dernier examen',
    highScore: 'Passes consécutives les plus élevées',
    fontExam: 'Taille de la police de l\'examen',
    averageExamResult: 'Note moyenne à l\'examen',
    serviceSatisfactionRate: 'Taux de satisfaction des services',
    certificationPassRate: 'Taux de réussite à la certification',
    yourMessage: 'Faites-nous savoir comment nous pouvons mieux vous aider?',
    writeUsDirectly: 'Écrivez-nous directement',
    contact: 'Contact',
    callUs: 'Appelez-nous',
    connectUs: 'Connectez-nous',
    quizNumber: 'Ceci est votre numéro d\'examen {e}',
    confirmRetry: 'Êtes-vous prêt à réessayer ?',
    noUserName: 'S\'il vous plaît entrez votre nom',
    noQuizResult: 'Vous n\'avez terminé aucun examen',
    noInfo: 'Aucune information',
    errorDialog: 'Une erreur s\'est produite, veuillez réessayer plus tard',
    error: 'Erreur',
    confirmDoQuiz: 'Êtes-vous prêt à passer l\'examen ?',
    resumeQuizDialog:
        'Vous n\'avez pas terminé un examen, vous voulez continuer ?',
    bookmarked: 'Favoris',
    questionList: 'Liste de questions',
    allQuestion: 'Toutes les questions',
    previous: 'Précédent',
    finish: 'Finir',
    next: 'Suivant',
    questionOf: 'de',
    question: 'Question',
    timeUp: 'Temps est révolu',
    remainTime: 'Temps restant',
    quitQuizDialog: 'Êtes-vous sûr de quitter l\'examen ?',
    timeUpDialog: 'Le temps est écoulé',
    no: 'Non',
    confirmEndQuiz: 'Êtes-vous sûr de terminer l\'examen ?',
    mustSelectAnswer: 'Veuillez sélectionner tout ceux qui s\'appliquent',
    attention: 'Attention!',
    productOfScrumPass: 'Un produit de ScrumPass',
    wellcomeEnterName: 'Veuillez entrer votre nom pour commencer',
    allAns: 'Toutes les réponses',
    save: 'Sauver',
    noLimit: 'Sans limites',
    answered: 'Répondu',
    noAnsSelected: 'Aucune réponse sélectionnée',
    continueQ: 'Continuez',
    time: 'Temps',
    back: 'Arrière',
    intruction: 'Instruction',
    minPercentage: 'Note de passage',
    timeEnd: 'Heure de fin',
    timeStart: 'Heure de début',
    quizDetail: 'Détails',
    userDetail: 'Paramètre',
    noQuizAvaliable: 'Aucun examen disponible',
    minutes: 'minutes',
    timeLeft: 'Temps restant',
    duration: 'Durée',
    numOfQuestion: 'Des questions',
    begin: 'Passer un examen',
    answer: 'Réponse',
    retry: 'Retenter',
    home: 'Écran d\'accueil',
    wrongAns: 'Mauvaises réponses',
    rightAns: 'Bonne réponse',
    marked: 'Questions marquées',
    finishDate: 'Date',
    numHasDone: 'Nombre d\'essais',
    numWrongAns: 'Nombre de mauvaises réponses',
    numRightAns: 'Nombre de bonnes réponses',
    passRequirement: 'Besoin de {e} % de bonnes réponses pour réussir',
    detail: 'Détails du compte',
    resultList: 'Liste de résultats',
    quizList: 'Liste des examens',
    aboutUs: 'À propos de nous',
    scrumguide: 'Guide de mêlée',
    result: 'Résultat d\'éxamen',
    examPractice: 'Passer un examen',
    numFailQuiz: 'Manqué',
    numPassQuiz: 'Passé',
    passPercentage: 'Pourcentage de réussite',
    chooseLang: 'Choisissez la langue',
    lang: 'Langue',
    name: 'Nom',
    hello: 'Bonjour',
    wellcomeTitlePrince2:
        'Bienvenue dans le simulateur d\'examen Prince2 Foundation Exam Simulator',
    wellcomeTitleCissp:
        'Bienvenue dans le simulateur d\'examen CISSP Exam Simulator',
    wellcomeTitleComptiaLinux:
        'Bienvenue dans le simulateur d\'examen CompTIA Linux+ Exam Simulator',
    wellcomeTitleComptiaCloud:
        'Bienvenue dans le simulateur d\'examen CompTIA Cloud+ Exam Simulator',
    wellcomeTitleComptiaProject:
        'Bienvenue dans le simulateur d\'examen CompTIA Project+ Exam Simulator',
    wellcomeTitleComptiaServer:
        'Bienvenue dans le simulateur d\'examen CompTIA Server+ Exam Simulator',
    wellcomeTitleSAFePOPM:
        'Bienvenue dans le simulateur d\'examen SAFe POPM Exam Simulator',
    wellcomeTitleSAFeSSM:
        'Bienvenue dans le simulateur d\'examen SAFe SSM Exam Simulator',
    wellcomeTitleComptiaSecurity:
        'Bienvenue dans le simulateur d\'examen CompTIA Security+ Exam Simulator',
    wellcomeTitleComptiaNetwork:
        'Bienvenue dans le simulateur d\'examen CompTIA Network+ Exam Simulator',
    wellcomeTitleComptiaITF:
        'Bienvenue dans le simulateur d\'examen CompTIA ITF+ Exam Simulator',
    wellcomeTitleComptiaA:
        'Bienvenue dans le simulateur d\'examen CompTIA A+ Exam Simulator',
    wellcomeTitleAwsMlsIos:
        'Bienvenue dans le simulateur d\'examen Machine Learning Exam Simulator',
    wellcomeTitleAwsMls:
        'Bienvenue dans le simulateur d\'examen AWS Machine Learning Specialty Exam Simulator',
    wellcomeTitleAwsSap:
        'Bienvenue dans le simulateur d\'examen AWS Solutions Architect Pro',
    wellcomeTitleAwsDop:
        'Bienvenue dans le simulateur d\'examen AWS DevOps Pro',
    wellcomeTitleItil: 'Bienvenue dans le simulateur d\'examen ITIL Foundation',
    wellcomeTitleAwsDva: 'Bienvenue dans le simulateur d\'examen AWS Developer',
    wellcomeTitleAwsClf: 'Bienvenue dans le simulateur d\'examen AWS Cloud',
    wellcomeTitleAwsSoa: 'Bienvenue dans le simulateur d\'examen AWS SysOps',
    wellcomeTitleRmp: 'Bienvenue dans le simulateur d\'examen PMI-RMP',
    wellcomeTitleAwsSaa: 'Bienvenue dans le simulateur d\'examen AWS-SAA',
    wellcomeTitlePba: 'Bienvenue dans le simulateur d\'examen PMI-PBA',
    wellcomeTitlePfmp: 'Bienvenue dans le simulateur d\'examen PfMP',
    wellcomeTitleEcba: 'Bienvenue dans le simulateur d\'examen ECBA',
    wellcomeTitleISTQB: 'Bienvenue dans le simulateur d\'examen ISTQB',
    wellcomeTitlePgmp: 'Bienvenue dans le simulateur d\'examen PgMP',
    wellcomeTitleCapm: 'Bienvenue dans le simulateur d\'examen CAPM',
    wellcomeTitleCbap: 'Bienvenue dans le simulateur d\'examen CBAP',
    wellcomeTitleCCBA: 'Bienvenue dans le simulateur d\'examen CCBA',
    wellcomeTitlePmp: 'Bienvenue dans le simulateur d\'examen PMP',
    wellcomeTitlePsd: 'Bienvenue dans le simulateur d\'examen PSD',
    wellcomeTitleAcp: 'Bienvenue dans le simulateur d\'examen ACP',
    wellcomeTitlePsk: 'Bienvenue dans le simulateur d\'examen PSK',
    wellcomeTitleSps: 'Bienvenue dans le simulateur d\'examen SPS',
    wellcomeTitlePal: 'Bienvenue dans le simulateur d\'examen PAL-I',
    wellcomeTitlePsm2: 'Bienvenue dans le simulateur d\'examen PSM-II',
    wellcomeTitlePspo2: 'Bienvenue dans le simulateur d\'examen PSPO-II',
    wellcomeTitlePspo: 'Bienvenue dans le simulateur d\'examen PSPO',
    wellcomeTitle: 'Bienvenue dans le simulateur d\'examen PSM',
    appDescriptionPrince2:
        'Test de simulation pour se préparer à votre examen PRINCE2® 7 Foundation Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionCISSP:
        'Test de simulation pour se préparer à votre examen CISSP Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionComptiaLinux:
        'Test de simulation pour se préparer à votre examen CompTIA Linux+ Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionComptiaCloud:
        'Test de simulation pour se préparer à votre examen CompTIA Cloud Essentials+ Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionComptiaProject:
        'Test de simulation pour se préparer à votre examen CompTIA Project+ Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionComptiaServer:
        'Test de simulation pour se préparer à votre examen CompTIA Server+ Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionSAFePOPM:
        'Test de simulation pour se préparer à votre examen SAFe POPM Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionSAFeSSM:
        'Test de simulation pour se préparer à votre examen SAFe SSM Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionComptiaSecurity:
        'Test de simulation pour se préparer à votre examen CompTIA Security+ Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionComptiaNetwork:
        'Test de simulation pour se préparer à votre examen CompTIA Network+ Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionComptiaITF:
        'Test de simulation pour se préparer à votre examen CompTIA ITF+ Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionComptiaA:
        'Test de simulation pour se préparer à votre examen AWS CompTIA A+ Exam. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionAwsMls:
        'Test de simulation pour se préparer à votre examen AWS Machine Learning Specialty. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionAwsSap:
        'Test de simulation pour se préparer à votre examen AWS Solution Architect Pro. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionAwsDop:
        'Test de simulation pour se préparer à votre examen AWS DevOps Pro. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionItil:
        'Test de simulation pour se préparer à votre examen ITIL Foundation. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionAwsDva:
        'Test de simulation pour se préparer à votre examen AWS Developer. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionAwsClf:
        'Test de simulation pour se préparer à votre examen AWS Cloud. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionAwsSoa:
        'Test de simulation pour se préparer à votre examen AWS SysOps. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionRmp:
        'Test de simulation pour se préparer à votre examen PMI-RMP. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionAwsSaa:
        'Test de simulation pour se préparer à votre examen AWS-SAA. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionPba:
        'Test de simulation pour se préparer à votre examen PMI-PBA. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionPfmp:
        'Test de simulation pour se préparer à votre examen PfMP. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionEcba:
        'Test de simulation pour se préparer à votre examen ECBA. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionISTQB:
        'Test de simulation pour se préparer à votre examen ISTQB Foundation. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionPgmp:
        'Test de simulation pour se préparer à votre examen PgMP. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionCapm:
        'Test de simulation pour se préparer à votre examen CAPM. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionCbap:
        'Test de simulation pour se préparer à votre examen CBAP. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionCCBA:
        'Test de simulation pour se préparer à votre examen CCBA. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionPmp:
        'Test de simulation pour se préparer à votre examen PMI-PMP. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionPsd:
        'Test de simulation pour se préparer à votre examen PSD. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionAcp:
        'Test de simulation pour se préparer à votre examen PMI-ACP. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionPsk:
        'Test de simulation pour se préparer à votre examen PSK I. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionSps:
        'Tests de simulation pour vous préparer à votre examen SPS. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionPal:
        'Tests de simulation pour vous préparer à votre examen PAL-I. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionPspo2:
        'Tests de simulation pour vous préparer à votre examen PSPO-II. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionPspo:
        'Tests de simulation pour vous préparer à votre examen PSPO. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appDescriptionPsm2:
        'Simulateur d\'examen de certification Project Management Professional (PMI-PMP)',
    appDescription:
        'Tests de simulation pour vous préparer à votre examen PSM. Utilisez cette application quotidiennement pour améliorer vos connaissances.',
    appTitlePrince2: 'Simulateur d\'examen PRINCE2® 7 Foundation',
    appTitleCissp: 'Simulateur d\'examen CISSP',
    appTitleComptiaLinux: 'Simulateur d\'examen CompTIA Linux+',
    appTitleComptiaCloud: 'Simulateur d\'examen CompTIA Cloud Essentials+',
    appTitleComptiaProject: 'Simulateur d\'examen CompTIA Project+',
    appTitleComptiaServer: 'Simulateur d\'examen CompTIA Server+',
    appTitleSAFePOPM:
        'Simulateur d\'examen SAFe® Product Owner/Product Manager',
    appTitleSAFeSSM: 'Simulateur d\'examen SAFe® Scrum Master',
    appTitleComptiaSecurity: 'Simulateur d\'examen CompTIA Security+',
    appTitleComptiaNetwork: 'Simulateur d\'examen CompTIA Network+',
    appTitleComptiaITF: 'Simulateur d\'examen CompTIA ITF+',
    appTitleComptiaA: 'Simulateur d\'examen CompTIA A+',
    appTitleAwsMls:
        'Machine Learning certifié AWS - Simulateur d\'examen spécialisé',
    appTitleAwsSap:
        'Architecte de solutions certifié AWS - Simulateur d\'examen professionnel',
    appTitleAwsDop:
        'Ingénieur DevOps certifié AWS - Simulateur d\'examen professionnel',
    appTitleItil:
        'Bibliothèque d\'infrastructure des technologies de l\'information Simulateur d\'examen ITIL Foundation',
    appTitleAwsDva:
        'Simulateur d\'examen de certification AWS Certified Developer Associate (AWS Developer)',
    appTitleAwsClf:
        'Simulateur d\'examen de certification AWS Certified Cloud Practitioner (AWS Cloud)',
    appTitleAwsSoa:
        'Administrateur Sysops certifié AWS - Simulateur d\'examen de certification associé (AWS SysOps)',
    appTitleRmp:
        'Simulateur d\'examen de certification PMI Risk Management Professional (PMI RMP)',
    appTitleAwsSaa:
        'Architecte de solutions certifié AWS - Simulateur d\'examen associé SAA-C03',
    appTitlePba:
        'Simulateur d\'examen de certification PMI Professional in Business Analysis (PMI-PBA)',
    appTitlePfmp:
        'Simulateur d\'examen de certification Portfolio Management Professional (PfMP)',
    appTitleEcba:
        'Simulateur d\'examen du certificat d\'entrée en analyse commerciale (ECBA)',
    appTitleISTQB:
        'Niveau Fondation de l\'International Software Testing Qualifications Board (ISTQB)',
    appTitlePgmp:
        'Simulateur d\'examen de certification Program Management Professional (PgMP)',
    appTitleCapm:
        'Simulateur d\'examen de certification Certified Associate in Project Management (CAPM)',
    appTitleCbap:
        'Simulateur d\'examen de certification Certified Business Analysis Professional (CBAP)',
    appTitleCCBA:
        'Die Anwendung unterstützt die Überprüfung und Prüfungsvorbereitung für die Certification of Capability in Business Analysis (CCBA).',
    appTitlePmp:
        'Simulateur d\'examen de certification PMI Agile Certified Practitioner (PMI-ACP)',
    appTitlePsd:
        'Simulateur d\'examen de certification Développeur professionnel Scrum (PSD)',
    appTitleAcp:
        'Simulateur d\'examen de certification PMI Agile Certified Practitioner (PMI-ACP)',
    appTitlePsk:
        'Simulateur d\'examen de certification Scrum professionnel avec Kanban (PSK I)',
    appTitleSps:
        'Simulateur d\'examen de certification Scaled Professional Scrum (SPS)',
    appTitlePal:
        'Simulateur d\'examen de certification Professional Agile Leadership (PAL-I)',
    appTitlePspo2:
        'Simulateur d\'examen de certification de propriétaire de produit professionnel (PSPO-II)',
    appTitlePspo:
        'Simulateur d\'examen de certification de propriétaire de produit professionnel (PSPO)',
    appTitlePsm2:
        'Simulateur d\'examen de certification Professional Scrum Master II (PSM-II)',
    appTitle:
        'Simulateur d\'examen de certification Professional Scrum Master (PSM)',
    appNamePrince2: 'Simulateur d\'examen Prince2 Foundation',
    appNameCissp: 'Simulateur d\'examen CISSP',
    appNameComptiaLinux: 'Simulateur d\'examen CompTIA Linux+',
    appNameComptiaCloud: 'Simulateur d\'examen CompTIA Cloud Essentials+',
    appNameComptiaProject: 'Simulateur d\'examen CompTIA Project+',
    appNameComptiaServer: 'Simulateur d\'examen CompTIA Server+',
    appNameSAFePOPM: 'Simulateur d\'examen SAFe POPM',
    appNameSAFeSSM: 'Simulateur d\'examen SAFe SSM',
    appNameComptiaSecurity: 'Simulateur d\'examen CompTIA Security+',
    appNameComptiaNetwork: 'Simulateur d\'examen CompTIA Network+',
    appNameComptiaITF: 'Simulateur d\'examen CompTIA ITF+',
    appNameComptiaA: 'Simulateur d\'examen CompTIA A+',
    appNameAwsMls: 'Simulateur d\'examen AWS Machine Learning Specialty',
    appNameAwsSap: 'Simulateur d\'examen AWS Solutions Architect Pro',
    appNameAwsDop: 'Simulateur d\'examen AWS DevOps Pro',
    appNameItil: 'Simulateur d\'examen ITIL Foundation',
    appNameAwsDva: 'Simulateur d\'examen AWS Developer',
    appNameAwsClf: 'Simulateur d\'examen AWS Cloud',
    appNameAwsSoa: 'Simulateur d\'examen AWS SysOps',
    appNameRmp: 'Simulateur d\'examen PMI-RMP',
    appNameAwsSaa: 'Simulateur d\'examen AWS Solutions Architect',
    appNamePba: 'Simulateur d\'examen PMI-PBA',
    appNamePfmp: 'Simulateur d\'examen PfMP',
    appNameEcba: 'Simulateur d\'examen ECBA',
    appNameISTQB: 'Simulateur d\'examen ISTQB',
    appNamePgmp: 'Simulateur d\'examen PgMP',
    appNameCapm: 'Simulateur d\'examen CAPM',
    appNameCbap: 'Simulateur d\'examen CBAP',
    appNameCCBA: 'Simulateur d\'examen CCBA',
    appNamePmp: 'Simulateur d\'examen PMP',
    appNamePsd: 'Simulateur d\'examen PSD',
    appNameAcp: 'Simulateur d\'examen ACP',
    appNamePsk: 'Simulateur d\'examen PSK',
    appNameSps: 'Simulateur d\'examen SPS',
    appNamePal: 'Simulateur d\'examen PAL-I',
    appNamePspo2: 'Simulateur d\'examen PSPO-II',
    appNamePsm2: 'Simulateur d\'examen PSM-II',
    appNamePspo: 'Simulateur d\'examen PSPO',
    appName: 'Simulateur d\'examen PSM',
  ),
  const Locale.fromSubtags(languageCode: 'hi'): const AppLocalizationsData(
    finishEarly:
        'यह ठीक है कि आपको एक अच्छा समय नहीं मिला। प्रश्न-यह कौन सा प्रश्न है?',
    redeemCodeError: 'अवैध कोड',
    introduceDescription:
        '<p><b>Common.appName</b> अंतरराष्ट्रीय स्तर पर मान्यता प्राप्त प्रमाणपत्रों की एक विस्तृत श्रृंखला में महारत हासिल करने के लिए आपकी वन-स्टॉप शॉप है। हमारे व्यापक उपकरणों और संसाधनों के साथ, आप <b>अपना ज्ञान बढ़ा सकते हैं</b> और <b>आत्मविश्वास के साथ अपनी परीक्षाओं में सफल हो सकते हैं</b>।</p><br><p><b>व्यक्तिगत सीखने की शुरुआत करें यात्रा:</b></p><p><b>• अभ्यास प्रश्नों के सागर में गोता लगाएँ:</b> सभी आवश्यक पाठ्यक्रम क्षेत्रों को कवर करने के लिए उद्योग विशेषज्ञों द्वारा सावधानीपूर्वक तैयार किए गए अभ्यास प्रश्नों के विशाल संग्रह का अन्वेषण करें। </p><p><b>• सटीकता के साथ अपनी प्रगति को ट्रैक करें:</b> अपने प्रदर्शन की निगरानी करें और विस्तृत परिणाम सारांश और वैयक्तिकृत अंतर्दृष्टि के साथ सुधार के लिए क्षेत्रों की पहचान करें।</p><p><b>• आगे बढ़ें -गहराई से समझ:</b> प्रत्येक विषय की गहन समझ सुनिश्चित करते हुए, प्रत्येक प्रश्न और उत्तर के लिए स्पष्ट स्पष्टीकरण में तल्लीन करें।</p><p><b>• वास्तविक परीक्षा अनुभव का अनुकरण करें:</b> अपने आप को इसमें डुबो दें समय पर मॉक परीक्षा के साथ प्रामाणिक परीक्षा की स्थिति, वास्तविक परीक्षण वातावरण की नकल।</p><p><b>• वक्र से आगे रहें:</b> नियमित रूप से अपडेट किए गए प्रश्न सेट तक पहुंचें जो नवीनतम परीक्षा सामग्री को प्रतिबिंबित करते हैं, यह सुनिश्चित करते हुए। आप हमेशा तैयार रहते हैं।</p><br><p><b>Common.appName के साथ, आपके पास वह सब कुछ है जो आपको अपने अंतर्राष्ट्रीय प्रमाणन लक्ष्यों को हासिल करने के लिए चाहिए।</b></p>',
    introduceTitle:
        'अंतर्राष्ट्रीय प्रमाणन सफलता के आपके प्रवेश द्वार में आपका स्वागत है',
    then: 'डैन',
    emptyExamTips: 'वर्तमान में कोई परीक्षा युक्तियाँ उपलब्ध नहीं हैं',
    afterTrial: 'परीक्षण समाप्त होने के बाद',
    trialDescription:
        '- नि:शुल्क परीक्षण केवल एक बार उपलब्ध है।\n- नि:शुल्क परीक्षण समाप्त होने के बाद सदस्यता स्वचालित रूप से शुरू हो जाती है।\n- आप ऐप स्टोर या प्ले स्टोर में अपनी सदस्यता रद्द कर सकते हैं।\n- यदि सदस्यता समाप्त होने से 24 घंटे पहले रद्द कर दी जाती है वर्तमान उपयोग अवधि में, उपयोग अवधि समाप्त होने के बाद सदस्यता स्वतः समाप्त हो जाएगी।',
    connectServerError:
        'सर्वर से कनेक्ट नहीं हो सका. कृपया बाद में पुन: प्रयास करें',
    hour: 'घंटे',
    hours: 'घंटे',
    noReport: 'आपने कोई रिपोर्ट नहीं भेजी',
    sendResponseSuccess: 'प्रतिक्रिया भेजें सफलता',
    sendResponse: 'प्रतिक्रिया भेजो',
    closeReport: 'रिपोर्ट बंद करें',
    scrumpassResponse: 'स्क्रमपास की प्रतिक्रिया',
    yourResponse: 'आपका जवाब',
    reportContent: 'रिपोर्ट सामग्री',
    closed: 'बंद किया हुआ',
    reviewing: 'की समीक्षा',
    status: 'दर्जा',
    reportedQuestion: 'रिपोर्ट किया गया प्रश्न',
    resultText: 'परीक्षा परीणाम',
    listQuizShowcase2:
        'परीक्षा में प्रश्न निश्चित हैं और बदलते नहीं हैं, जिससे आपको गति बढ़ाने और वास्तविक परीक्षा की तैयारी करने में मदद मिलती है।',
    listQuizShowcase1:
        'हर बार जब आप परीक्षा देते हैं, तो आप विभिन्न प्रश्नों का सामना करेंगे, जो आपके दैनिक अभ्यास और समीक्षा के लिए उपयुक्त हैं।',
    homeShowcase5: 'फ़्लैशकार्ड के साथ जल्दी से सिद्धांत और शब्दावली सीखें।',
    homeShowcase4:
        'आपके द्वारा गलत किए गए प्रश्नों की सूची और पूर्ण स्पष्टीकरण के साथ चिह्नित करके अधिक प्रभावी ढंग से समीक्षा करने में आपकी सहायता करता है।',
    homeShowcase3: 'आपके परीक्षा परिणामों का इतिहास।',
    homeShowcase2: 'उपलब्ध परीक्षणों की सूची जो आप ले सकते हैं।',
    homeShowcase1:
        'परीक्षा पास करने की आपकी क्षमता आपके परीक्षा लेने के इतिहास पर आधारित है। आप अपने विस्तृत परिणाम आँकड़े देखने के लिए क्लिक कर सकते हैं।',
    descriptionContent:
        'एग्जाम सिम्युलेटर मोबाइल ऐप्स का एक अग्रणी डेवलपर है जिसे शिक्षार्थियों को उनकी अंतरराष्ट्रीय प्रमाणन परीक्षाओं में सफल होने के लिए सशक्त बनाने के लिए डिज़ाइन किया गया है। हम एक भावुक टीम हैं, जो एक लचीला और प्रभावी शिक्षण मंच बनाने के लिए समर्पित है जो आपकी व्यक्तिगत जरूरतों को पूरा करता है।\n\nहम शिक्षार्थियों को उनके अंतरराष्ट्रीय प्रमाणन लक्ष्यों को प्राप्त करने के लिए सशक्त बनाने के बारे में भावुक हैं। परीक्षा की तैयारी के लिए डिज़ाइन किए गए मोबाइल ऐप्स के एक अग्रणी डेवलपर के रूप में, हम पेशेवर प्रमाणपत्रों के माध्यम से अपने करियर को आगे बढ़ाने के इच्छुक व्यक्तियों की अनूठी चुनौतियों और आकांक्षाओं को समझते हैं।\n\nहमारे साथ जुड़ें और वैयक्तिकृत शिक्षण टूल, गहन स्पष्टीकरण और की दुनिया को अनलॉक करें। परीक्षा के दिन अपने ज्ञान और आत्मविश्वास को अधिकतम करने के लिए लक्षित अभ्यास।',
    aboutUsContent:
        'Common.appName ऐप्लिकेशन में आपका स्वागत है। आप प्रोफेशनल स्क्रम मास्टर परीक्षा के अपने बुनियादी ज्ञान में सुधार और सत्यापन कर सकते हैं। Common.appName के साथ आप अपने फुर्तीले और स्क्रम ज्ञान में सुधार कर सकते हैं और अपनी पहली कोशिश scrum.org या PMI प्रमाणन को पारित करने में आपकी सहायता कर सकते हैं। \n\nऐप पर सभी परीक्षण सामग्री स्क्रमपास विशेषज्ञों द्वारा क्यूरेट की जाती है। जैसे ही आप परीक्षा देते हैं, आप सीधे ऐप पर अपनी प्रगति का सारांश ट्रैक कर पाएंगे। आपको पता चलेगा कि आपको अपने स्क्रम ज्ञान में सुधार करने और स्क्रम पेशेवर बनने के लिए किन क्षेत्रों में सुधार करने की आवश्यकता है। \n\nविभिन्न क्षेत्रों में स्क्रम परीक्षा दें \nअपना परिणाम और प्रदर्शन सारांश ट्रैक करें \nप्रत्येक प्रश्न और उत्तर पर विस्तृत स्पष्टीकरण \nसमयबद्ध इंटरफ़ेस के साथ वास्तविक परीक्षा शैली पूर्ण मॉक परीक्षा \nइसमें बड़ी संख्या में प्रश्न शामिल हैं जो पूरे पाठ्यक्रम क्षेत्र को कवर करते हैं। \n\nहम वास्तविक परीक्षा सामग्री के साथ प्रश्न सेट को नियमित रूप से अपडेट करते हैं। यदि आप बार-बार हमारी स्क्रम परीक्षा देते हैं और सभी परीक्षाओं में कम से कम 85% प्राप्त करने का लक्ष्य रखते हैं, तो आप आसानी से वास्तविक स्क्रम परीक्षा उत्तीर्ण कर लेंगे। Common.appName डाउनलोड करें और स्क्रम परीक्षा से परिचित हों। आप कहीं से भी और किसी भी समय हमारे एप्लिकेशन का उपयोग कर सकते हैं।',
    copyToClipboard: 'क्लिपबोर्ड पर नकल',
    androidLink:
        'एंड्रॉइड ऐप डाउनलोड करने के लिए लिंक: https://play.google.com/store/apps/details?id=',
    iosLink: 'आईओएस ऐप डाउनलोड करने के लिए लिंक: https://apps.apple.com/app/id',
    getCertificate:
        '[app_name] के साथ [certificate_name] प्रमाणपत्र प्राप्त करना आसान है।',
    shareApp: 'अपने दोस्त के साथ शेयर ऐप',
    tutorialTap: 'परिभाषा देखने के लिए टैप करें',
    tutorialSwipeRight: 'अगले शब्द पर दाईं ओर स्वाइप करें',
    tutorialSwipeLeft: 'शब्द छोड़ने के लिए बाएं स्वाइप करें',
    flashcardEndText:
        'आपने अभी-अभी {e1} शर्तें सीखी हैं! शेष {e2} में महारत हासिल करने के लिए अभ्यास करते रहें।',
    learnedEverything: 'आपने सब कुछ सीख लिया है',
    halfWayThere: 'तुम आधे रास्ते में हो!',
    congratulations: 'बधाई हो!',
    continueStudy: 'अध्ययन जारी रखें',
    restart: 'पुनर्प्रारंभ करें',
    restartFlashCardPopup:
        'क्या आप वाकई फ़्लैशकार्ड पुनः प्रारंभ करना चाहते हैं?',
    favoriteWords: 'पसंदीदा शब्द',
    score: 'अंक',
    testNumber: 'परीक्षाओं की संख्या',
    disclamer:
        'परीक्षा पास दर की गणना आपके परीक्षा परिणामों और सिस्टम में डेटा के आधार पर की जाती है। सूचकांक केवल संदर्भ के लिए है।',
    goodToGo:
        'जाना अच्छा है!!! आप परीक्षा में भाग लेने के लिए पूरी तरह से तैयार हैं।',
    keepGoing: 'बढ़ा चल। आप परीक्षा में भाग लेने के लिए काफी तैयार हैं।',
    notEnough:
        'आपने अभी तक कम से कम 10 परीक्षाएं पूरी नहीं की हैं. सबसे सटीक पास दर प्राप्त करने के लिए, कृपया कुछ अतिरिक्त परीक्षाएं दें।',
    examPassRateDetail: 'आंकड़े',
    noData: 'कोई डेटा नहीं',
    timeDetail: 'समय विवरण',
    questionDetail: 'प्रश्न का विवरण',
    eachQuestion: 'प्रत्येक प्रश्न',
    totalTime: 'कुल समय',
    questionDone: 'प्रश्न हो गया',
    streak: 'स्ट्रीक उत्तीर्ण परीक्षा',
    latestScore: 'नवीनतम स्कोर',
    passedExam: 'उत्तीर्ण परीक्षा',
    quizDone: 'परीक्षा ली',
    avgScore: 'मध्यम स्कोर',
    quizFlashCardDetail: 'परीक्षा विवरण',
    flashCard: 'फ़्लैश कार्ड',
    notReady:
        'आप तैयार नहीं हैं। बेहतर परिणाम के लिए अभ्यास में अधिक समय व्यतीत करें।',
    examPassRate: 'परीक्षा पास दर',
    emptyField: 'फ़ील्ड खाली नहीं हो सकता',
    notificationDetail: 'वापस आओ और अपना परीक्षण करो',
    notification: 'अधिसूचना',
    questionrule: 'परीक्षण में प्रश्न निश्चित हैं',
    randomRule: 'परीक्षा में प्रश्न अलग-अलग होते हैं',
    getSupport: 'हमसे समर्थन प्राप्त करें',
    premiumVersion: 'प्रीमियम संस्करण',
    someTips4U: 'आपके लिए कुछ टिप्स',
    yourCode: 'तुम्हारा कोड',
    enterRedeemCode: 'कृपया अपना रिडीम कोड दर्ज करें',
    redeemCode: 'रीडीम कोड',
    updateYear: '%1 के लिए अपडेट किया गया',
    fully: 'पूरी तरह से',
    detailedExplain: 'विस्तृत व्याख्या',
    answersWith: 'उत्तर के साथ',
    allTheExams: 'सभी परीक्षा',
    unlock: 'अनलॉक',
    startTrial: '3-दिवसीय नि:शुल्क परीक्षण प्रारंभ करें',
    cancelAnyTime: 'किसी भी समय अपनी सदस्यता रद्द करें',
    fullyIUpdated: '%1 के लिए पूरी तरह से अद्यतन',
    trialTitle: 'असीमित पहुंच के साथ 3-दिवसीय परीक्षण:',
    sunday: 'रविवार',
    saturday: 'शनिवार',
    friday: 'शुक्रवार',
    thursday: 'गुरुवार',
    wednesday: 'बुधवार',
    tuesday: 'मंगलवार',
    monday: 'सोमवार',
    notificationStatus: 'अध्ययन अनुस्मारक',
    notificationDays: 'दिनों की सूचना',
    notificationTime: 'अधिसूचना का समय',
    expiresToday: 'आज समाप्त हो रहा है',
    trial: 'परीक्षण',
    dayLeft: 'दिन बचा है',
    otherApp: 'Other Apps',
    randomSet: 'रैंडम प्रश्',
    questionSet: 'निश्चित प्रश्न',
    doing: 'करते हुए',
    premium: 'Premium',
    free: 'Free',
    resultDetail: 'विवरण',
    version: 'संस्करण',
    fail: 'विफल',
    pass: 'रास्ता',
    secondShort: 'सेकेंड',
    term: 'शर्तें',
    support: 'सहायता',
    privacyPolicy: 'गोपनीयता नीति',
    termOfUse: 'उपयोग की शर्तें',
    percentageRequireToPass: 'पास होने योग्य नम्बर',
    plan: 'योजना',
    resetData: 'डेटा रीसेट करें',
    upgradeToGetFeature: 'प्रीमियम संस्करण के साथ 3-दिवसीय निःशुल्क परीक्षण',
    deleteSuccess: 'सफलतापूर्वक हटा दिया गया',
    confirmDeleteQuestionReview:
        'क्या आप वाकई सभी प्रश्न समीक्षा डेटा हटाना चाहते हैं?',
    upgrade: 'अब कोशिश करो',
    unlockAllExams: 'सभी परीक्षाओं को अनलॉक करें',
    unlockQR: 'अनलॉक सुविधा प्रश्न समीक्षा',
    premiumFeature: 'प्रीमियम फ़ीचर',
    hide: 'छिपाना',
    delete: 'मिटाना',
    viewExplanation: 'स्पष्टीकरण देखें',
    confirmDeleteQuestion:
        'क्या आप वाकई सभी चयनित प्रश्नों को हटाना चाहते हैं?',
    yes: 'हां',
    restorePurchase: 'पुनःस्थापन क्रय',
    thankForPurchase: 'आपने प्रीमियम सुविधा को अनलॉक कर दिया है',
    purchase: 'खरीदना',
    alreadyPaid: 'पहले से भुगतान किया हुआ?',
    month: 'महीना',
    months: 'महीने',
    total: 'कुल',
    monthly: 'महीने के',
    withExplain: 'विस्तृत स्पष्टीकरण के साथ उत्तर',
    unlockAllExam: 'सभी परीक्षाओं को अनलॉक करें',
    unlockFeature: 'अनलॉक सुविधा',
    accessPremium: 'आसानी से परीक्षा पास करें',
    statistics: 'आंकड़े',
    noMarkedQuestion: 'आपके पास कोई चिह्नित प्रश्न नहीं है',
    noWrongQuestion: 'आपका कोई गलत सवाल नहीं है',
    questionReview: 'समीक्षा प्रश्न',
    numberWrong: 'गलत: {ई} टाइम्स',
    markedQuestion: 'चिह्नित प्रश्न',
    wrongQuestion: 'गलत प्रश्न',
    unanswered: 'जवाब नहीं दिया',
    wrong: 'गलत',
    correct: 'सही',
    totalQuestion: 'कुल: {e} प्रश्न',
    yourName: 'तुम्हारा  नाम',
    retryInternet: 'पुन: प्रयास करें',
    viewExamList: 'परीक्षा सूची देखें',
    minutesShort: 'मिनट',
    left: 'बाएं',
    numberQuestion: 'प्रशन',
    reportDetail: 'रिपोर्ट विवरण',
    numReport: 'प्रश्न संख्या',
    cancel: 'रद्द करना',
    send: 'भेजना',
    reportSuccess: 'आपकी रिपोर्ट भेज दी गई है।',
    report: 'प्रतिवेदन',
    notAnswered: 'जवाब नहीं दिया',
    forceUpdateDialogContent:
        'नया अपडेट उपलब्ध है। जारी रखने के लिए आपको इस एप्लिकेशन को अपडेट करना होगा।',
    rateUs: 'हमें रेटिंग दें',
    updateDialogContent: 'नया अपडेट उपलब्ध है। क्या आप अपडेट करना चाहते हैं?',
    noInternet: 'कोई इंटरनेट कनेक्शन नहीं',
    totalQuizDone: 'परीक्षा ली',
    recentScore: 'अंतिम परीक्षा स्कोर',
    highScore: 'उच्चतम लगातार पास',
    fontExam: 'परीक्षा फ़ॉन्ट आकार',
    averageExamResult: 'औसत परीक्षा स्कोर',
    serviceSatisfactionRate: 'सेवा संतुष्टि दर',
    certificationPassRate: 'प्रमाणन पास दर',
    yourMessage: 'हमें बताएं कि हम आपकी बेहतर सहायता कैसे कर सकते हैं?',
    writeUsDirectly: 'हमें सीधे लिखें',
    contact: 'संपर्क करना',
    callUs: 'हमें कॉल करें',
    connectUs: 'हमसे जुड़ें',
    quizNumber: 'यह आपका परीक्षा क्रमांक है {e}',
    confirmRetry: 'क्या आप पुनः प्रयास करने के लिए तैयार हैं?',
    noUserName: 'अपना नाम दर्ज करें',
    noQuizResult: 'आपने कोई परीक्षा पूरी नहीं की',
    noInfo: 'कोई जानकारी नहीं',
    errorDialog: 'एक त्रुटि घटित हुई है, कृपया बाद में पुन: प्रयास करें',
    error: 'गलती',
    confirmDoQuiz: 'क्या आप परीक्षा करने के लिए तैयार हैं?',
    resumeQuizDialog:
        'आपने कोई परीक्षा पूरी नहीं की, फिर भी जारी रखना चाहते हैं?',
    bookmarked: 'बुकमार्क',
    questionList: 'प्रश्न सूची',
    allQuestion: 'सभी प्रश्न',
    previous: 'पिछला',
    finish: 'खत्म करना',
    next: 'अगला',
    questionOf: 'का',
    question: 'प्रश्न',
    timeUp: 'समय समाप्त हो गया है',
    remainTime: 'शेष समय',
    quitQuizDialog: 'क्या आप निश्चित रूप से परीक्षा छोड़ना चाहते हैं?',
    timeUpDialog: 'समय समाप्त',
    no: 'नहीं',
    confirmEndQuiz: 'क्या आप निश्चित रूप से परीक्षा समाप्त करना चाहते हैं?',
    mustSelectAnswer: 'लो भी लागू होते हैं, उन सभी को चुनें',
    attention: 'ध्यान!',
    productOfScrumPass: 'स्क्रमपास का एक उत्पाद',
    wellcomeEnterName: 'शुरू करने के लिए कृपया अपना नाम दर्ज करें',
    allAns: 'सभी उत्तर',
    save: 'बचाना',
    noLimit: 'कोई सीमा नहीं',
    answered: 'उत्तर',
    noAnsSelected: 'कोई उत्तर नहीं चुना गया',
    continueQ: 'जारी रखना',
    time: 'समय',
    back: 'पीछे',
    intruction: 'अनुदेश',
    minPercentage: 'सर्वाधिक गणना',
    timeEnd: 'अंत समय',
    timeStart: 'समय शुरू',
    quizDetail: 'विवरण',
    userDetail: 'स्थापना',
    noQuizAvaliable: 'कोई परीक्षा उपलब्ध नहीं',
    minutes: 'मिनट',
    timeLeft: 'शेष समय',
    duration: 'अवधि',
    numOfQuestion: 'प्रशन',
    begin: 'परीक्षा लें',
    answer: 'उत्तर',
    retry: 'पुन: प्रयास करें',
    home: 'होम स्क्रीन',
    wrongAns: 'गलत उत्तर',
    rightAns: 'सही उत्तर',
    marked: 'चिह्नित प्रश्न',
    finishDate: 'दिनांक',
    numHasDone: 'कोशिशों की संख्या',
    numWrongAns: 'गलत उत्तरों की संख्या',
    numRightAns: 'सही उत्तरों की संख्या',
    passRequirement: 'पास होने के लिए {e}% सही चाहिए',
    detail: 'खाता विवरण',
    resultList: 'परिणाम सूची',
    quizList: 'परीक्षा सूची',
    aboutUs: 'हमारे बारे में',
    scrumguide: 'स्क्रम गाइड',
    result: 'परीक्षा परीणाम',
    examPractice: 'परीक्षा लें',
    numFailQuiz: 'असफल',
    numPassQuiz: 'उत्तीर्ण',
    passPercentage: 'पास प्रतिशत',
    chooseLang: 'भाषा चुनें',
    lang: 'भाषा',
    name: 'नाम',
    hello: 'नमस्ते',
    wellcomeTitlePrince2:
        'Prince2 Foundation Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleCissp:
        'CISSP Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleComptiaLinux:
        'CompTIA Linux+ Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleComptiaCloud:
        'CompTIA Cloud+ Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleComptiaProject:
        'CompTIA Project+ Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleComptiaServer:
        'CompTIA Server+ Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleSAFePOPM:
        'SAFe POPM Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleSAFeSSM:
        'SAFe SSM Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleComptiaSecurity:
        'CompTIA Security+ Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleComptiaNetwork:
        'CompTIA Network+ Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleComptiaITF:
        'CompTIA ITF+ Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleComptiaA:
        'CompTIA A+ Exam Simulator परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleAwsMlsIos:
        'Machine Learning Exam Simulator Pro परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleAwsMls:
        'AWS Machine Learning Specialty Exam Simulator Pro परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleAwsSap:
        'AWS Solutions Architect Pro परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleAwsDop: 'AWS DevOps Pro परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleItil: 'ITIL Foundation परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleAwsDva: 'AWS Developer परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleAwsClf: 'AWS Cloud परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleAwsSoa: 'AWS SysOps परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleRmp: 'PMI-RMP परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleAwsSaa: 'AWS-SAA परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitlePba: 'PMI-PBA परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitlePfmp: 'PfMP परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleEcba: 'ECBA परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleISTQB: 'ISTQB परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitlePgmp: 'PgMP परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleCapm: 'CAPM परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleCbap: 'CBAP परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleCCBA: 'CCBA परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitlePmp: 'PMP परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitlePsd: 'PSD परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleAcp: 'ACP परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitlePsk: 'PSK परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitleSps: 'SPS परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitlePal: 'PAL-I परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitlePsm2: 'PSM-II परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitlePspo2: 'PSPO-II परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitlePspo: 'PSPO परीक्षा सिम्युलेटर में आपका स्वागत है',
    wellcomeTitle: 'PSM परीक्षा सिम्युलेटर में आपका स्वागत है',
    appDescriptionPrince2:
        'आपके PRINCE2® 7 फाउंडेशन परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionCISSP:
        'आपकी सीआईएसएसपी परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionComptiaLinux:
        'आपके CompTIA Linux+ परीक्षा के लिए तैयार होने के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionComptiaCloud:
        'आपके CompTIA क्लाउड एसेंशियल+ परीक्षा के लिए तैयार होने के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionComptiaProject:
        'आपके CompTIA प्रोजेक्ट+ परीक्षा के लिए तैयार होने के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionComptiaServer:
        'आपके CompTIA सर्वर+ परीक्षा के लिए तैयार होने के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionSAFePOPM:
        'आपकी SAFe POPM परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionSAFeSSM:
        'आपकी SAFe SSM परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionComptiaSecurity:
        'आपकी CompTIA सुरक्षा+ परीक्षा के लिए तैयार होने के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionComptiaNetwork:
        'आपके CompTIA नेटवर्क+ परीक्षा के लिए तैयार होने के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionComptiaITF:
        'आपकी CompTIA ITF+ परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionComptiaA:
        'आपकी CompTIA A+ परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionAwsMls:
        'आपकी AWS मशीन लर्निंग स्पेशलिटी परीक्षा की तैयारी के लिए सिमुलेशन टेस्ट। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionAwsSap:
        'आपके AWS सॉल्यूशन आर्किटेक्ट प्रो परीक्षा की तैयारी के लिए सिमुलेशन टेस्ट। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionAwsDop:
        'आपके AWS DevOps Pro परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionItil:
        'आपके आईटीआईएल फाउंडेशन परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionAwsDva:
        'आपकी AWS डेवलपर परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionAwsClf:
        'आपके AWS क्लाउड परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionAwsSoa:
        'आपकी AWS SysOps परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionRmp:
        'अपनी पीएमआई-आरएमपी परीक्षा के लिए तैयार होने के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionAwsSaa:
        'आपकी AWS-SAA परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionPba:
        'आपकी पीएमआई-पीबीए परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionPfmp:
        'अपनी पीएफएमपी परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionEcba:
        'आपकी ईसीबीए परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionISTQB:
        'आपके ISTQB फाउंडेशन परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionPgmp:
        'आपकी पीजीएमपी परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionCapm:
        'आपकी सीएपीएम परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionCbap:
        'आपकी सीबीएपी परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए इस ऐप का प्रतिदिन उपयोग करें।',
    appDescriptionCCBA:
        'एप्लिकेशन व्यवसाय विश्लेषण (सीसीबीए) में आपकी क्षमता के प्रमाणन के लिए समीक्षा और परीक्षा की तैयारी का समर्थन करता है। अपने ज्ञान को बेहतर बनाने के लिए दैनिक क्विज में भाग लें।',
    appDescriptionPmp:
        'आपकी पीएमआई-पीएमपी परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए प्रतिदिन इस ऐप का उपयोग करें।',
    appDescriptionPsd:
        'अपने PSD परीक्षा के लिए तैयार होने के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए प्रतिदिन इस ऐप का उपयोग करें।',
    appDescriptionAcp:
        'आपकी पीएमआई-एसीपी परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए प्रतिदिन इस ऐप का उपयोग करें।',
    appDescriptionPsk:
        'आपकी पीएसके I परीक्षा की तैयारी के लिए सिमुलेशन टेस्ट। अपने ज्ञान को बेहतर बनाने के लिए प्रतिदिन इस ऐप का उपयोग करें।',
    appDescriptionSps: '',
    appDescriptionPal:
        'आपकी SPS परीक्षा की तैयारी के लिए अनुकार परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए प्रतिदिन इस ऐप का उपयोग करें।',
    appDescriptionPspo2:
        'अपने PSPO-II परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए प्रतिदिन इस ऐप का उपयोग करें।',
    appDescriptionPspo:
        'अपने PSPO परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए प्रतिदिन इस ऐप का उपयोग करें।',
    appDescriptionPsm2:
        'अपने PSM-II परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए प्रतिदिन इस ऐप का उपयोग करें।',
    appDescription:
        'अपने PSM परीक्षा की तैयारी के लिए सिमुलेशन परीक्षण। अपने ज्ञान को बेहतर बनाने के लिए प्रतिदिन इस ऐप का उपयोग करें।',
    appTitlePrince2: 'PRINCE2® 7 फाउंडेशन परीक्षा सिम्युलेटर',
    appTitleCissp: 'सीआईएसएसपी परीक्षा सिम्युलेटर',
    appTitleComptiaLinux: 'CompTIA Linux+ परीक्षा सिम्युलेटर',
    appTitleComptiaCloud: 'CompTIA क्लाउड एसेंशियल+ परीक्षा सिम्युलेटर',
    appTitleComptiaProject: 'CompTIA प्रोजेक्ट+ परीक्षा सिम्युलेटर',
    appTitleComptiaServer: 'CompTIA सर्वर+ परीक्षा सिम्युलेटर',
    appTitleSAFePOPM: 'SAFe® उत्पाद स्वामी/उत्पाद प्रबंधक परीक्षा सिम्युलेटर',
    appTitleSAFeSSM: 'SAFe® स्क्रम मास्टर परीक्षा सिम्युलेटर',
    appTitleComptiaSecurity: 'CompTIA सुरक्षा+ परीक्षा सिम्युलेटर',
    appTitleComptiaNetwork: 'CompTIA नेटवर्क+ परीक्षा सिम्युलेटर',
    appTitleComptiaITF: 'CompTIA ITF+ परीक्षा सिम्युलेटर',
    appTitleComptiaA: 'CompTIA A+ परीक्षा सिम्युलेटर',
    appTitleAwsMls: 'AWS प्रमाणित मशीन लर्निंग - विशेष परीक्षा सिम्युलेटर',
    appTitleAwsSap:
        'एडब्ल्यूएस प्रमाणित समाधान वास्तुकार - व्यावसायिक परीक्षा सिम्युलेटर',
    appTitleAwsDop:
        'AWS प्रमाणित DevOps इंजीनियर - व्यावसायिक परीक्षा सिम्युलेटर',
    appTitleItil:
        'सूचना प्रौद्योगिकी इन्फ्रास्ट्रक्चर लाइब्रेरी आईटीआईएल फाउंडेशन परीक्षा सिम्युलेटर',
    appTitleAwsDva:
        'AWS प्रमाणित डेवलपर एसोसिएट (AWS डेवलपर) प्रमाणन परीक्षा सिम्युलेटर',
    appTitleAwsClf:
        'AWS प्रमाणित क्लाउड प्रैक्टिशनर (AWS क्लाउड) प्रमाणन परीक्षा सिम्युलेटर',
    appTitleAwsSoa:
        'AWS प्रमाणित SysOps प्रशासक - एसोसिएट (AWS SysOps) प्रमाणन परीक्षा सिम्युलेटर',
    appTitleRmp:
        'पीएमआई जोखिम प्रबंधन पेशेवर (पीएमआई आरएमपी) प्रमाणन परीक्षा सिम्युलेटर',
    appTitleAwsSaa:
        'AWS प्रमाणित समाधान आर्किटेक्ट - एसोसिएट SAA-C03 परीक्षा सिम्युलेटर',
    appTitlePba:
        'बिजनेस एनालिसिस में पीएमआई प्रोफेशनल (पीएमआई-पीबीए) प्रमाणन परीक्षा सिम्युलेटर',
    appTitlePfmp:
        'पोर्टफोलियो मैनेजमेंट प्रोफेशनल (पीएफएमपी) प्रमाणन परीक्षा सिम्युलेटर',
    appTitleEcba:
        'बिजनेस एनालिसिस (ईसीबीए) परीक्षा सिम्युलेटर में प्रवेश प्रमाणपत्र',
    appTitleISTQB:
        'अंतर्राष्ट्रीय सॉफ्टवेयर परीक्षण योग्यता बोर्ड (ISTQB) फाउंडेशन स्तर',
    appTitlePgmp:
        'पीएमआई के प्रोग्राम मैनेजमेंट प्रोफेशनल (PgMP) प्रमाणन परीक्षा सिम्युलेटर अर्जित करें',
    appTitleCapm:
        'परियोजना प्रबंधन में प्रमाणित एसोसिएट (CAPM) प्रमाणन परीक्षा सिम्युलेटर',
    appTitleCbap:
        'प्रमाणित व्यवसाय विश्लेषण पेशेवर (CBAP) प्रमाणन परीक्षा सिम्युलेटर',
    appTitleCCBA:
        'आवेदन व्यापार विश्लेषण (CCBA) में क्षमता के प्रमाणन के लिए समीक्षा और परीक्षा की तैयारी का समर्थन करता है।',
    appTitlePmp: 'परियोजना प्रबंधन पेशेवर (PMI-PMP) प्रमाणन परीक्षा सिम्युलेटर',
    appTitlePsd: 'व्यावसायिक स्क्रम डेवलपर (PSD) प्रमाणन परीक्षा सिम्युलेटर',
    appTitleAcp:
        'पीएमआई एजाइल सर्टिफाइड प्रैक्टिशनर (PMI-ACP) प्रमाणन परीक्षा सिम्युलेटर',
    appTitlePsk:
        'कानबन (PSK I) प्रमाणन परीक्षा सिम्युलेटर के साथ व्यावसायिक स्क्रम',
    appTitleSps: 'स्केल्ड प्रोफेशनल स्क्रम (SPS) प्रमाणन परीक्षा सिम्युलेटर',
    appTitlePal: 'व्यावसायिक चुस्त नेतृत्व (PAL-I) प्रमाणन परीक्षा सिम्युलेटर',
    appTitlePspo2:
        'व्यावसायिक उत्पाद स्वामी (PSPO-II) प्रमाणन परीक्षा सिम्युलेटर',
    appTitlePspo: 'व्यावसायिक उत्पाद स्वामी (PSPO) प्रमाणन परीक्षा सिम्युलेटर',
    appTitlePsm2:
        'व्यावसायिक स्क्रम मास्टर (PSM-II) प्रमाणन परीक्षा सिम्युलेटर',
    appTitle: 'व्यावसायिक स्क्रम मास्टर (PSM) प्रमाणन परीक्षा सिम्युलेटर',
    appNamePrince2: 'Prince2 Foundation Exam Simulator',
    appNameCissp: 'CISSP Exam Simulator',
    appNameComptiaLinux: 'CompTIA Linux+ Exam Simulator',
    appNameComptiaCloud: 'CompTIA Cloud Essentials+ Exam Simulator',
    appNameComptiaProject: 'CompTIA Project+ Exam Simulator',
    appNameComptiaServer: 'CompTIA Server+ Exam Simulator',
    appNameSAFePOPM: 'SAFe POPM Exam Simulator',
    appNameSAFeSSM: 'SAFe SSM Exam Simulator',
    appNameComptiaSecurity: 'CompTIA Security+ Exam Simulator',
    appNameComptiaNetwork: 'CompTIA Network+ Exam Simulator',
    appNameComptiaITF: 'CompTIA ITF+ Exam Simulator',
    appNameComptiaA: 'CompTIA A+ Exam Simulator',
    appNameAwsMls: 'AWS Machine Learning Specialty Exam Simulator',
    appNameAwsSap: 'AWS DevOps Pro Exam Simulator',
    appNameAwsDop: 'AWS DevOps Pro Exam Simulator',
    appNameItil: 'ITIL Foundation Exam Simulator',
    appNameAwsDva: 'AWS Developer Exam Simulator',
    appNameAwsClf: 'AWS Cloud Exam Simulator',
    appNameAwsSoa: 'AWS SysOps Exam Simulator',
    appNameRmp: 'PMI-RMP Exam Simulator',
    appNameAwsSaa: 'AWS Solutions Architect Exam Simulator',
    appNamePba: 'PMI-PBA Exam Simulator',
    appNamePfmp: 'PfMP Exam Simulator',
    appNameEcba: 'ECBA Exam Simulator',
    appNameISTQB: 'ISTQB Exam Simulator',
    appNamePgmp: 'PgMP Exam Simulator',
    appNameCapm: 'CAPM Exam Simulator',
    appNameCbap: 'CBAP Exam Simulator',
    appNameCCBA: 'CCBA Exam Simulator',
    appNamePmp: 'PMP Exam Simulator',
    appNamePsd: 'PSD Exam Simulator',
    appNameAcp: 'ACP Exam Simulator',
    appNamePsk: 'PSK Exam Simulator',
    appNameSps: 'SPS Exam Simulator',
    appNamePal: 'PAL-I Exam Simulator',
    appNamePspo2: 'PSPO-II Exam Simulator',
    appNamePsm2: 'PSM-II Exam Simulator',
    appNamePspo: 'PSPO Exam Simulator',
    appName: 'PSM Exam Simulator',
  ),
};

class AppLocalizationsData {
  const AppLocalizationsData({
    required this.finishEarly,
    required this.redeemCodeError,
    required this.introduceDescription,
    required this.introduceTitle,
    required this.then,
    required this.emptyExamTips,
    required this.afterTrial,
    required this.trialDescription,
    required this.connectServerError,
    required this.hour,
    required this.hours,
    required this.noReport,
    required this.sendResponseSuccess,
    required this.sendResponse,
    required this.closeReport,
    required this.scrumpassResponse,
    required this.yourResponse,
    required this.reportContent,
    required this.closed,
    required this.reviewing,
    required this.status,
    required this.reportedQuestion,
    required this.resultText,
    required this.listQuizShowcase2,
    required this.listQuizShowcase1,
    required this.homeShowcase5,
    required this.homeShowcase4,
    required this.homeShowcase3,
    required this.homeShowcase2,
    required this.homeShowcase1,
    required this.descriptionContent,
    required this.aboutUsContent,
    required this.copyToClipboard,
    required this.androidLink,
    required this.iosLink,
    required this.getCertificate,
    required this.shareApp,
    required this.tutorialTap,
    required this.tutorialSwipeRight,
    required this.tutorialSwipeLeft,
    required this.flashcardEndText,
    required this.learnedEverything,
    required this.halfWayThere,
    required this.congratulations,
    required this.continueStudy,
    required this.restart,
    required this.restartFlashCardPopup,
    required this.favoriteWords,
    required this.score,
    required this.testNumber,
    required this.disclamer,
    required this.goodToGo,
    required this.keepGoing,
    required this.notEnough,
    required this.examPassRateDetail,
    required this.noData,
    required this.timeDetail,
    required this.questionDetail,
    required this.eachQuestion,
    required this.totalTime,
    required this.questionDone,
    required this.streak,
    required this.latestScore,
    required this.passedExam,
    required this.quizDone,
    required this.avgScore,
    required this.quizFlashCardDetail,
    required this.flashCard,
    required this.notReady,
    required this.examPassRate,
    required this.emptyField,
    required this.notificationDetail,
    required this.notification,
    required this.questionrule,
    required this.randomRule,
    required this.getSupport,
    required this.premiumVersion,
    required this.someTips4U,
    required this.yourCode,
    required this.enterRedeemCode,
    required this.redeemCode,
    required this.updateYear,
    required this.fully,
    required this.detailedExplain,
    required this.answersWith,
    required this.allTheExams,
    required this.unlock,
    required this.startTrial,
    required this.cancelAnyTime,
    required this.fullyIUpdated,
    required this.trialTitle,
    required this.sunday,
    required this.saturday,
    required this.friday,
    required this.thursday,
    required this.wednesday,
    required this.tuesday,
    required this.monday,
    required this.notificationStatus,
    required this.notificationDays,
    required this.notificationTime,
    required this.expiresToday,
    required this.trial,
    required this.dayLeft,
    required this.otherApp,
    required this.randomSet,
    required this.questionSet,
    required this.doing,
    required this.premium,
    required this.free,
    required this.resultDetail,
    required this.version,
    required this.fail,
    required this.pass,
    required this.secondShort,
    required this.term,
    required this.support,
    required this.privacyPolicy,
    required this.termOfUse,
    required this.percentageRequireToPass,
    required this.plan,
    required this.resetData,
    required this.upgradeToGetFeature,
    required this.deleteSuccess,
    required this.confirmDeleteQuestionReview,
    required this.upgrade,
    required this.unlockAllExams,
    required this.unlockQR,
    required this.premiumFeature,
    required this.hide,
    required this.delete,
    required this.viewExplanation,
    required this.confirmDeleteQuestion,
    required this.yes,
    required this.restorePurchase,
    required this.thankForPurchase,
    required this.purchase,
    required this.alreadyPaid,
    required this.month,
    required this.months,
    required this.total,
    required this.monthly,
    required this.withExplain,
    required this.unlockAllExam,
    required this.unlockFeature,
    required this.accessPremium,
    required this.statistics,
    required this.noMarkedQuestion,
    required this.noWrongQuestion,
    required this.questionReview,
    required this.numberWrong,
    required this.markedQuestion,
    required this.wrongQuestion,
    required this.unanswered,
    required this.wrong,
    required this.correct,
    required this.totalQuestion,
    required this.yourName,
    required this.retryInternet,
    required this.viewExamList,
    required this.minutesShort,
    required this.left,
    required this.numberQuestion,
    required this.reportDetail,
    required this.numReport,
    required this.cancel,
    required this.send,
    required this.reportSuccess,
    required this.report,
    required this.notAnswered,
    required this.forceUpdateDialogContent,
    required this.rateUs,
    required this.updateDialogContent,
    required this.noInternet,
    required this.totalQuizDone,
    required this.recentScore,
    required this.highScore,
    required this.fontExam,
    required this.averageExamResult,
    required this.serviceSatisfactionRate,
    required this.certificationPassRate,
    required this.yourMessage,
    required this.writeUsDirectly,
    required this.contact,
    required this.callUs,
    required this.connectUs,
    required this.quizNumber,
    required this.confirmRetry,
    required this.noUserName,
    required this.noQuizResult,
    required this.noInfo,
    required this.errorDialog,
    required this.error,
    required this.confirmDoQuiz,
    required this.resumeQuizDialog,
    required this.bookmarked,
    required this.questionList,
    required this.allQuestion,
    required this.previous,
    required this.finish,
    required this.next,
    required this.questionOf,
    required this.question,
    required this.timeUp,
    required this.remainTime,
    required this.quitQuizDialog,
    required this.timeUpDialog,
    required this.no,
    required this.confirmEndQuiz,
    required this.mustSelectAnswer,
    required this.attention,
    required this.productOfScrumPass,
    required this.wellcomeEnterName,
    required this.allAns,
    required this.save,
    required this.noLimit,
    required this.answered,
    required this.noAnsSelected,
    required this.continueQ,
    required this.time,
    required this.back,
    required this.intruction,
    required this.minPercentage,
    required this.timeEnd,
    required this.timeStart,
    required this.quizDetail,
    required this.userDetail,
    required this.noQuizAvaliable,
    required this.minutes,
    required this.timeLeft,
    required this.duration,
    required this.numOfQuestion,
    required this.begin,
    required this.answer,
    required this.retry,
    required this.home,
    required this.wrongAns,
    required this.rightAns,
    required this.marked,
    required this.finishDate,
    required this.numHasDone,
    required this.numWrongAns,
    required this.numRightAns,
    required this.passRequirement,
    required this.detail,
    required this.resultList,
    required this.quizList,
    required this.aboutUs,
    required this.scrumguide,
    required this.result,
    required this.examPractice,
    required this.numFailQuiz,
    required this.numPassQuiz,
    required this.passPercentage,
    required this.chooseLang,
    required this.lang,
    required this.name,
    required this.hello,
    required this.wellcomeTitlePrince2,
    required this.wellcomeTitleCissp,
    required this.wellcomeTitleComptiaLinux,
    required this.wellcomeTitleComptiaCloud,
    required this.wellcomeTitleComptiaProject,
    required this.wellcomeTitleComptiaServer,
    required this.wellcomeTitleSAFePOPM,
    required this.wellcomeTitleSAFeSSM,
    required this.wellcomeTitleComptiaSecurity,
    required this.wellcomeTitleComptiaNetwork,
    required this.wellcomeTitleComptiaITF,
    required this.wellcomeTitleComptiaA,
    required this.wellcomeTitleAwsMlsIos,
    required this.wellcomeTitleAwsMls,
    required this.wellcomeTitleAwsSap,
    required this.wellcomeTitleAwsDop,
    required this.wellcomeTitleItil,
    required this.wellcomeTitleAwsDva,
    required this.wellcomeTitleAwsClf,
    required this.wellcomeTitleAwsSoa,
    required this.wellcomeTitleRmp,
    required this.wellcomeTitleAwsSaa,
    required this.wellcomeTitlePba,
    required this.wellcomeTitlePfmp,
    required this.wellcomeTitleEcba,
    required this.wellcomeTitleISTQB,
    required this.wellcomeTitlePgmp,
    required this.wellcomeTitleCapm,
    required this.wellcomeTitleCbap,
    required this.wellcomeTitleCCBA,
    required this.wellcomeTitlePmp,
    required this.wellcomeTitlePsd,
    required this.wellcomeTitleAcp,
    required this.wellcomeTitlePsk,
    required this.wellcomeTitleSps,
    required this.wellcomeTitlePal,
    required this.wellcomeTitlePsm2,
    required this.wellcomeTitlePspo2,
    required this.wellcomeTitlePspo,
    required this.wellcomeTitle,
    required this.appDescriptionPrince2,
    required this.appDescriptionCISSP,
    required this.appDescriptionComptiaLinux,
    required this.appDescriptionComptiaCloud,
    required this.appDescriptionComptiaProject,
    required this.appDescriptionComptiaServer,
    required this.appDescriptionSAFePOPM,
    required this.appDescriptionSAFeSSM,
    required this.appDescriptionComptiaSecurity,
    required this.appDescriptionComptiaNetwork,
    required this.appDescriptionComptiaITF,
    required this.appDescriptionComptiaA,
    required this.appDescriptionAwsMls,
    required this.appDescriptionAwsSap,
    required this.appDescriptionAwsDop,
    required this.appDescriptionItil,
    required this.appDescriptionAwsDva,
    required this.appDescriptionAwsClf,
    required this.appDescriptionAwsSoa,
    required this.appDescriptionRmp,
    required this.appDescriptionAwsSaa,
    required this.appDescriptionPba,
    required this.appDescriptionPfmp,
    required this.appDescriptionEcba,
    required this.appDescriptionISTQB,
    required this.appDescriptionPgmp,
    required this.appDescriptionCapm,
    required this.appDescriptionCbap,
    required this.appDescriptionCCBA,
    required this.appDescriptionPmp,
    required this.appDescriptionPsd,
    required this.appDescriptionAcp,
    required this.appDescriptionPsk,
    required this.appDescriptionSps,
    required this.appDescriptionPal,
    required this.appDescriptionPspo2,
    required this.appDescriptionPspo,
    required this.appDescriptionPsm2,
    required this.appDescription,
    required this.appTitlePrince2,
    required this.appTitleCissp,
    required this.appTitleComptiaLinux,
    required this.appTitleComptiaCloud,
    required this.appTitleComptiaProject,
    required this.appTitleComptiaServer,
    required this.appTitleSAFePOPM,
    required this.appTitleSAFeSSM,
    required this.appTitleComptiaSecurity,
    required this.appTitleComptiaNetwork,
    required this.appTitleComptiaITF,
    required this.appTitleComptiaA,
    required this.appTitleAwsMls,
    required this.appTitleAwsSap,
    required this.appTitleAwsDop,
    required this.appTitleItil,
    required this.appTitleAwsDva,
    required this.appTitleAwsClf,
    required this.appTitleAwsSoa,
    required this.appTitleRmp,
    required this.appTitleAwsSaa,
    required this.appTitlePba,
    required this.appTitlePfmp,
    required this.appTitleEcba,
    required this.appTitleISTQB,
    required this.appTitlePgmp,
    required this.appTitleCapm,
    required this.appTitleCbap,
    required this.appTitleCCBA,
    required this.appTitlePmp,
    required this.appTitlePsd,
    required this.appTitleAcp,
    required this.appTitlePsk,
    required this.appTitleSps,
    required this.appTitlePal,
    required this.appTitlePspo2,
    required this.appTitlePspo,
    required this.appTitlePsm2,
    required this.appTitle,
    required this.appNamePrince2,
    required this.appNameCissp,
    required this.appNameComptiaLinux,
    required this.appNameComptiaCloud,
    required this.appNameComptiaProject,
    required this.appNameComptiaServer,
    required this.appNameSAFePOPM,
    required this.appNameSAFeSSM,
    required this.appNameComptiaSecurity,
    required this.appNameComptiaNetwork,
    required this.appNameComptiaITF,
    required this.appNameComptiaA,
    required this.appNameAwsMls,
    required this.appNameAwsSap,
    required this.appNameAwsDop,
    required this.appNameItil,
    required this.appNameAwsDva,
    required this.appNameAwsClf,
    required this.appNameAwsSoa,
    required this.appNameRmp,
    required this.appNameAwsSaa,
    required this.appNamePba,
    required this.appNamePfmp,
    required this.appNameEcba,
    required this.appNameISTQB,
    required this.appNamePgmp,
    required this.appNameCapm,
    required this.appNameCbap,
    required this.appNameCCBA,
    required this.appNamePmp,
    required this.appNamePsd,
    required this.appNameAcp,
    required this.appNamePsk,
    required this.appNameSps,
    required this.appNamePal,
    required this.appNamePspo2,
    required this.appNamePsm2,
    required this.appNamePspo,
    required this.appName,
  });

  final String finishEarly;
  final String redeemCodeError;
  final String introduceDescription;
  final String introduceTitle;
  final String then;
  final String emptyExamTips;
  final String afterTrial;
  final String trialDescription;
  final String connectServerError;
  final String hour;
  final String hours;
  final String noReport;
  final String sendResponseSuccess;
  final String sendResponse;
  final String closeReport;
  final String scrumpassResponse;
  final String yourResponse;
  final String reportContent;
  final String closed;
  final String reviewing;
  final String status;
  final String reportedQuestion;
  final String resultText;
  final String listQuizShowcase2;
  final String listQuizShowcase1;
  final String homeShowcase5;
  final String homeShowcase4;
  final String homeShowcase3;
  final String homeShowcase2;
  final String homeShowcase1;
  final String descriptionContent;
  final String aboutUsContent;
  final String copyToClipboard;
  final String androidLink;
  final String iosLink;
  final String getCertificate;
  final String shareApp;
  final String tutorialTap;
  final String tutorialSwipeRight;
  final String tutorialSwipeLeft;
  final String flashcardEndText;
  final String learnedEverything;
  final String halfWayThere;
  final String congratulations;
  final String continueStudy;
  final String restart;
  final String restartFlashCardPopup;
  final String favoriteWords;
  final String score;
  final String testNumber;
  final String disclamer;
  final String goodToGo;
  final String keepGoing;
  final String notEnough;
  final String examPassRateDetail;
  final String noData;
  final String timeDetail;
  final String questionDetail;
  final String eachQuestion;
  final String totalTime;
  final String questionDone;
  final String streak;
  final String latestScore;
  final String passedExam;
  final String quizDone;
  final String avgScore;
  final String quizFlashCardDetail;
  final String flashCard;
  final String notReady;
  final String examPassRate;
  final String emptyField;
  final String notificationDetail;
  final String notification;
  final String questionrule;
  final String randomRule;
  final String getSupport;
  final String premiumVersion;
  final String someTips4U;
  final String yourCode;
  final String enterRedeemCode;
  final String redeemCode;
  final String updateYear;
  final String fully;
  final String detailedExplain;
  final String answersWith;
  final String allTheExams;
  final String unlock;
  final String startTrial;
  final String cancelAnyTime;
  final String fullyIUpdated;
  final String trialTitle;
  final String sunday;
  final String saturday;
  final String friday;
  final String thursday;
  final String wednesday;
  final String tuesday;
  final String monday;
  final String notificationStatus;
  final String notificationDays;
  final String notificationTime;
  final String expiresToday;
  final String trial;
  final String dayLeft;
  final String otherApp;
  final String randomSet;
  final String questionSet;
  final String doing;
  final String premium;
  final String free;
  final String resultDetail;
  final String version;
  final String fail;
  final String pass;
  final String secondShort;
  final String term;
  final String support;
  final String privacyPolicy;
  final String termOfUse;
  final String percentageRequireToPass;
  final String plan;
  final String resetData;
  final String upgradeToGetFeature;
  final String deleteSuccess;
  final String confirmDeleteQuestionReview;
  final String upgrade;
  final String unlockAllExams;
  final String unlockQR;
  final String premiumFeature;
  final String hide;
  final String delete;
  final String viewExplanation;
  final String confirmDeleteQuestion;
  final String yes;
  final String restorePurchase;
  final String thankForPurchase;
  final String purchase;
  final String alreadyPaid;
  final String month;
  final String months;
  final String total;
  final String monthly;
  final String withExplain;
  final String unlockAllExam;
  final String unlockFeature;
  final String accessPremium;
  final String statistics;
  final String noMarkedQuestion;
  final String noWrongQuestion;
  final String questionReview;
  final String numberWrong;
  final String markedQuestion;
  final String wrongQuestion;
  final String unanswered;
  final String wrong;
  final String correct;
  final String totalQuestion;
  final String yourName;
  final String retryInternet;
  final String viewExamList;
  final String minutesShort;
  final String left;
  final String numberQuestion;
  final String reportDetail;
  final String numReport;
  final String cancel;
  final String send;
  final String reportSuccess;
  final String report;
  final String notAnswered;
  final String forceUpdateDialogContent;
  final String rateUs;
  final String updateDialogContent;
  final String noInternet;
  final String totalQuizDone;
  final String recentScore;
  final String highScore;
  final String fontExam;
  final String averageExamResult;
  final String serviceSatisfactionRate;
  final String certificationPassRate;
  final String yourMessage;
  final String writeUsDirectly;
  final String contact;
  final String callUs;
  final String connectUs;
  final String quizNumber;
  final String confirmRetry;
  final String noUserName;
  final String noQuizResult;
  final String noInfo;
  final String errorDialog;
  final String error;
  final String confirmDoQuiz;
  final String resumeQuizDialog;
  final String bookmarked;
  final String questionList;
  final String allQuestion;
  final String previous;
  final String finish;
  final String next;
  final String questionOf;
  final String question;
  final String timeUp;
  final String remainTime;
  final String quitQuizDialog;
  final String timeUpDialog;
  final String no;
  final String confirmEndQuiz;
  final String mustSelectAnswer;
  final String attention;
  final String productOfScrumPass;
  final String wellcomeEnterName;
  final String allAns;
  final String save;
  final String noLimit;
  final String answered;
  final String noAnsSelected;
  final String continueQ;
  final String time;
  final String back;
  final String intruction;
  final String minPercentage;
  final String timeEnd;
  final String timeStart;
  final String quizDetail;
  final String userDetail;
  final String noQuizAvaliable;
  final String minutes;
  final String timeLeft;
  final String duration;
  final String numOfQuestion;
  final String begin;
  final String answer;
  final String retry;
  final String home;
  final String wrongAns;
  final String rightAns;
  final String marked;
  final String finishDate;
  final String numHasDone;
  final String numWrongAns;
  final String numRightAns;
  final String passRequirement;
  final String detail;
  final String resultList;
  final String quizList;
  final String aboutUs;
  final String scrumguide;
  final String result;
  final String examPractice;
  final String numFailQuiz;
  final String numPassQuiz;
  final String passPercentage;
  final String chooseLang;
  final String lang;
  final String name;
  final String hello;
  final String wellcomeTitlePrince2;
  final String wellcomeTitleCissp;
  final String wellcomeTitleComptiaLinux;
  final String wellcomeTitleComptiaCloud;
  final String wellcomeTitleComptiaProject;
  final String wellcomeTitleComptiaServer;
  final String wellcomeTitleSAFePOPM;
  final String wellcomeTitleSAFeSSM;
  final String wellcomeTitleComptiaSecurity;
  final String wellcomeTitleComptiaNetwork;
  final String wellcomeTitleComptiaITF;
  final String wellcomeTitleComptiaA;
  final String wellcomeTitleAwsMlsIos;
  final String wellcomeTitleAwsMls;
  final String wellcomeTitleAwsSap;
  final String wellcomeTitleAwsDop;
  final String wellcomeTitleItil;
  final String wellcomeTitleAwsDva;
  final String wellcomeTitleAwsClf;
  final String wellcomeTitleAwsSoa;
  final String wellcomeTitleRmp;
  final String wellcomeTitleAwsSaa;
  final String wellcomeTitlePba;
  final String wellcomeTitlePfmp;
  final String wellcomeTitleEcba;
  final String wellcomeTitleISTQB;
  final String wellcomeTitlePgmp;
  final String wellcomeTitleCapm;
  final String wellcomeTitleCbap;
  final String wellcomeTitleCCBA;
  final String wellcomeTitlePmp;
  final String wellcomeTitlePsd;
  final String wellcomeTitleAcp;
  final String wellcomeTitlePsk;
  final String wellcomeTitleSps;
  final String wellcomeTitlePal;
  final String wellcomeTitlePsm2;
  final String wellcomeTitlePspo2;
  final String wellcomeTitlePspo;
  final String wellcomeTitle;
  final String appDescriptionPrince2;
  final String appDescriptionCISSP;
  final String appDescriptionComptiaLinux;
  final String appDescriptionComptiaCloud;
  final String appDescriptionComptiaProject;
  final String appDescriptionComptiaServer;
  final String appDescriptionSAFePOPM;
  final String appDescriptionSAFeSSM;
  final String appDescriptionComptiaSecurity;
  final String appDescriptionComptiaNetwork;
  final String appDescriptionComptiaITF;
  final String appDescriptionComptiaA;
  final String appDescriptionAwsMls;
  final String appDescriptionAwsSap;
  final String appDescriptionAwsDop;
  final String appDescriptionItil;
  final String appDescriptionAwsDva;
  final String appDescriptionAwsClf;
  final String appDescriptionAwsSoa;
  final String appDescriptionRmp;
  final String appDescriptionAwsSaa;
  final String appDescriptionPba;
  final String appDescriptionPfmp;
  final String appDescriptionEcba;
  final String appDescriptionISTQB;
  final String appDescriptionPgmp;
  final String appDescriptionCapm;
  final String appDescriptionCbap;
  final String appDescriptionCCBA;
  final String appDescriptionPmp;
  final String appDescriptionPsd;
  final String appDescriptionAcp;
  final String appDescriptionPsk;
  final String appDescriptionSps;
  final String appDescriptionPal;
  final String appDescriptionPspo2;
  final String appDescriptionPspo;
  final String appDescriptionPsm2;
  final String appDescription;
  final String appTitlePrince2;
  final String appTitleCissp;
  final String appTitleComptiaLinux;
  final String appTitleComptiaCloud;
  final String appTitleComptiaProject;
  final String appTitleComptiaServer;
  final String appTitleSAFePOPM;
  final String appTitleSAFeSSM;
  final String appTitleComptiaSecurity;
  final String appTitleComptiaNetwork;
  final String appTitleComptiaITF;
  final String appTitleComptiaA;
  final String appTitleAwsMls;
  final String appTitleAwsSap;
  final String appTitleAwsDop;
  final String appTitleItil;
  final String appTitleAwsDva;
  final String appTitleAwsClf;
  final String appTitleAwsSoa;
  final String appTitleRmp;
  final String appTitleAwsSaa;
  final String appTitlePba;
  final String appTitlePfmp;
  final String appTitleEcba;
  final String appTitleISTQB;
  final String appTitlePgmp;
  final String appTitleCapm;
  final String appTitleCbap;
  final String appTitleCCBA;
  final String appTitlePmp;
  final String appTitlePsd;
  final String appTitleAcp;
  final String appTitlePsk;
  final String appTitleSps;
  final String appTitlePal;
  final String appTitlePspo2;
  final String appTitlePspo;
  final String appTitlePsm2;
  final String appTitle;
  final String appNamePrince2;
  final String appNameCissp;
  final String appNameComptiaLinux;
  final String appNameComptiaCloud;
  final String appNameComptiaProject;
  final String appNameComptiaServer;
  final String appNameSAFePOPM;
  final String appNameSAFeSSM;
  final String appNameComptiaSecurity;
  final String appNameComptiaNetwork;
  final String appNameComptiaITF;
  final String appNameComptiaA;
  final String appNameAwsMls;
  final String appNameAwsSap;
  final String appNameAwsDop;
  final String appNameItil;
  final String appNameAwsDva;
  final String appNameAwsClf;
  final String appNameAwsSoa;
  final String appNameRmp;
  final String appNameAwsSaa;
  final String appNamePba;
  final String appNamePfmp;
  final String appNameEcba;
  final String appNameISTQB;
  final String appNamePgmp;
  final String appNameCapm;
  final String appNameCbap;
  final String appNameCCBA;
  final String appNamePmp;
  final String appNamePsd;
  final String appNameAcp;
  final String appNamePsk;
  final String appNameSps;
  final String appNamePal;
  final String appNamePspo2;
  final String appNamePsm2;
  final String appNamePspo;
  final String appName;
  factory AppLocalizationsData.fromJson(Map<String, Object?> map) =>
      AppLocalizationsData(
        finishEarly: map['finishEarly']! as String,
        redeemCodeError: map['redeemCodeError']! as String,
        introduceDescription: map['introduceDescription']! as String,
        introduceTitle: map['introduceTitle']! as String,
        then: map['then']! as String,
        emptyExamTips: map['emptyExamTips']! as String,
        afterTrial: map['afterTrial']! as String,
        trialDescription: map['trialDescription']! as String,
        connectServerError: map['connectServerError']! as String,
        hour: map['hour']! as String,
        hours: map['hours']! as String,
        noReport: map['noReport']! as String,
        sendResponseSuccess: map['sendResponseSuccess']! as String,
        sendResponse: map['sendResponse']! as String,
        closeReport: map['closeReport']! as String,
        scrumpassResponse: map['scrumpassResponse']! as String,
        yourResponse: map['yourResponse']! as String,
        reportContent: map['reportContent']! as String,
        closed: map['closed']! as String,
        reviewing: map['reviewing']! as String,
        status: map['status']! as String,
        reportedQuestion: map['reportedQuestion']! as String,
        resultText: map['resultText']! as String,
        listQuizShowcase2: map['listQuizShowcase2']! as String,
        listQuizShowcase1: map['listQuizShowcase1']! as String,
        homeShowcase5: map['homeShowcase5']! as String,
        homeShowcase4: map['homeShowcase4']! as String,
        homeShowcase3: map['homeShowcase3']! as String,
        homeShowcase2: map['homeShowcase2']! as String,
        homeShowcase1: map['homeShowcase1']! as String,
        descriptionContent: map['descriptionContent']! as String,
        aboutUsContent: map['aboutUsContent']! as String,
        copyToClipboard: map['copyToClipboard']! as String,
        androidLink: map['androidLink']! as String,
        iosLink: map['iosLink']! as String,
        getCertificate: map['getCertificate']! as String,
        shareApp: map['shareApp']! as String,
        tutorialTap: map['tutorialTap']! as String,
        tutorialSwipeRight: map['tutorialSwipeRight']! as String,
        tutorialSwipeLeft: map['tutorialSwipeLeft']! as String,
        flashcardEndText: map['flashcardEndText']! as String,
        learnedEverything: map['learnedEverything']! as String,
        halfWayThere: map['halfWayThere']! as String,
        congratulations: map['congratulations']! as String,
        continueStudy: map['continueStudy']! as String,
        restart: map['restart']! as String,
        restartFlashCardPopup: map['restartFlashCardPopup']! as String,
        favoriteWords: map['favoriteWords']! as String,
        score: map['score']! as String,
        testNumber: map['testNumber']! as String,
        disclamer: map['disclamer']! as String,
        goodToGo: map['goodToGo']! as String,
        keepGoing: map['keepGoing']! as String,
        notEnough: map['notEnough']! as String,
        examPassRateDetail: map['examPassRateDetail']! as String,
        noData: map['noData']! as String,
        timeDetail: map['timeDetail']! as String,
        questionDetail: map['questionDetail']! as String,
        eachQuestion: map['eachQuestion']! as String,
        totalTime: map['totalTime']! as String,
        questionDone: map['questionDone']! as String,
        streak: map['streak']! as String,
        latestScore: map['latestScore']! as String,
        passedExam: map['passedExam']! as String,
        quizDone: map['quizDone']! as String,
        avgScore: map['avgScore']! as String,
        quizFlashCardDetail: map['quizFlashCardDetail']! as String,
        flashCard: map['flashCard']! as String,
        notReady: map['notReady']! as String,
        examPassRate: map['examPassRate']! as String,
        emptyField: map['emptyField']! as String,
        notificationDetail: map['notificationDetail']! as String,
        notification: map['notification']! as String,
        questionrule: map['questionrule']! as String,
        randomRule: map['randomRule']! as String,
        getSupport: map['getSupport']! as String,
        premiumVersion: map['premiumVersion']! as String,
        someTips4U: map['someTips4U']! as String,
        yourCode: map['yourCode']! as String,
        enterRedeemCode: map['enterRedeemCode']! as String,
        redeemCode: map['redeemCode']! as String,
        updateYear: map['updateYear']! as String,
        fully: map['fully']! as String,
        detailedExplain: map['detailedExplain']! as String,
        answersWith: map['answersWith']! as String,
        allTheExams: map['allTheExams']! as String,
        unlock: map['unlock']! as String,
        startTrial: map['startTrial']! as String,
        cancelAnyTime: map['cancelAnyTime']! as String,
        fullyIUpdated: map['fullyIUpdated']! as String,
        trialTitle: map['trialTitle']! as String,
        sunday: map['sunday']! as String,
        saturday: map['saturday']! as String,
        friday: map['friday']! as String,
        thursday: map['thursday']! as String,
        wednesday: map['wednesday']! as String,
        tuesday: map['tuesday']! as String,
        monday: map['monday']! as String,
        notificationStatus: map['notificationStatus']! as String,
        notificationDays: map['notificationDays']! as String,
        notificationTime: map['notificationTime']! as String,
        expiresToday: map['expiresToday']! as String,
        trial: map['trial']! as String,
        dayLeft: map['dayLeft']! as String,
        otherApp: map['otherApp']! as String,
        randomSet: map['randomSet']! as String,
        questionSet: map['questionSet']! as String,
        doing: map['doing']! as String,
        premium: map['premium']! as String,
        free: map['free']! as String,
        resultDetail: map['resultDetail']! as String,
        version: map['version']! as String,
        fail: map['fail']! as String,
        pass: map['pass']! as String,
        secondShort: map['secondShort']! as String,
        term: map['term']! as String,
        support: map['support']! as String,
        privacyPolicy: map['privacyPolicy']! as String,
        termOfUse: map['termOfUse']! as String,
        percentageRequireToPass: map['percentageRequireToPass']! as String,
        plan: map['plan']! as String,
        resetData: map['resetData']! as String,
        upgradeToGetFeature: map['upgradeToGetFeature']! as String,
        deleteSuccess: map['deleteSuccess']! as String,
        confirmDeleteQuestionReview:
            map['confirmDeleteQuestionReview']! as String,
        upgrade: map['upgrade']! as String,
        unlockAllExams: map['unlockAllExams']! as String,
        unlockQR: map['unlockQR']! as String,
        premiumFeature: map['premiumFeature']! as String,
        hide: map['hide']! as String,
        delete: map['delete']! as String,
        viewExplanation: map['viewExplanation']! as String,
        confirmDeleteQuestion: map['confirmDeleteQuestion']! as String,
        yes: map['yes']! as String,
        restorePurchase: map['restorePurchase']! as String,
        thankForPurchase: map['thankForPurchase']! as String,
        purchase: map['purchase']! as String,
        alreadyPaid: map['alreadyPaid']! as String,
        month: map['month']! as String,
        months: map['months']! as String,
        total: map['total']! as String,
        monthly: map['monthly']! as String,
        withExplain: map['withExplain']! as String,
        unlockAllExam: map['unlockAllExam']! as String,
        unlockFeature: map['unlockFeature']! as String,
        accessPremium: map['accessPremium']! as String,
        statistics: map['statistics']! as String,
        noMarkedQuestion: map['noMarkedQuestion']! as String,
        noWrongQuestion: map['noWrongQuestion']! as String,
        questionReview: map['questionReview']! as String,
        numberWrong: map['numberWrong']! as String,
        markedQuestion: map['markedQuestion']! as String,
        wrongQuestion: map['wrongQuestion']! as String,
        unanswered: map['unanswered']! as String,
        wrong: map['wrong']! as String,
        correct: map['correct']! as String,
        totalQuestion: map['totalQuestion']! as String,
        yourName: map['yourName']! as String,
        retryInternet: map['retryInternet']! as String,
        viewExamList: map['viewExamList']! as String,
        minutesShort: map['minutesShort']! as String,
        left: map['left']! as String,
        numberQuestion: map['numberQuestion']! as String,
        reportDetail: map['reportDetail']! as String,
        numReport: map['numReport']! as String,
        cancel: map['cancel']! as String,
        send: map['send']! as String,
        reportSuccess: map['reportSuccess']! as String,
        report: map['report']! as String,
        notAnswered: map['notAnswered']! as String,
        forceUpdateDialogContent: map['forceUpdateDialogContent']! as String,
        rateUs: map['rateUs']! as String,
        updateDialogContent: map['updateDialogContent']! as String,
        noInternet: map['noInternet']! as String,
        totalQuizDone: map['totalQuizDone']! as String,
        recentScore: map['recentScore']! as String,
        highScore: map['highScore']! as String,
        fontExam: map['fontExam']! as String,
        averageExamResult: map['averageExamResult']! as String,
        serviceSatisfactionRate: map['serviceSatisfactionRate']! as String,
        certificationPassRate: map['certificationPassRate']! as String,
        yourMessage: map['yourMessage']! as String,
        writeUsDirectly: map['writeUsDirectly']! as String,
        contact: map['contact']! as String,
        callUs: map['callUs']! as String,
        connectUs: map['connectUs']! as String,
        quizNumber: map['quizNumber']! as String,
        confirmRetry: map['confirmRetry']! as String,
        noUserName: map['noUserName']! as String,
        noQuizResult: map['noQuizResult']! as String,
        noInfo: map['noInfo']! as String,
        errorDialog: map['errorDialog']! as String,
        error: map['error']! as String,
        confirmDoQuiz: map['confirmDoQuiz']! as String,
        resumeQuizDialog: map['resumeQuizDialog']! as String,
        bookmarked: map['bookmarked']! as String,
        questionList: map['questionList']! as String,
        allQuestion: map['allQuestion']! as String,
        previous: map['previous']! as String,
        finish: map['finish']! as String,
        next: map['next']! as String,
        questionOf: map['questionOf']! as String,
        question: map['question']! as String,
        timeUp: map['timeUp']! as String,
        remainTime: map['remainTime']! as String,
        quitQuizDialog: map['quitQuizDialog']! as String,
        timeUpDialog: map['timeUpDialog']! as String,
        no: map['no']! as String,
        confirmEndQuiz: map['confirmEndQuiz']! as String,
        mustSelectAnswer: map['mustSelectAnswer']! as String,
        attention: map['attention']! as String,
        productOfScrumPass: map['productOfScrumPass']! as String,
        wellcomeEnterName: map['wellcomeEnterName']! as String,
        allAns: map['allAns']! as String,
        save: map['save']! as String,
        noLimit: map['noLimit']! as String,
        answered: map['answered']! as String,
        noAnsSelected: map['noAnsSelected']! as String,
        continueQ: map['continueQ']! as String,
        time: map['time']! as String,
        back: map['back']! as String,
        intruction: map['intruction']! as String,
        minPercentage: map['minPercentage']! as String,
        timeEnd: map['timeEnd']! as String,
        timeStart: map['timeStart']! as String,
        quizDetail: map['quizDetail']! as String,
        userDetail: map['userDetail']! as String,
        noQuizAvaliable: map['noQuizAvaliable']! as String,
        minutes: map['minutes']! as String,
        timeLeft: map['timeLeft']! as String,
        duration: map['duration']! as String,
        numOfQuestion: map['numOfQuestion']! as String,
        begin: map['begin']! as String,
        answer: map['answer']! as String,
        retry: map['retry']! as String,
        home: map['home']! as String,
        wrongAns: map['wrongAns']! as String,
        rightAns: map['rightAns']! as String,
        marked: map['marked']! as String,
        finishDate: map['finishDate']! as String,
        numHasDone: map['numHasDone']! as String,
        numWrongAns: map['numWrongAns']! as String,
        numRightAns: map['numRightAns']! as String,
        passRequirement: map['passRequirement']! as String,
        detail: map['detail']! as String,
        resultList: map['resultList']! as String,
        quizList: map['quizList']! as String,
        aboutUs: map['aboutUs']! as String,
        scrumguide: map['scrumguide']! as String,
        result: map['result']! as String,
        examPractice: map['examPractice']! as String,
        numFailQuiz: map['numFailQuiz']! as String,
        numPassQuiz: map['numPassQuiz']! as String,
        passPercentage: map['passPercentage']! as String,
        chooseLang: map['chooseLang']! as String,
        lang: map['lang']! as String,
        name: map['name']! as String,
        hello: map['hello']! as String,
        wellcomeTitlePrince2: map['wellcomeTitlePrince2']! as String,
        wellcomeTitleCissp: map['wellcomeTitleCissp']! as String,
        wellcomeTitleComptiaLinux: map['wellcomeTitleComptiaLinux']! as String,
        wellcomeTitleComptiaCloud: map['wellcomeTitleComptiaCloud']! as String,
        wellcomeTitleComptiaProject:
            map['wellcomeTitleComptiaProject']! as String,
        wellcomeTitleComptiaServer:
            map['wellcomeTitleComptiaServer']! as String,
        wellcomeTitleSAFePOPM: map['wellcomeTitleSAFePOPM']! as String,
        wellcomeTitleSAFeSSM: map['wellcomeTitleSAFeSSM']! as String,
        wellcomeTitleComptiaSecurity:
            map['wellcomeTitleComptiaSecurity']! as String,
        wellcomeTitleComptiaNetwork:
            map['wellcomeTitleComptiaNetwork']! as String,
        wellcomeTitleComptiaITF: map['wellcomeTitleComptiaITF']! as String,
        wellcomeTitleComptiaA: map['wellcomeTitleComptiaA']! as String,
        wellcomeTitleAwsMlsIos: map['wellcomeTitleAwsMlsIos']! as String,
        wellcomeTitleAwsMls: map['wellcomeTitleAwsMls']! as String,
        wellcomeTitleAwsSap: map['wellcomeTitleAwsSap']! as String,
        wellcomeTitleAwsDop: map['wellcomeTitleAwsDop']! as String,
        wellcomeTitleItil: map['wellcomeTitleItil']! as String,
        wellcomeTitleAwsDva: map['wellcomeTitleAwsDva']! as String,
        wellcomeTitleAwsClf: map['wellcomeTitleAwsClf']! as String,
        wellcomeTitleAwsSoa: map['wellcomeTitleAwsSoa']! as String,
        wellcomeTitleRmp: map['wellcomeTitleRmp']! as String,
        wellcomeTitleAwsSaa: map['wellcomeTitleAwsSaa']! as String,
        wellcomeTitlePba: map['wellcomeTitlePba']! as String,
        wellcomeTitlePfmp: map['wellcomeTitlePfmp']! as String,
        wellcomeTitleEcba: map['wellcomeTitleEcba']! as String,
        wellcomeTitleISTQB: map['wellcomeTitleISTQB']! as String,
        wellcomeTitlePgmp: map['wellcomeTitlePgmp']! as String,
        wellcomeTitleCapm: map['wellcomeTitleCapm']! as String,
        wellcomeTitleCbap: map['wellcomeTitleCbap']! as String,
        wellcomeTitleCCBA: map['wellcomeTitleCCBA']! as String,
        wellcomeTitlePmp: map['wellcomeTitlePmp']! as String,
        wellcomeTitlePsd: map['wellcomeTitlePsd']! as String,
        wellcomeTitleAcp: map['wellcomeTitleAcp']! as String,
        wellcomeTitlePsk: map['wellcomeTitlePsk']! as String,
        wellcomeTitleSps: map['wellcomeTitleSps']! as String,
        wellcomeTitlePal: map['wellcomeTitlePal']! as String,
        wellcomeTitlePsm2: map['wellcomeTitlePsm2']! as String,
        wellcomeTitlePspo2: map['wellcomeTitlePspo2']! as String,
        wellcomeTitlePspo: map['wellcomeTitlePspo']! as String,
        wellcomeTitle: map['wellcomeTitle']! as String,
        appDescriptionPrince2: map['appDescriptionPrince2']! as String,
        appDescriptionCISSP: map['appDescriptionCISSP']! as String,
        appDescriptionComptiaLinux:
            map['appDescriptionComptiaLinux']! as String,
        appDescriptionComptiaCloud:
            map['appDescriptionComptiaCloud']! as String,
        appDescriptionComptiaProject:
            map['appDescriptionComptiaProject']! as String,
        appDescriptionComptiaServer:
            map['appDescriptionComptiaServer']! as String,
        appDescriptionSAFePOPM: map['appDescriptionSAFePOPM']! as String,
        appDescriptionSAFeSSM: map['appDescriptionSAFeSSM']! as String,
        appDescriptionComptiaSecurity:
            map['appDescriptionComptiaSecurity']! as String,
        appDescriptionComptiaNetwork:
            map['appDescriptionComptiaNetwork']! as String,
        appDescriptionComptiaITF: map['appDescriptionComptiaITF']! as String,
        appDescriptionComptiaA: map['appDescriptionComptiaA']! as String,
        appDescriptionAwsMls: map['appDescriptionAwsMls']! as String,
        appDescriptionAwsSap: map['appDescriptionAwsSap']! as String,
        appDescriptionAwsDop: map['appDescriptionAwsDop']! as String,
        appDescriptionItil: map['appDescriptionItil']! as String,
        appDescriptionAwsDva: map['appDescriptionAwsDva']! as String,
        appDescriptionAwsClf: map['appDescriptionAwsClf']! as String,
        appDescriptionAwsSoa: map['appDescriptionAwsSoa']! as String,
        appDescriptionRmp: map['appDescriptionRmp']! as String,
        appDescriptionAwsSaa: map['appDescriptionAwsSaa']! as String,
        appDescriptionPba: map['appDescriptionPba']! as String,
        appDescriptionPfmp: map['appDescriptionPfmp']! as String,
        appDescriptionEcba: map['appDescriptionEcba']! as String,
        appDescriptionISTQB: map['appDescriptionISTQB']! as String,
        appDescriptionPgmp: map['appDescriptionPgmp']! as String,
        appDescriptionCapm: map['appDescriptionCapm']! as String,
        appDescriptionCbap: map['appDescriptionCbap']! as String,
        appDescriptionCCBA: map['appDescriptionCCBA']! as String,
        appDescriptionPmp: map['appDescriptionPmp']! as String,
        appDescriptionPsd: map['appDescriptionPsd']! as String,
        appDescriptionAcp: map['appDescriptionAcp']! as String,
        appDescriptionPsk: map['appDescriptionPsk']! as String,
        appDescriptionSps: map['appDescriptionSps']! as String,
        appDescriptionPal: map['appDescriptionPal']! as String,
        appDescriptionPspo2: map['appDescriptionPspo2']! as String,
        appDescriptionPspo: map['appDescriptionPspo']! as String,
        appDescriptionPsm2: map['appDescriptionPsm2']! as String,
        appDescription: map['appDescription']! as String,
        appTitlePrince2: map['appTitlePrince2']! as String,
        appTitleCissp: map['appTitleCissp']! as String,
        appTitleComptiaLinux: map['appTitleComptiaLinux']! as String,
        appTitleComptiaCloud: map['appTitleComptiaCloud']! as String,
        appTitleComptiaProject: map['appTitleComptiaProject']! as String,
        appTitleComptiaServer: map['appTitleComptiaServer']! as String,
        appTitleSAFePOPM: map['appTitleSAFePOPM']! as String,
        appTitleSAFeSSM: map['appTitleSAFeSSM']! as String,
        appTitleComptiaSecurity: map['appTitleComptiaSecurity']! as String,
        appTitleComptiaNetwork: map['appTitleComptiaNetwork']! as String,
        appTitleComptiaITF: map['appTitleComptiaITF']! as String,
        appTitleComptiaA: map['appTitleComptiaA']! as String,
        appTitleAwsMls: map['appTitleAwsMls']! as String,
        appTitleAwsSap: map['appTitleAwsSap']! as String,
        appTitleAwsDop: map['appTitleAwsDop']! as String,
        appTitleItil: map['appTitleItil']! as String,
        appTitleAwsDva: map['appTitleAwsDva']! as String,
        appTitleAwsClf: map['appTitleAwsClf']! as String,
        appTitleAwsSoa: map['appTitleAwsSoa']! as String,
        appTitleRmp: map['appTitleRmp']! as String,
        appTitleAwsSaa: map['appTitleAwsSaa']! as String,
        appTitlePba: map['appTitlePba']! as String,
        appTitlePfmp: map['appTitlePfmp']! as String,
        appTitleEcba: map['appTitleEcba']! as String,
        appTitleISTQB: map['appTitleISTQB']! as String,
        appTitlePgmp: map['appTitlePgmp']! as String,
        appTitleCapm: map['appTitleCapm']! as String,
        appTitleCbap: map['appTitleCbap']! as String,
        appTitleCCBA: map['appTitleCCBA']! as String,
        appTitlePmp: map['appTitlePmp']! as String,
        appTitlePsd: map['appTitlePsd']! as String,
        appTitleAcp: map['appTitleAcp']! as String,
        appTitlePsk: map['appTitlePsk']! as String,
        appTitleSps: map['appTitleSps']! as String,
        appTitlePal: map['appTitlePal']! as String,
        appTitlePspo2: map['appTitlePspo2']! as String,
        appTitlePspo: map['appTitlePspo']! as String,
        appTitlePsm2: map['appTitlePsm2']! as String,
        appTitle: map['appTitle']! as String,
        appNamePrince2: map['appNamePrince2']! as String,
        appNameCissp: map['appNameCissp']! as String,
        appNameComptiaLinux: map['appNameComptiaLinux']! as String,
        appNameComptiaCloud: map['appNameComptiaCloud']! as String,
        appNameComptiaProject: map['appNameComptiaProject']! as String,
        appNameComptiaServer: map['appNameComptiaServer']! as String,
        appNameSAFePOPM: map['appNameSAFePOPM']! as String,
        appNameSAFeSSM: map['appNameSAFeSSM']! as String,
        appNameComptiaSecurity: map['appNameComptiaSecurity']! as String,
        appNameComptiaNetwork: map['appNameComptiaNetwork']! as String,
        appNameComptiaITF: map['appNameComptiaITF']! as String,
        appNameComptiaA: map['appNameComptiaA']! as String,
        appNameAwsMls: map['appNameAwsMls']! as String,
        appNameAwsSap: map['appNameAwsSap']! as String,
        appNameAwsDop: map['appNameAwsDop']! as String,
        appNameItil: map['appNameItil']! as String,
        appNameAwsDva: map['appNameAwsDva']! as String,
        appNameAwsClf: map['appNameAwsClf']! as String,
        appNameAwsSoa: map['appNameAwsSoa']! as String,
        appNameRmp: map['appNameRmp']! as String,
        appNameAwsSaa: map['appNameAwsSaa']! as String,
        appNamePba: map['appNamePba']! as String,
        appNamePfmp: map['appNamePfmp']! as String,
        appNameEcba: map['appNameEcba']! as String,
        appNameISTQB: map['appNameISTQB']! as String,
        appNamePgmp: map['appNamePgmp']! as String,
        appNameCapm: map['appNameCapm']! as String,
        appNameCbap: map['appNameCbap']! as String,
        appNameCCBA: map['appNameCCBA']! as String,
        appNamePmp: map['appNamePmp']! as String,
        appNamePsd: map['appNamePsd']! as String,
        appNameAcp: map['appNameAcp']! as String,
        appNamePsk: map['appNamePsk']! as String,
        appNameSps: map['appNameSps']! as String,
        appNamePal: map['appNamePal']! as String,
        appNamePspo2: map['appNamePspo2']! as String,
        appNamePsm2: map['appNamePsm2']! as String,
        appNamePspo: map['appNamePspo']! as String,
        appName: map['appName']! as String,
      );

  AppLocalizationsData copyWith({
    String? finishEarly,
    String? redeemCodeError,
    String? introduceDescription,
    String? introduceTitle,
    String? then,
    String? emptyExamTips,
    String? afterTrial,
    String? trialDescription,
    String? connectServerError,
    String? hour,
    String? hours,
    String? noReport,
    String? sendResponseSuccess,
    String? sendResponse,
    String? closeReport,
    String? scrumpassResponse,
    String? yourResponse,
    String? reportContent,
    String? closed,
    String? reviewing,
    String? status,
    String? reportedQuestion,
    String? resultText,
    String? listQuizShowcase2,
    String? listQuizShowcase1,
    String? homeShowcase5,
    String? homeShowcase4,
    String? homeShowcase3,
    String? homeShowcase2,
    String? homeShowcase1,
    String? descriptionContent,
    String? aboutUsContent,
    String? copyToClipboard,
    String? androidLink,
    String? iosLink,
    String? getCertificate,
    String? shareApp,
    String? tutorialTap,
    String? tutorialSwipeRight,
    String? tutorialSwipeLeft,
    String? flashcardEndText,
    String? learnedEverything,
    String? halfWayThere,
    String? congratulations,
    String? continueStudy,
    String? restart,
    String? restartFlashCardPopup,
    String? favoriteWords,
    String? score,
    String? testNumber,
    String? disclamer,
    String? goodToGo,
    String? keepGoing,
    String? notEnough,
    String? examPassRateDetail,
    String? noData,
    String? timeDetail,
    String? questionDetail,
    String? eachQuestion,
    String? totalTime,
    String? questionDone,
    String? streak,
    String? latestScore,
    String? passedExam,
    String? quizDone,
    String? avgScore,
    String? quizFlashCardDetail,
    String? flashCard,
    String? notReady,
    String? examPassRate,
    String? emptyField,
    String? notificationDetail,
    String? notification,
    String? questionrule,
    String? randomRule,
    String? getSupport,
    String? premiumVersion,
    String? someTips4U,
    String? yourCode,
    String? enterRedeemCode,
    String? redeemCode,
    String? updateYear,
    String? fully,
    String? detailedExplain,
    String? answersWith,
    String? allTheExams,
    String? unlock,
    String? startTrial,
    String? cancelAnyTime,
    String? fullyIUpdated,
    String? trialTitle,
    String? sunday,
    String? saturday,
    String? friday,
    String? thursday,
    String? wednesday,
    String? tuesday,
    String? monday,
    String? notificationStatus,
    String? notificationDays,
    String? notificationTime,
    String? expiresToday,
    String? trial,
    String? dayLeft,
    String? otherApp,
    String? randomSet,
    String? questionSet,
    String? doing,
    String? premium,
    String? free,
    String? resultDetail,
    String? version,
    String? fail,
    String? pass,
    String? secondShort,
    String? term,
    String? support,
    String? privacyPolicy,
    String? termOfUse,
    String? percentageRequireToPass,
    String? plan,
    String? resetData,
    String? upgradeToGetFeature,
    String? deleteSuccess,
    String? confirmDeleteQuestionReview,
    String? upgrade,
    String? unlockAllExams,
    String? unlockQR,
    String? premiumFeature,
    String? hide,
    String? delete,
    String? viewExplanation,
    String? confirmDeleteQuestion,
    String? yes,
    String? restorePurchase,
    String? thankForPurchase,
    String? purchase,
    String? alreadyPaid,
    String? month,
    String? months,
    String? total,
    String? monthly,
    String? withExplain,
    String? unlockAllExam,
    String? unlockFeature,
    String? accessPremium,
    String? statistics,
    String? noMarkedQuestion,
    String? noWrongQuestion,
    String? questionReview,
    String? numberWrong,
    String? markedQuestion,
    String? wrongQuestion,
    String? unanswered,
    String? wrong,
    String? correct,
    String? totalQuestion,
    String? yourName,
    String? retryInternet,
    String? viewExamList,
    String? minutesShort,
    String? left,
    String? numberQuestion,
    String? reportDetail,
    String? numReport,
    String? cancel,
    String? send,
    String? reportSuccess,
    String? report,
    String? notAnswered,
    String? forceUpdateDialogContent,
    String? rateUs,
    String? updateDialogContent,
    String? noInternet,
    String? totalQuizDone,
    String? recentScore,
    String? highScore,
    String? fontExam,
    String? averageExamResult,
    String? serviceSatisfactionRate,
    String? certificationPassRate,
    String? yourMessage,
    String? writeUsDirectly,
    String? contact,
    String? callUs,
    String? connectUs,
    String? quizNumber,
    String? confirmRetry,
    String? noUserName,
    String? noQuizResult,
    String? noInfo,
    String? errorDialog,
    String? error,
    String? confirmDoQuiz,
    String? resumeQuizDialog,
    String? bookmarked,
    String? questionList,
    String? allQuestion,
    String? previous,
    String? finish,
    String? next,
    String? questionOf,
    String? question,
    String? timeUp,
    String? remainTime,
    String? quitQuizDialog,
    String? timeUpDialog,
    String? no,
    String? confirmEndQuiz,
    String? mustSelectAnswer,
    String? attention,
    String? productOfScrumPass,
    String? wellcomeEnterName,
    String? allAns,
    String? save,
    String? noLimit,
    String? answered,
    String? noAnsSelected,
    String? continueQ,
    String? time,
    String? back,
    String? intruction,
    String? minPercentage,
    String? timeEnd,
    String? timeStart,
    String? quizDetail,
    String? userDetail,
    String? noQuizAvaliable,
    String? minutes,
    String? timeLeft,
    String? duration,
    String? numOfQuestion,
    String? begin,
    String? answer,
    String? retry,
    String? home,
    String? wrongAns,
    String? rightAns,
    String? marked,
    String? finishDate,
    String? numHasDone,
    String? numWrongAns,
    String? numRightAns,
    String? passRequirement,
    String? detail,
    String? resultList,
    String? quizList,
    String? aboutUs,
    String? scrumguide,
    String? result,
    String? examPractice,
    String? numFailQuiz,
    String? numPassQuiz,
    String? passPercentage,
    String? chooseLang,
    String? lang,
    String? name,
    String? hello,
    String? wellcomeTitlePrince2,
    String? wellcomeTitleCissp,
    String? wellcomeTitleComptiaLinux,
    String? wellcomeTitleComptiaCloud,
    String? wellcomeTitleComptiaProject,
    String? wellcomeTitleComptiaServer,
    String? wellcomeTitleSAFePOPM,
    String? wellcomeTitleSAFeSSM,
    String? wellcomeTitleComptiaSecurity,
    String? wellcomeTitleComptiaNetwork,
    String? wellcomeTitleComptiaITF,
    String? wellcomeTitleComptiaA,
    String? wellcomeTitleAwsMlsIos,
    String? wellcomeTitleAwsMls,
    String? wellcomeTitleAwsSap,
    String? wellcomeTitleAwsDop,
    String? wellcomeTitleItil,
    String? wellcomeTitleAwsDva,
    String? wellcomeTitleAwsClf,
    String? wellcomeTitleAwsSoa,
    String? wellcomeTitleRmp,
    String? wellcomeTitleAwsSaa,
    String? wellcomeTitlePba,
    String? wellcomeTitlePfmp,
    String? wellcomeTitleEcba,
    String? wellcomeTitleISTQB,
    String? wellcomeTitlePgmp,
    String? wellcomeTitleCapm,
    String? wellcomeTitleCbap,
    String? wellcomeTitleCCBA,
    String? wellcomeTitlePmp,
    String? wellcomeTitlePsd,
    String? wellcomeTitleAcp,
    String? wellcomeTitlePsk,
    String? wellcomeTitleSps,
    String? wellcomeTitlePal,
    String? wellcomeTitlePsm2,
    String? wellcomeTitlePspo2,
    String? wellcomeTitlePspo,
    String? wellcomeTitle,
    String? appDescriptionPrince2,
    String? appDescriptionCISSP,
    String? appDescriptionComptiaLinux,
    String? appDescriptionComptiaCloud,
    String? appDescriptionComptiaProject,
    String? appDescriptionComptiaServer,
    String? appDescriptionSAFePOPM,
    String? appDescriptionSAFeSSM,
    String? appDescriptionComptiaSecurity,
    String? appDescriptionComptiaNetwork,
    String? appDescriptionComptiaITF,
    String? appDescriptionComptiaA,
    String? appDescriptionAwsMls,
    String? appDescriptionAwsSap,
    String? appDescriptionAwsDop,
    String? appDescriptionItil,
    String? appDescriptionAwsDva,
    String? appDescriptionAwsClf,
    String? appDescriptionAwsSoa,
    String? appDescriptionRmp,
    String? appDescriptionAwsSaa,
    String? appDescriptionPba,
    String? appDescriptionPfmp,
    String? appDescriptionEcba,
    String? appDescriptionISTQB,
    String? appDescriptionPgmp,
    String? appDescriptionCapm,
    String? appDescriptionCbap,
    String? appDescriptionCCBA,
    String? appDescriptionPmp,
    String? appDescriptionPsd,
    String? appDescriptionAcp,
    String? appDescriptionPsk,
    String? appDescriptionSps,
    String? appDescriptionPal,
    String? appDescriptionPspo2,
    String? appDescriptionPspo,
    String? appDescriptionPsm2,
    String? appDescription,
    String? appTitlePrince2,
    String? appTitleCissp,
    String? appTitleComptiaLinux,
    String? appTitleComptiaCloud,
    String? appTitleComptiaProject,
    String? appTitleComptiaServer,
    String? appTitleSAFePOPM,
    String? appTitleSAFeSSM,
    String? appTitleComptiaSecurity,
    String? appTitleComptiaNetwork,
    String? appTitleComptiaITF,
    String? appTitleComptiaA,
    String? appTitleAwsMls,
    String? appTitleAwsSap,
    String? appTitleAwsDop,
    String? appTitleItil,
    String? appTitleAwsDva,
    String? appTitleAwsClf,
    String? appTitleAwsSoa,
    String? appTitleRmp,
    String? appTitleAwsSaa,
    String? appTitlePba,
    String? appTitlePfmp,
    String? appTitleEcba,
    String? appTitleISTQB,
    String? appTitlePgmp,
    String? appTitleCapm,
    String? appTitleCbap,
    String? appTitleCCBA,
    String? appTitlePmp,
    String? appTitlePsd,
    String? appTitleAcp,
    String? appTitlePsk,
    String? appTitleSps,
    String? appTitlePal,
    String? appTitlePspo2,
    String? appTitlePspo,
    String? appTitlePsm2,
    String? appTitle,
    String? appNamePrince2,
    String? appNameCissp,
    String? appNameComptiaLinux,
    String? appNameComptiaCloud,
    String? appNameComptiaProject,
    String? appNameComptiaServer,
    String? appNameSAFePOPM,
    String? appNameSAFeSSM,
    String? appNameComptiaSecurity,
    String? appNameComptiaNetwork,
    String? appNameComptiaITF,
    String? appNameComptiaA,
    String? appNameAwsMls,
    String? appNameAwsSap,
    String? appNameAwsDop,
    String? appNameItil,
    String? appNameAwsDva,
    String? appNameAwsClf,
    String? appNameAwsSoa,
    String? appNameRmp,
    String? appNameAwsSaa,
    String? appNamePba,
    String? appNamePfmp,
    String? appNameEcba,
    String? appNameISTQB,
    String? appNamePgmp,
    String? appNameCapm,
    String? appNameCbap,
    String? appNameCCBA,
    String? appNamePmp,
    String? appNamePsd,
    String? appNameAcp,
    String? appNamePsk,
    String? appNameSps,
    String? appNamePal,
    String? appNamePspo2,
    String? appNamePsm2,
    String? appNamePspo,
    String? appName,
  }) =>
      AppLocalizationsData(
        finishEarly: finishEarly ?? this.finishEarly,
        redeemCodeError: redeemCodeError ?? this.redeemCodeError,
        introduceDescription: introduceDescription ?? this.introduceDescription,
        introduceTitle: introduceTitle ?? this.introduceTitle,
        then: then ?? this.then,
        emptyExamTips: emptyExamTips ?? this.emptyExamTips,
        afterTrial: afterTrial ?? this.afterTrial,
        trialDescription: trialDescription ?? this.trialDescription,
        connectServerError: connectServerError ?? this.connectServerError,
        hour: hour ?? this.hour,
        hours: hours ?? this.hours,
        noReport: noReport ?? this.noReport,
        sendResponseSuccess: sendResponseSuccess ?? this.sendResponseSuccess,
        sendResponse: sendResponse ?? this.sendResponse,
        closeReport: closeReport ?? this.closeReport,
        scrumpassResponse: scrumpassResponse ?? this.scrumpassResponse,
        yourResponse: yourResponse ?? this.yourResponse,
        reportContent: reportContent ?? this.reportContent,
        closed: closed ?? this.closed,
        reviewing: reviewing ?? this.reviewing,
        status: status ?? this.status,
        reportedQuestion: reportedQuestion ?? this.reportedQuestion,
        resultText: resultText ?? this.resultText,
        listQuizShowcase2: listQuizShowcase2 ?? this.listQuizShowcase2,
        listQuizShowcase1: listQuizShowcase1 ?? this.listQuizShowcase1,
        homeShowcase5: homeShowcase5 ?? this.homeShowcase5,
        homeShowcase4: homeShowcase4 ?? this.homeShowcase4,
        homeShowcase3: homeShowcase3 ?? this.homeShowcase3,
        homeShowcase2: homeShowcase2 ?? this.homeShowcase2,
        homeShowcase1: homeShowcase1 ?? this.homeShowcase1,
        descriptionContent: descriptionContent ?? this.descriptionContent,
        aboutUsContent: aboutUsContent ?? this.aboutUsContent,
        copyToClipboard: copyToClipboard ?? this.copyToClipboard,
        androidLink: androidLink ?? this.androidLink,
        iosLink: iosLink ?? this.iosLink,
        getCertificate: getCertificate ?? this.getCertificate,
        shareApp: shareApp ?? this.shareApp,
        tutorialTap: tutorialTap ?? this.tutorialTap,
        tutorialSwipeRight: tutorialSwipeRight ?? this.tutorialSwipeRight,
        tutorialSwipeLeft: tutorialSwipeLeft ?? this.tutorialSwipeLeft,
        flashcardEndText: flashcardEndText ?? this.flashcardEndText,
        learnedEverything: learnedEverything ?? this.learnedEverything,
        halfWayThere: halfWayThere ?? this.halfWayThere,
        congratulations: congratulations ?? this.congratulations,
        continueStudy: continueStudy ?? this.continueStudy,
        restart: restart ?? this.restart,
        restartFlashCardPopup:
            restartFlashCardPopup ?? this.restartFlashCardPopup,
        favoriteWords: favoriteWords ?? this.favoriteWords,
        score: score ?? this.score,
        testNumber: testNumber ?? this.testNumber,
        disclamer: disclamer ?? this.disclamer,
        goodToGo: goodToGo ?? this.goodToGo,
        keepGoing: keepGoing ?? this.keepGoing,
        notEnough: notEnough ?? this.notEnough,
        examPassRateDetail: examPassRateDetail ?? this.examPassRateDetail,
        noData: noData ?? this.noData,
        timeDetail: timeDetail ?? this.timeDetail,
        questionDetail: questionDetail ?? this.questionDetail,
        eachQuestion: eachQuestion ?? this.eachQuestion,
        totalTime: totalTime ?? this.totalTime,
        questionDone: questionDone ?? this.questionDone,
        streak: streak ?? this.streak,
        latestScore: latestScore ?? this.latestScore,
        passedExam: passedExam ?? this.passedExam,
        quizDone: quizDone ?? this.quizDone,
        avgScore: avgScore ?? this.avgScore,
        quizFlashCardDetail: quizFlashCardDetail ?? this.quizFlashCardDetail,
        flashCard: flashCard ?? this.flashCard,
        notReady: notReady ?? this.notReady,
        examPassRate: examPassRate ?? this.examPassRate,
        emptyField: emptyField ?? this.emptyField,
        notificationDetail: notificationDetail ?? this.notificationDetail,
        notification: notification ?? this.notification,
        questionrule: questionrule ?? this.questionrule,
        randomRule: randomRule ?? this.randomRule,
        getSupport: getSupport ?? this.getSupport,
        premiumVersion: premiumVersion ?? this.premiumVersion,
        someTips4U: someTips4U ?? this.someTips4U,
        yourCode: yourCode ?? this.yourCode,
        enterRedeemCode: enterRedeemCode ?? this.enterRedeemCode,
        redeemCode: redeemCode ?? this.redeemCode,
        updateYear: updateYear ?? this.updateYear,
        fully: fully ?? this.fully,
        detailedExplain: detailedExplain ?? this.detailedExplain,
        answersWith: answersWith ?? this.answersWith,
        allTheExams: allTheExams ?? this.allTheExams,
        unlock: unlock ?? this.unlock,
        startTrial: startTrial ?? this.startTrial,
        cancelAnyTime: cancelAnyTime ?? this.cancelAnyTime,
        fullyIUpdated: fullyIUpdated ?? this.fullyIUpdated,
        trialTitle: trialTitle ?? this.trialTitle,
        sunday: sunday ?? this.sunday,
        saturday: saturday ?? this.saturday,
        friday: friday ?? this.friday,
        thursday: thursday ?? this.thursday,
        wednesday: wednesday ?? this.wednesday,
        tuesday: tuesday ?? this.tuesday,
        monday: monday ?? this.monday,
        notificationStatus: notificationStatus ?? this.notificationStatus,
        notificationDays: notificationDays ?? this.notificationDays,
        notificationTime: notificationTime ?? this.notificationTime,
        expiresToday: expiresToday ?? this.expiresToday,
        trial: trial ?? this.trial,
        dayLeft: dayLeft ?? this.dayLeft,
        otherApp: otherApp ?? this.otherApp,
        randomSet: randomSet ?? this.randomSet,
        questionSet: questionSet ?? this.questionSet,
        doing: doing ?? this.doing,
        premium: premium ?? this.premium,
        free: free ?? this.free,
        resultDetail: resultDetail ?? this.resultDetail,
        version: version ?? this.version,
        fail: fail ?? this.fail,
        pass: pass ?? this.pass,
        secondShort: secondShort ?? this.secondShort,
        term: term ?? this.term,
        support: support ?? this.support,
        privacyPolicy: privacyPolicy ?? this.privacyPolicy,
        termOfUse: termOfUse ?? this.termOfUse,
        percentageRequireToPass:
            percentageRequireToPass ?? this.percentageRequireToPass,
        plan: plan ?? this.plan,
        resetData: resetData ?? this.resetData,
        upgradeToGetFeature: upgradeToGetFeature ?? this.upgradeToGetFeature,
        deleteSuccess: deleteSuccess ?? this.deleteSuccess,
        confirmDeleteQuestionReview:
            confirmDeleteQuestionReview ?? this.confirmDeleteQuestionReview,
        upgrade: upgrade ?? this.upgrade,
        unlockAllExams: unlockAllExams ?? this.unlockAllExams,
        unlockQR: unlockQR ?? this.unlockQR,
        premiumFeature: premiumFeature ?? this.premiumFeature,
        hide: hide ?? this.hide,
        delete: delete ?? this.delete,
        viewExplanation: viewExplanation ?? this.viewExplanation,
        confirmDeleteQuestion:
            confirmDeleteQuestion ?? this.confirmDeleteQuestion,
        yes: yes ?? this.yes,
        restorePurchase: restorePurchase ?? this.restorePurchase,
        thankForPurchase: thankForPurchase ?? this.thankForPurchase,
        purchase: purchase ?? this.purchase,
        alreadyPaid: alreadyPaid ?? this.alreadyPaid,
        month: month ?? this.month,
        months: months ?? this.months,
        total: total ?? this.total,
        monthly: monthly ?? this.monthly,
        withExplain: withExplain ?? this.withExplain,
        unlockAllExam: unlockAllExam ?? this.unlockAllExam,
        unlockFeature: unlockFeature ?? this.unlockFeature,
        accessPremium: accessPremium ?? this.accessPremium,
        statistics: statistics ?? this.statistics,
        noMarkedQuestion: noMarkedQuestion ?? this.noMarkedQuestion,
        noWrongQuestion: noWrongQuestion ?? this.noWrongQuestion,
        questionReview: questionReview ?? this.questionReview,
        numberWrong: numberWrong ?? this.numberWrong,
        markedQuestion: markedQuestion ?? this.markedQuestion,
        wrongQuestion: wrongQuestion ?? this.wrongQuestion,
        unanswered: unanswered ?? this.unanswered,
        wrong: wrong ?? this.wrong,
        correct: correct ?? this.correct,
        totalQuestion: totalQuestion ?? this.totalQuestion,
        yourName: yourName ?? this.yourName,
        retryInternet: retryInternet ?? this.retryInternet,
        viewExamList: viewExamList ?? this.viewExamList,
        minutesShort: minutesShort ?? this.minutesShort,
        left: left ?? this.left,
        numberQuestion: numberQuestion ?? this.numberQuestion,
        reportDetail: reportDetail ?? this.reportDetail,
        numReport: numReport ?? this.numReport,
        cancel: cancel ?? this.cancel,
        send: send ?? this.send,
        reportSuccess: reportSuccess ?? this.reportSuccess,
        report: report ?? this.report,
        notAnswered: notAnswered ?? this.notAnswered,
        forceUpdateDialogContent:
            forceUpdateDialogContent ?? this.forceUpdateDialogContent,
        rateUs: rateUs ?? this.rateUs,
        updateDialogContent: updateDialogContent ?? this.updateDialogContent,
        noInternet: noInternet ?? this.noInternet,
        totalQuizDone: totalQuizDone ?? this.totalQuizDone,
        recentScore: recentScore ?? this.recentScore,
        highScore: highScore ?? this.highScore,
        fontExam: fontExam ?? this.fontExam,
        averageExamResult: averageExamResult ?? this.averageExamResult,
        serviceSatisfactionRate:
            serviceSatisfactionRate ?? this.serviceSatisfactionRate,
        certificationPassRate:
            certificationPassRate ?? this.certificationPassRate,
        yourMessage: yourMessage ?? this.yourMessage,
        writeUsDirectly: writeUsDirectly ?? this.writeUsDirectly,
        contact: contact ?? this.contact,
        callUs: callUs ?? this.callUs,
        connectUs: connectUs ?? this.connectUs,
        quizNumber: quizNumber ?? this.quizNumber,
        confirmRetry: confirmRetry ?? this.confirmRetry,
        noUserName: noUserName ?? this.noUserName,
        noQuizResult: noQuizResult ?? this.noQuizResult,
        noInfo: noInfo ?? this.noInfo,
        errorDialog: errorDialog ?? this.errorDialog,
        error: error ?? this.error,
        confirmDoQuiz: confirmDoQuiz ?? this.confirmDoQuiz,
        resumeQuizDialog: resumeQuizDialog ?? this.resumeQuizDialog,
        bookmarked: bookmarked ?? this.bookmarked,
        questionList: questionList ?? this.questionList,
        allQuestion: allQuestion ?? this.allQuestion,
        previous: previous ?? this.previous,
        finish: finish ?? this.finish,
        next: next ?? this.next,
        questionOf: questionOf ?? this.questionOf,
        question: question ?? this.question,
        timeUp: timeUp ?? this.timeUp,
        remainTime: remainTime ?? this.remainTime,
        quitQuizDialog: quitQuizDialog ?? this.quitQuizDialog,
        timeUpDialog: timeUpDialog ?? this.timeUpDialog,
        no: no ?? this.no,
        confirmEndQuiz: confirmEndQuiz ?? this.confirmEndQuiz,
        mustSelectAnswer: mustSelectAnswer ?? this.mustSelectAnswer,
        attention: attention ?? this.attention,
        productOfScrumPass: productOfScrumPass ?? this.productOfScrumPass,
        wellcomeEnterName: wellcomeEnterName ?? this.wellcomeEnterName,
        allAns: allAns ?? this.allAns,
        save: save ?? this.save,
        noLimit: noLimit ?? this.noLimit,
        answered: answered ?? this.answered,
        noAnsSelected: noAnsSelected ?? this.noAnsSelected,
        continueQ: continueQ ?? this.continueQ,
        time: time ?? this.time,
        back: back ?? this.back,
        intruction: intruction ?? this.intruction,
        minPercentage: minPercentage ?? this.minPercentage,
        timeEnd: timeEnd ?? this.timeEnd,
        timeStart: timeStart ?? this.timeStart,
        quizDetail: quizDetail ?? this.quizDetail,
        userDetail: userDetail ?? this.userDetail,
        noQuizAvaliable: noQuizAvaliable ?? this.noQuizAvaliable,
        minutes: minutes ?? this.minutes,
        timeLeft: timeLeft ?? this.timeLeft,
        duration: duration ?? this.duration,
        numOfQuestion: numOfQuestion ?? this.numOfQuestion,
        begin: begin ?? this.begin,
        answer: answer ?? this.answer,
        retry: retry ?? this.retry,
        home: home ?? this.home,
        wrongAns: wrongAns ?? this.wrongAns,
        rightAns: rightAns ?? this.rightAns,
        marked: marked ?? this.marked,
        finishDate: finishDate ?? this.finishDate,
        numHasDone: numHasDone ?? this.numHasDone,
        numWrongAns: numWrongAns ?? this.numWrongAns,
        numRightAns: numRightAns ?? this.numRightAns,
        passRequirement: passRequirement ?? this.passRequirement,
        detail: detail ?? this.detail,
        resultList: resultList ?? this.resultList,
        quizList: quizList ?? this.quizList,
        aboutUs: aboutUs ?? this.aboutUs,
        scrumguide: scrumguide ?? this.scrumguide,
        result: result ?? this.result,
        examPractice: examPractice ?? this.examPractice,
        numFailQuiz: numFailQuiz ?? this.numFailQuiz,
        numPassQuiz: numPassQuiz ?? this.numPassQuiz,
        passPercentage: passPercentage ?? this.passPercentage,
        chooseLang: chooseLang ?? this.chooseLang,
        lang: lang ?? this.lang,
        name: name ?? this.name,
        hello: hello ?? this.hello,
        wellcomeTitlePrince2: wellcomeTitlePrince2 ?? this.wellcomeTitlePrince2,
        wellcomeTitleCissp: wellcomeTitleCissp ?? this.wellcomeTitleCissp,
        wellcomeTitleComptiaLinux:
            wellcomeTitleComptiaLinux ?? this.wellcomeTitleComptiaLinux,
        wellcomeTitleComptiaCloud:
            wellcomeTitleComptiaCloud ?? this.wellcomeTitleComptiaCloud,
        wellcomeTitleComptiaProject:
            wellcomeTitleComptiaProject ?? this.wellcomeTitleComptiaProject,
        wellcomeTitleComptiaServer:
            wellcomeTitleComptiaServer ?? this.wellcomeTitleComptiaServer,
        wellcomeTitleSAFePOPM:
            wellcomeTitleSAFePOPM ?? this.wellcomeTitleSAFePOPM,
        wellcomeTitleSAFeSSM: wellcomeTitleSAFeSSM ?? this.wellcomeTitleSAFeSSM,
        wellcomeTitleComptiaSecurity:
            wellcomeTitleComptiaSecurity ?? this.wellcomeTitleComptiaSecurity,
        wellcomeTitleComptiaNetwork:
            wellcomeTitleComptiaNetwork ?? this.wellcomeTitleComptiaNetwork,
        wellcomeTitleComptiaITF:
            wellcomeTitleComptiaITF ?? this.wellcomeTitleComptiaITF,
        wellcomeTitleComptiaA:
            wellcomeTitleComptiaA ?? this.wellcomeTitleComptiaA,
        wellcomeTitleAwsMlsIos:
            wellcomeTitleAwsMlsIos ?? this.wellcomeTitleAwsMlsIos,
        wellcomeTitleAwsMls: wellcomeTitleAwsMls ?? this.wellcomeTitleAwsMls,
        wellcomeTitleAwsSap: wellcomeTitleAwsSap ?? this.wellcomeTitleAwsSap,
        wellcomeTitleAwsDop: wellcomeTitleAwsDop ?? this.wellcomeTitleAwsDop,
        wellcomeTitleItil: wellcomeTitleItil ?? this.wellcomeTitleItil,
        wellcomeTitleAwsDva: wellcomeTitleAwsDva ?? this.wellcomeTitleAwsDva,
        wellcomeTitleAwsClf: wellcomeTitleAwsClf ?? this.wellcomeTitleAwsClf,
        wellcomeTitleAwsSoa: wellcomeTitleAwsSoa ?? this.wellcomeTitleAwsSoa,
        wellcomeTitleRmp: wellcomeTitleRmp ?? this.wellcomeTitleRmp,
        wellcomeTitleAwsSaa: wellcomeTitleAwsSaa ?? this.wellcomeTitleAwsSaa,
        wellcomeTitlePba: wellcomeTitlePba ?? this.wellcomeTitlePba,
        wellcomeTitlePfmp: wellcomeTitlePfmp ?? this.wellcomeTitlePfmp,
        wellcomeTitleEcba: wellcomeTitleEcba ?? this.wellcomeTitleEcba,
        wellcomeTitleISTQB: wellcomeTitleISTQB ?? this.wellcomeTitleISTQB,
        wellcomeTitlePgmp: wellcomeTitlePgmp ?? this.wellcomeTitlePgmp,
        wellcomeTitleCapm: wellcomeTitleCapm ?? this.wellcomeTitleCapm,
        wellcomeTitleCbap: wellcomeTitleCbap ?? this.wellcomeTitleCbap,
        wellcomeTitleCCBA: wellcomeTitleCCBA ?? this.wellcomeTitleCCBA,
        wellcomeTitlePmp: wellcomeTitlePmp ?? this.wellcomeTitlePmp,
        wellcomeTitlePsd: wellcomeTitlePsd ?? this.wellcomeTitlePsd,
        wellcomeTitleAcp: wellcomeTitleAcp ?? this.wellcomeTitleAcp,
        wellcomeTitlePsk: wellcomeTitlePsk ?? this.wellcomeTitlePsk,
        wellcomeTitleSps: wellcomeTitleSps ?? this.wellcomeTitleSps,
        wellcomeTitlePal: wellcomeTitlePal ?? this.wellcomeTitlePal,
        wellcomeTitlePsm2: wellcomeTitlePsm2 ?? this.wellcomeTitlePsm2,
        wellcomeTitlePspo2: wellcomeTitlePspo2 ?? this.wellcomeTitlePspo2,
        wellcomeTitlePspo: wellcomeTitlePspo ?? this.wellcomeTitlePspo,
        wellcomeTitle: wellcomeTitle ?? this.wellcomeTitle,
        appDescriptionPrince2:
            appDescriptionPrince2 ?? this.appDescriptionPrince2,
        appDescriptionCISSP: appDescriptionCISSP ?? this.appDescriptionCISSP,
        appDescriptionComptiaLinux:
            appDescriptionComptiaLinux ?? this.appDescriptionComptiaLinux,
        appDescriptionComptiaCloud:
            appDescriptionComptiaCloud ?? this.appDescriptionComptiaCloud,
        appDescriptionComptiaProject:
            appDescriptionComptiaProject ?? this.appDescriptionComptiaProject,
        appDescriptionComptiaServer:
            appDescriptionComptiaServer ?? this.appDescriptionComptiaServer,
        appDescriptionSAFePOPM:
            appDescriptionSAFePOPM ?? this.appDescriptionSAFePOPM,
        appDescriptionSAFeSSM:
            appDescriptionSAFeSSM ?? this.appDescriptionSAFeSSM,
        appDescriptionComptiaSecurity:
            appDescriptionComptiaSecurity ?? this.appDescriptionComptiaSecurity,
        appDescriptionComptiaNetwork:
            appDescriptionComptiaNetwork ?? this.appDescriptionComptiaNetwork,
        appDescriptionComptiaITF:
            appDescriptionComptiaITF ?? this.appDescriptionComptiaITF,
        appDescriptionComptiaA:
            appDescriptionComptiaA ?? this.appDescriptionComptiaA,
        appDescriptionAwsMls: appDescriptionAwsMls ?? this.appDescriptionAwsMls,
        appDescriptionAwsSap: appDescriptionAwsSap ?? this.appDescriptionAwsSap,
        appDescriptionAwsDop: appDescriptionAwsDop ?? this.appDescriptionAwsDop,
        appDescriptionItil: appDescriptionItil ?? this.appDescriptionItil,
        appDescriptionAwsDva: appDescriptionAwsDva ?? this.appDescriptionAwsDva,
        appDescriptionAwsClf: appDescriptionAwsClf ?? this.appDescriptionAwsClf,
        appDescriptionAwsSoa: appDescriptionAwsSoa ?? this.appDescriptionAwsSoa,
        appDescriptionRmp: appDescriptionRmp ?? this.appDescriptionRmp,
        appDescriptionAwsSaa: appDescriptionAwsSaa ?? this.appDescriptionAwsSaa,
        appDescriptionPba: appDescriptionPba ?? this.appDescriptionPba,
        appDescriptionPfmp: appDescriptionPfmp ?? this.appDescriptionPfmp,
        appDescriptionEcba: appDescriptionEcba ?? this.appDescriptionEcba,
        appDescriptionISTQB: appDescriptionISTQB ?? this.appDescriptionISTQB,
        appDescriptionPgmp: appDescriptionPgmp ?? this.appDescriptionPgmp,
        appDescriptionCapm: appDescriptionCapm ?? this.appDescriptionCapm,
        appDescriptionCbap: appDescriptionCbap ?? this.appDescriptionCbap,
        appDescriptionCCBA: appDescriptionCCBA ?? this.appDescriptionCCBA,
        appDescriptionPmp: appDescriptionPmp ?? this.appDescriptionPmp,
        appDescriptionPsd: appDescriptionPsd ?? this.appDescriptionPsd,
        appDescriptionAcp: appDescriptionAcp ?? this.appDescriptionAcp,
        appDescriptionPsk: appDescriptionPsk ?? this.appDescriptionPsk,
        appDescriptionSps: appDescriptionSps ?? this.appDescriptionSps,
        appDescriptionPal: appDescriptionPal ?? this.appDescriptionPal,
        appDescriptionPspo2: appDescriptionPspo2 ?? this.appDescriptionPspo2,
        appDescriptionPspo: appDescriptionPspo ?? this.appDescriptionPspo,
        appDescriptionPsm2: appDescriptionPsm2 ?? this.appDescriptionPsm2,
        appDescription: appDescription ?? this.appDescription,
        appTitlePrince2: appTitlePrince2 ?? this.appTitlePrince2,
        appTitleCissp: appTitleCissp ?? this.appTitleCissp,
        appTitleComptiaLinux: appTitleComptiaLinux ?? this.appTitleComptiaLinux,
        appTitleComptiaCloud: appTitleComptiaCloud ?? this.appTitleComptiaCloud,
        appTitleComptiaProject:
            appTitleComptiaProject ?? this.appTitleComptiaProject,
        appTitleComptiaServer:
            appTitleComptiaServer ?? this.appTitleComptiaServer,
        appTitleSAFePOPM: appTitleSAFePOPM ?? this.appTitleSAFePOPM,
        appTitleSAFeSSM: appTitleSAFeSSM ?? this.appTitleSAFeSSM,
        appTitleComptiaSecurity:
            appTitleComptiaSecurity ?? this.appTitleComptiaSecurity,
        appTitleComptiaNetwork:
            appTitleComptiaNetwork ?? this.appTitleComptiaNetwork,
        appTitleComptiaITF: appTitleComptiaITF ?? this.appTitleComptiaITF,
        appTitleComptiaA: appTitleComptiaA ?? this.appTitleComptiaA,
        appTitleAwsMls: appTitleAwsMls ?? this.appTitleAwsMls,
        appTitleAwsSap: appTitleAwsSap ?? this.appTitleAwsSap,
        appTitleAwsDop: appTitleAwsDop ?? this.appTitleAwsDop,
        appTitleItil: appTitleItil ?? this.appTitleItil,
        appTitleAwsDva: appTitleAwsDva ?? this.appTitleAwsDva,
        appTitleAwsClf: appTitleAwsClf ?? this.appTitleAwsClf,
        appTitleAwsSoa: appTitleAwsSoa ?? this.appTitleAwsSoa,
        appTitleRmp: appTitleRmp ?? this.appTitleRmp,
        appTitleAwsSaa: appTitleAwsSaa ?? this.appTitleAwsSaa,
        appTitlePba: appTitlePba ?? this.appTitlePba,
        appTitlePfmp: appTitlePfmp ?? this.appTitlePfmp,
        appTitleEcba: appTitleEcba ?? this.appTitleEcba,
        appTitleISTQB: appTitleISTQB ?? this.appTitleISTQB,
        appTitlePgmp: appTitlePgmp ?? this.appTitlePgmp,
        appTitleCapm: appTitleCapm ?? this.appTitleCapm,
        appTitleCbap: appTitleCbap ?? this.appTitleCbap,
        appTitleCCBA: appTitleCCBA ?? this.appTitleCCBA,
        appTitlePmp: appTitlePmp ?? this.appTitlePmp,
        appTitlePsd: appTitlePsd ?? this.appTitlePsd,
        appTitleAcp: appTitleAcp ?? this.appTitleAcp,
        appTitlePsk: appTitlePsk ?? this.appTitlePsk,
        appTitleSps: appTitleSps ?? this.appTitleSps,
        appTitlePal: appTitlePal ?? this.appTitlePal,
        appTitlePspo2: appTitlePspo2 ?? this.appTitlePspo2,
        appTitlePspo: appTitlePspo ?? this.appTitlePspo,
        appTitlePsm2: appTitlePsm2 ?? this.appTitlePsm2,
        appTitle: appTitle ?? this.appTitle,
        appNamePrince2: appNamePrince2 ?? this.appNamePrince2,
        appNameCissp: appNameCissp ?? this.appNameCissp,
        appNameComptiaLinux: appNameComptiaLinux ?? this.appNameComptiaLinux,
        appNameComptiaCloud: appNameComptiaCloud ?? this.appNameComptiaCloud,
        appNameComptiaProject:
            appNameComptiaProject ?? this.appNameComptiaProject,
        appNameComptiaServer: appNameComptiaServer ?? this.appNameComptiaServer,
        appNameSAFePOPM: appNameSAFePOPM ?? this.appNameSAFePOPM,
        appNameSAFeSSM: appNameSAFeSSM ?? this.appNameSAFeSSM,
        appNameComptiaSecurity:
            appNameComptiaSecurity ?? this.appNameComptiaSecurity,
        appNameComptiaNetwork:
            appNameComptiaNetwork ?? this.appNameComptiaNetwork,
        appNameComptiaITF: appNameComptiaITF ?? this.appNameComptiaITF,
        appNameComptiaA: appNameComptiaA ?? this.appNameComptiaA,
        appNameAwsMls: appNameAwsMls ?? this.appNameAwsMls,
        appNameAwsSap: appNameAwsSap ?? this.appNameAwsSap,
        appNameAwsDop: appNameAwsDop ?? this.appNameAwsDop,
        appNameItil: appNameItil ?? this.appNameItil,
        appNameAwsDva: appNameAwsDva ?? this.appNameAwsDva,
        appNameAwsClf: appNameAwsClf ?? this.appNameAwsClf,
        appNameAwsSoa: appNameAwsSoa ?? this.appNameAwsSoa,
        appNameRmp: appNameRmp ?? this.appNameRmp,
        appNameAwsSaa: appNameAwsSaa ?? this.appNameAwsSaa,
        appNamePba: appNamePba ?? this.appNamePba,
        appNamePfmp: appNamePfmp ?? this.appNamePfmp,
        appNameEcba: appNameEcba ?? this.appNameEcba,
        appNameISTQB: appNameISTQB ?? this.appNameISTQB,
        appNamePgmp: appNamePgmp ?? this.appNamePgmp,
        appNameCapm: appNameCapm ?? this.appNameCapm,
        appNameCbap: appNameCbap ?? this.appNameCbap,
        appNameCCBA: appNameCCBA ?? this.appNameCCBA,
        appNamePmp: appNamePmp ?? this.appNamePmp,
        appNamePsd: appNamePsd ?? this.appNamePsd,
        appNameAcp: appNameAcp ?? this.appNameAcp,
        appNamePsk: appNamePsk ?? this.appNamePsk,
        appNameSps: appNameSps ?? this.appNameSps,
        appNamePal: appNamePal ?? this.appNamePal,
        appNamePspo2: appNamePspo2 ?? this.appNamePspo2,
        appNamePsm2: appNamePsm2 ?? this.appNamePsm2,
        appNamePspo: appNamePspo ?? this.appNamePspo,
        appName: appName ?? this.appName,
      );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AppLocalizationsData &&
          finishEarly == other.finishEarly &&
          redeemCodeError == other.redeemCodeError &&
          introduceDescription == other.introduceDescription &&
          introduceTitle == other.introduceTitle &&
          then == other.then &&
          emptyExamTips == other.emptyExamTips &&
          afterTrial == other.afterTrial &&
          trialDescription == other.trialDescription &&
          connectServerError == other.connectServerError &&
          hour == other.hour &&
          hours == other.hours &&
          noReport == other.noReport &&
          sendResponseSuccess == other.sendResponseSuccess &&
          sendResponse == other.sendResponse &&
          closeReport == other.closeReport &&
          scrumpassResponse == other.scrumpassResponse &&
          yourResponse == other.yourResponse &&
          reportContent == other.reportContent &&
          closed == other.closed &&
          reviewing == other.reviewing &&
          status == other.status &&
          reportedQuestion == other.reportedQuestion &&
          resultText == other.resultText &&
          listQuizShowcase2 == other.listQuizShowcase2 &&
          listQuizShowcase1 == other.listQuizShowcase1 &&
          homeShowcase5 == other.homeShowcase5 &&
          homeShowcase4 == other.homeShowcase4 &&
          homeShowcase3 == other.homeShowcase3 &&
          homeShowcase2 == other.homeShowcase2 &&
          homeShowcase1 == other.homeShowcase1 &&
          descriptionContent == other.descriptionContent &&
          aboutUsContent == other.aboutUsContent &&
          copyToClipboard == other.copyToClipboard &&
          androidLink == other.androidLink &&
          iosLink == other.iosLink &&
          getCertificate == other.getCertificate &&
          shareApp == other.shareApp &&
          tutorialTap == other.tutorialTap &&
          tutorialSwipeRight == other.tutorialSwipeRight &&
          tutorialSwipeLeft == other.tutorialSwipeLeft &&
          flashcardEndText == other.flashcardEndText &&
          learnedEverything == other.learnedEverything &&
          halfWayThere == other.halfWayThere &&
          congratulations == other.congratulations &&
          continueStudy == other.continueStudy &&
          restart == other.restart &&
          restartFlashCardPopup == other.restartFlashCardPopup &&
          favoriteWords == other.favoriteWords &&
          score == other.score &&
          testNumber == other.testNumber &&
          disclamer == other.disclamer &&
          goodToGo == other.goodToGo &&
          keepGoing == other.keepGoing &&
          notEnough == other.notEnough &&
          examPassRateDetail == other.examPassRateDetail &&
          noData == other.noData &&
          timeDetail == other.timeDetail &&
          questionDetail == other.questionDetail &&
          eachQuestion == other.eachQuestion &&
          totalTime == other.totalTime &&
          questionDone == other.questionDone &&
          streak == other.streak &&
          latestScore == other.latestScore &&
          passedExam == other.passedExam &&
          quizDone == other.quizDone &&
          avgScore == other.avgScore &&
          quizFlashCardDetail == other.quizFlashCardDetail &&
          flashCard == other.flashCard &&
          notReady == other.notReady &&
          examPassRate == other.examPassRate &&
          emptyField == other.emptyField &&
          notificationDetail == other.notificationDetail &&
          notification == other.notification &&
          questionrule == other.questionrule &&
          randomRule == other.randomRule &&
          getSupport == other.getSupport &&
          premiumVersion == other.premiumVersion &&
          someTips4U == other.someTips4U &&
          yourCode == other.yourCode &&
          enterRedeemCode == other.enterRedeemCode &&
          redeemCode == other.redeemCode &&
          updateYear == other.updateYear &&
          fully == other.fully &&
          detailedExplain == other.detailedExplain &&
          answersWith == other.answersWith &&
          allTheExams == other.allTheExams &&
          unlock == other.unlock &&
          startTrial == other.startTrial &&
          cancelAnyTime == other.cancelAnyTime &&
          fullyIUpdated == other.fullyIUpdated &&
          trialTitle == other.trialTitle &&
          sunday == other.sunday &&
          saturday == other.saturday &&
          friday == other.friday &&
          thursday == other.thursday &&
          wednesday == other.wednesday &&
          tuesday == other.tuesday &&
          monday == other.monday &&
          notificationStatus == other.notificationStatus &&
          notificationDays == other.notificationDays &&
          notificationTime == other.notificationTime &&
          expiresToday == other.expiresToday &&
          trial == other.trial &&
          dayLeft == other.dayLeft &&
          otherApp == other.otherApp &&
          randomSet == other.randomSet &&
          questionSet == other.questionSet &&
          doing == other.doing &&
          premium == other.premium &&
          free == other.free &&
          resultDetail == other.resultDetail &&
          version == other.version &&
          fail == other.fail &&
          pass == other.pass &&
          secondShort == other.secondShort &&
          term == other.term &&
          support == other.support &&
          privacyPolicy == other.privacyPolicy &&
          termOfUse == other.termOfUse &&
          percentageRequireToPass == other.percentageRequireToPass &&
          plan == other.plan &&
          resetData == other.resetData &&
          upgradeToGetFeature == other.upgradeToGetFeature &&
          deleteSuccess == other.deleteSuccess &&
          confirmDeleteQuestionReview == other.confirmDeleteQuestionReview &&
          upgrade == other.upgrade &&
          unlockAllExams == other.unlockAllExams &&
          unlockQR == other.unlockQR &&
          premiumFeature == other.premiumFeature &&
          hide == other.hide &&
          delete == other.delete &&
          viewExplanation == other.viewExplanation &&
          confirmDeleteQuestion == other.confirmDeleteQuestion &&
          yes == other.yes &&
          restorePurchase == other.restorePurchase &&
          thankForPurchase == other.thankForPurchase &&
          purchase == other.purchase &&
          alreadyPaid == other.alreadyPaid &&
          month == other.month &&
          months == other.months &&
          total == other.total &&
          monthly == other.monthly &&
          withExplain == other.withExplain &&
          unlockAllExam == other.unlockAllExam &&
          unlockFeature == other.unlockFeature &&
          accessPremium == other.accessPremium &&
          statistics == other.statistics &&
          noMarkedQuestion == other.noMarkedQuestion &&
          noWrongQuestion == other.noWrongQuestion &&
          questionReview == other.questionReview &&
          numberWrong == other.numberWrong &&
          markedQuestion == other.markedQuestion &&
          wrongQuestion == other.wrongQuestion &&
          unanswered == other.unanswered &&
          wrong == other.wrong &&
          correct == other.correct &&
          totalQuestion == other.totalQuestion &&
          yourName == other.yourName &&
          retryInternet == other.retryInternet &&
          viewExamList == other.viewExamList &&
          minutesShort == other.minutesShort &&
          left == other.left &&
          numberQuestion == other.numberQuestion &&
          reportDetail == other.reportDetail &&
          numReport == other.numReport &&
          cancel == other.cancel &&
          send == other.send &&
          reportSuccess == other.reportSuccess &&
          report == other.report &&
          notAnswered == other.notAnswered &&
          forceUpdateDialogContent == other.forceUpdateDialogContent &&
          rateUs == other.rateUs &&
          updateDialogContent == other.updateDialogContent &&
          noInternet == other.noInternet &&
          totalQuizDone == other.totalQuizDone &&
          recentScore == other.recentScore &&
          highScore == other.highScore &&
          fontExam == other.fontExam &&
          averageExamResult == other.averageExamResult &&
          serviceSatisfactionRate == other.serviceSatisfactionRate &&
          certificationPassRate == other.certificationPassRate &&
          yourMessage == other.yourMessage &&
          writeUsDirectly == other.writeUsDirectly &&
          contact == other.contact &&
          callUs == other.callUs &&
          connectUs == other.connectUs &&
          quizNumber == other.quizNumber &&
          confirmRetry == other.confirmRetry &&
          noUserName == other.noUserName &&
          noQuizResult == other.noQuizResult &&
          noInfo == other.noInfo &&
          errorDialog == other.errorDialog &&
          error == other.error &&
          confirmDoQuiz == other.confirmDoQuiz &&
          resumeQuizDialog == other.resumeQuizDialog &&
          bookmarked == other.bookmarked &&
          questionList == other.questionList &&
          allQuestion == other.allQuestion &&
          previous == other.previous &&
          finish == other.finish &&
          next == other.next &&
          questionOf == other.questionOf &&
          question == other.question &&
          timeUp == other.timeUp &&
          remainTime == other.remainTime &&
          quitQuizDialog == other.quitQuizDialog &&
          timeUpDialog == other.timeUpDialog &&
          no == other.no &&
          confirmEndQuiz == other.confirmEndQuiz &&
          mustSelectAnswer == other.mustSelectAnswer &&
          attention == other.attention &&
          productOfScrumPass == other.productOfScrumPass &&
          wellcomeEnterName == other.wellcomeEnterName &&
          allAns == other.allAns &&
          save == other.save &&
          noLimit == other.noLimit &&
          answered == other.answered &&
          noAnsSelected == other.noAnsSelected &&
          continueQ == other.continueQ &&
          time == other.time &&
          back == other.back &&
          intruction == other.intruction &&
          minPercentage == other.minPercentage &&
          timeEnd == other.timeEnd &&
          timeStart == other.timeStart &&
          quizDetail == other.quizDetail &&
          userDetail == other.userDetail &&
          noQuizAvaliable == other.noQuizAvaliable &&
          minutes == other.minutes &&
          timeLeft == other.timeLeft &&
          duration == other.duration &&
          numOfQuestion == other.numOfQuestion &&
          begin == other.begin &&
          answer == other.answer &&
          retry == other.retry &&
          home == other.home &&
          wrongAns == other.wrongAns &&
          rightAns == other.rightAns &&
          marked == other.marked &&
          finishDate == other.finishDate &&
          numHasDone == other.numHasDone &&
          numWrongAns == other.numWrongAns &&
          numRightAns == other.numRightAns &&
          passRequirement == other.passRequirement &&
          detail == other.detail &&
          resultList == other.resultList &&
          quizList == other.quizList &&
          aboutUs == other.aboutUs &&
          scrumguide == other.scrumguide &&
          result == other.result &&
          examPractice == other.examPractice &&
          numFailQuiz == other.numFailQuiz &&
          numPassQuiz == other.numPassQuiz &&
          passPercentage == other.passPercentage &&
          chooseLang == other.chooseLang &&
          lang == other.lang &&
          name == other.name &&
          hello == other.hello &&
          wellcomeTitlePrince2 == other.wellcomeTitlePrince2 &&
          wellcomeTitleCissp == other.wellcomeTitleCissp &&
          wellcomeTitleComptiaLinux == other.wellcomeTitleComptiaLinux &&
          wellcomeTitleComptiaCloud == other.wellcomeTitleComptiaCloud &&
          wellcomeTitleComptiaProject == other.wellcomeTitleComptiaProject &&
          wellcomeTitleComptiaServer == other.wellcomeTitleComptiaServer &&
          wellcomeTitleSAFePOPM == other.wellcomeTitleSAFePOPM &&
          wellcomeTitleSAFeSSM == other.wellcomeTitleSAFeSSM &&
          wellcomeTitleComptiaSecurity == other.wellcomeTitleComptiaSecurity &&
          wellcomeTitleComptiaNetwork == other.wellcomeTitleComptiaNetwork &&
          wellcomeTitleComptiaITF == other.wellcomeTitleComptiaITF &&
          wellcomeTitleComptiaA == other.wellcomeTitleComptiaA &&
          wellcomeTitleAwsMlsIos == other.wellcomeTitleAwsMlsIos &&
          wellcomeTitleAwsMls == other.wellcomeTitleAwsMls &&
          wellcomeTitleAwsSap == other.wellcomeTitleAwsSap &&
          wellcomeTitleAwsDop == other.wellcomeTitleAwsDop &&
          wellcomeTitleItil == other.wellcomeTitleItil &&
          wellcomeTitleAwsDva == other.wellcomeTitleAwsDva &&
          wellcomeTitleAwsClf == other.wellcomeTitleAwsClf &&
          wellcomeTitleAwsSoa == other.wellcomeTitleAwsSoa &&
          wellcomeTitleRmp == other.wellcomeTitleRmp &&
          wellcomeTitleAwsSaa == other.wellcomeTitleAwsSaa &&
          wellcomeTitlePba == other.wellcomeTitlePba &&
          wellcomeTitlePfmp == other.wellcomeTitlePfmp &&
          wellcomeTitleEcba == other.wellcomeTitleEcba &&
          wellcomeTitleISTQB == other.wellcomeTitleISTQB &&
          wellcomeTitlePgmp == other.wellcomeTitlePgmp &&
          wellcomeTitleCapm == other.wellcomeTitleCapm &&
          wellcomeTitleCbap == other.wellcomeTitleCbap &&
          wellcomeTitleCCBA == other.wellcomeTitleCCBA &&
          wellcomeTitlePmp == other.wellcomeTitlePmp &&
          wellcomeTitlePsd == other.wellcomeTitlePsd &&
          wellcomeTitleAcp == other.wellcomeTitleAcp &&
          wellcomeTitlePsk == other.wellcomeTitlePsk &&
          wellcomeTitleSps == other.wellcomeTitleSps &&
          wellcomeTitlePal == other.wellcomeTitlePal &&
          wellcomeTitlePsm2 == other.wellcomeTitlePsm2 &&
          wellcomeTitlePspo2 == other.wellcomeTitlePspo2 &&
          wellcomeTitlePspo == other.wellcomeTitlePspo &&
          wellcomeTitle == other.wellcomeTitle &&
          appDescriptionPrince2 == other.appDescriptionPrince2 &&
          appDescriptionCISSP == other.appDescriptionCISSP &&
          appDescriptionComptiaLinux == other.appDescriptionComptiaLinux &&
          appDescriptionComptiaCloud == other.appDescriptionComptiaCloud &&
          appDescriptionComptiaProject == other.appDescriptionComptiaProject &&
          appDescriptionComptiaServer == other.appDescriptionComptiaServer &&
          appDescriptionSAFePOPM == other.appDescriptionSAFePOPM &&
          appDescriptionSAFeSSM == other.appDescriptionSAFeSSM &&
          appDescriptionComptiaSecurity ==
              other.appDescriptionComptiaSecurity &&
          appDescriptionComptiaNetwork == other.appDescriptionComptiaNetwork &&
          appDescriptionComptiaITF == other.appDescriptionComptiaITF &&
          appDescriptionComptiaA == other.appDescriptionComptiaA &&
          appDescriptionAwsMls == other.appDescriptionAwsMls &&
          appDescriptionAwsSap == other.appDescriptionAwsSap &&
          appDescriptionAwsDop == other.appDescriptionAwsDop &&
          appDescriptionItil == other.appDescriptionItil &&
          appDescriptionAwsDva == other.appDescriptionAwsDva &&
          appDescriptionAwsClf == other.appDescriptionAwsClf &&
          appDescriptionAwsSoa == other.appDescriptionAwsSoa &&
          appDescriptionRmp == other.appDescriptionRmp &&
          appDescriptionAwsSaa == other.appDescriptionAwsSaa &&
          appDescriptionPba == other.appDescriptionPba &&
          appDescriptionPfmp == other.appDescriptionPfmp &&
          appDescriptionEcba == other.appDescriptionEcba &&
          appDescriptionISTQB == other.appDescriptionISTQB &&
          appDescriptionPgmp == other.appDescriptionPgmp &&
          appDescriptionCapm == other.appDescriptionCapm &&
          appDescriptionCbap == other.appDescriptionCbap &&
          appDescriptionCCBA == other.appDescriptionCCBA &&
          appDescriptionPmp == other.appDescriptionPmp &&
          appDescriptionPsd == other.appDescriptionPsd &&
          appDescriptionAcp == other.appDescriptionAcp &&
          appDescriptionPsk == other.appDescriptionPsk &&
          appDescriptionSps == other.appDescriptionSps &&
          appDescriptionPal == other.appDescriptionPal &&
          appDescriptionPspo2 == other.appDescriptionPspo2 &&
          appDescriptionPspo == other.appDescriptionPspo &&
          appDescriptionPsm2 == other.appDescriptionPsm2 &&
          appDescription == other.appDescription &&
          appTitlePrince2 == other.appTitlePrince2 &&
          appTitleCissp == other.appTitleCissp &&
          appTitleComptiaLinux == other.appTitleComptiaLinux &&
          appTitleComptiaCloud == other.appTitleComptiaCloud &&
          appTitleComptiaProject == other.appTitleComptiaProject &&
          appTitleComptiaServer == other.appTitleComptiaServer &&
          appTitleSAFePOPM == other.appTitleSAFePOPM &&
          appTitleSAFeSSM == other.appTitleSAFeSSM &&
          appTitleComptiaSecurity == other.appTitleComptiaSecurity &&
          appTitleComptiaNetwork == other.appTitleComptiaNetwork &&
          appTitleComptiaITF == other.appTitleComptiaITF &&
          appTitleComptiaA == other.appTitleComptiaA &&
          appTitleAwsMls == other.appTitleAwsMls &&
          appTitleAwsSap == other.appTitleAwsSap &&
          appTitleAwsDop == other.appTitleAwsDop &&
          appTitleItil == other.appTitleItil &&
          appTitleAwsDva == other.appTitleAwsDva &&
          appTitleAwsClf == other.appTitleAwsClf &&
          appTitleAwsSoa == other.appTitleAwsSoa &&
          appTitleRmp == other.appTitleRmp &&
          appTitleAwsSaa == other.appTitleAwsSaa &&
          appTitlePba == other.appTitlePba &&
          appTitlePfmp == other.appTitlePfmp &&
          appTitleEcba == other.appTitleEcba &&
          appTitleISTQB == other.appTitleISTQB &&
          appTitlePgmp == other.appTitlePgmp &&
          appTitleCapm == other.appTitleCapm &&
          appTitleCbap == other.appTitleCbap &&
          appTitleCCBA == other.appTitleCCBA &&
          appTitlePmp == other.appTitlePmp &&
          appTitlePsd == other.appTitlePsd &&
          appTitleAcp == other.appTitleAcp &&
          appTitlePsk == other.appTitlePsk &&
          appTitleSps == other.appTitleSps &&
          appTitlePal == other.appTitlePal &&
          appTitlePspo2 == other.appTitlePspo2 &&
          appTitlePspo == other.appTitlePspo &&
          appTitlePsm2 == other.appTitlePsm2 &&
          appTitle == other.appTitle &&
          appNamePrince2 == other.appNamePrince2 &&
          appNameCissp == other.appNameCissp &&
          appNameComptiaLinux == other.appNameComptiaLinux &&
          appNameComptiaCloud == other.appNameComptiaCloud &&
          appNameComptiaProject == other.appNameComptiaProject &&
          appNameComptiaServer == other.appNameComptiaServer &&
          appNameSAFePOPM == other.appNameSAFePOPM &&
          appNameSAFeSSM == other.appNameSAFeSSM &&
          appNameComptiaSecurity == other.appNameComptiaSecurity &&
          appNameComptiaNetwork == other.appNameComptiaNetwork &&
          appNameComptiaITF == other.appNameComptiaITF &&
          appNameComptiaA == other.appNameComptiaA &&
          appNameAwsMls == other.appNameAwsMls &&
          appNameAwsSap == other.appNameAwsSap &&
          appNameAwsDop == other.appNameAwsDop &&
          appNameItil == other.appNameItil &&
          appNameAwsDva == other.appNameAwsDva &&
          appNameAwsClf == other.appNameAwsClf &&
          appNameAwsSoa == other.appNameAwsSoa &&
          appNameRmp == other.appNameRmp &&
          appNameAwsSaa == other.appNameAwsSaa &&
          appNamePba == other.appNamePba &&
          appNamePfmp == other.appNamePfmp &&
          appNameEcba == other.appNameEcba &&
          appNameISTQB == other.appNameISTQB &&
          appNamePgmp == other.appNamePgmp &&
          appNameCapm == other.appNameCapm &&
          appNameCbap == other.appNameCbap &&
          appNameCCBA == other.appNameCCBA &&
          appNamePmp == other.appNamePmp &&
          appNamePsd == other.appNamePsd &&
          appNameAcp == other.appNameAcp &&
          appNamePsk == other.appNamePsk &&
          appNameSps == other.appNameSps &&
          appNamePal == other.appNamePal &&
          appNamePspo2 == other.appNamePspo2 &&
          appNamePsm2 == other.appNamePsm2 &&
          appNamePspo == other.appNamePspo &&
          appName == other.appName);
  @override
  int get hashCode =>
      runtimeType.hashCode ^
      finishEarly.hashCode ^
      redeemCodeError.hashCode ^
      introduceDescription.hashCode ^
      introduceTitle.hashCode ^
      then.hashCode ^
      emptyExamTips.hashCode ^
      afterTrial.hashCode ^
      trialDescription.hashCode ^
      connectServerError.hashCode ^
      hour.hashCode ^
      hours.hashCode ^
      noReport.hashCode ^
      sendResponseSuccess.hashCode ^
      sendResponse.hashCode ^
      closeReport.hashCode ^
      scrumpassResponse.hashCode ^
      yourResponse.hashCode ^
      reportContent.hashCode ^
      closed.hashCode ^
      reviewing.hashCode ^
      status.hashCode ^
      reportedQuestion.hashCode ^
      resultText.hashCode ^
      listQuizShowcase2.hashCode ^
      listQuizShowcase1.hashCode ^
      homeShowcase5.hashCode ^
      homeShowcase4.hashCode ^
      homeShowcase3.hashCode ^
      homeShowcase2.hashCode ^
      homeShowcase1.hashCode ^
      descriptionContent.hashCode ^
      aboutUsContent.hashCode ^
      copyToClipboard.hashCode ^
      androidLink.hashCode ^
      iosLink.hashCode ^
      getCertificate.hashCode ^
      shareApp.hashCode ^
      tutorialTap.hashCode ^
      tutorialSwipeRight.hashCode ^
      tutorialSwipeLeft.hashCode ^
      flashcardEndText.hashCode ^
      learnedEverything.hashCode ^
      halfWayThere.hashCode ^
      congratulations.hashCode ^
      continueStudy.hashCode ^
      restart.hashCode ^
      restartFlashCardPopup.hashCode ^
      favoriteWords.hashCode ^
      score.hashCode ^
      testNumber.hashCode ^
      disclamer.hashCode ^
      goodToGo.hashCode ^
      keepGoing.hashCode ^
      notEnough.hashCode ^
      examPassRateDetail.hashCode ^
      noData.hashCode ^
      timeDetail.hashCode ^
      questionDetail.hashCode ^
      eachQuestion.hashCode ^
      totalTime.hashCode ^
      questionDone.hashCode ^
      streak.hashCode ^
      latestScore.hashCode ^
      passedExam.hashCode ^
      quizDone.hashCode ^
      avgScore.hashCode ^
      quizFlashCardDetail.hashCode ^
      flashCard.hashCode ^
      notReady.hashCode ^
      examPassRate.hashCode ^
      emptyField.hashCode ^
      notificationDetail.hashCode ^
      notification.hashCode ^
      questionrule.hashCode ^
      randomRule.hashCode ^
      getSupport.hashCode ^
      premiumVersion.hashCode ^
      someTips4U.hashCode ^
      yourCode.hashCode ^
      enterRedeemCode.hashCode ^
      redeemCode.hashCode ^
      updateYear.hashCode ^
      fully.hashCode ^
      detailedExplain.hashCode ^
      answersWith.hashCode ^
      allTheExams.hashCode ^
      unlock.hashCode ^
      startTrial.hashCode ^
      cancelAnyTime.hashCode ^
      fullyIUpdated.hashCode ^
      trialTitle.hashCode ^
      sunday.hashCode ^
      saturday.hashCode ^
      friday.hashCode ^
      thursday.hashCode ^
      wednesday.hashCode ^
      tuesday.hashCode ^
      monday.hashCode ^
      notificationStatus.hashCode ^
      notificationDays.hashCode ^
      notificationTime.hashCode ^
      expiresToday.hashCode ^
      trial.hashCode ^
      dayLeft.hashCode ^
      otherApp.hashCode ^
      randomSet.hashCode ^
      questionSet.hashCode ^
      doing.hashCode ^
      premium.hashCode ^
      free.hashCode ^
      resultDetail.hashCode ^
      version.hashCode ^
      fail.hashCode ^
      pass.hashCode ^
      secondShort.hashCode ^
      term.hashCode ^
      support.hashCode ^
      privacyPolicy.hashCode ^
      termOfUse.hashCode ^
      percentageRequireToPass.hashCode ^
      plan.hashCode ^
      resetData.hashCode ^
      upgradeToGetFeature.hashCode ^
      deleteSuccess.hashCode ^
      confirmDeleteQuestionReview.hashCode ^
      upgrade.hashCode ^
      unlockAllExams.hashCode ^
      unlockQR.hashCode ^
      premiumFeature.hashCode ^
      hide.hashCode ^
      delete.hashCode ^
      viewExplanation.hashCode ^
      confirmDeleteQuestion.hashCode ^
      yes.hashCode ^
      restorePurchase.hashCode ^
      thankForPurchase.hashCode ^
      purchase.hashCode ^
      alreadyPaid.hashCode ^
      month.hashCode ^
      months.hashCode ^
      total.hashCode ^
      monthly.hashCode ^
      withExplain.hashCode ^
      unlockAllExam.hashCode ^
      unlockFeature.hashCode ^
      accessPremium.hashCode ^
      statistics.hashCode ^
      noMarkedQuestion.hashCode ^
      noWrongQuestion.hashCode ^
      questionReview.hashCode ^
      numberWrong.hashCode ^
      markedQuestion.hashCode ^
      wrongQuestion.hashCode ^
      unanswered.hashCode ^
      wrong.hashCode ^
      correct.hashCode ^
      totalQuestion.hashCode ^
      yourName.hashCode ^
      retryInternet.hashCode ^
      viewExamList.hashCode ^
      minutesShort.hashCode ^
      left.hashCode ^
      numberQuestion.hashCode ^
      reportDetail.hashCode ^
      numReport.hashCode ^
      cancel.hashCode ^
      send.hashCode ^
      reportSuccess.hashCode ^
      report.hashCode ^
      notAnswered.hashCode ^
      forceUpdateDialogContent.hashCode ^
      rateUs.hashCode ^
      updateDialogContent.hashCode ^
      noInternet.hashCode ^
      totalQuizDone.hashCode ^
      recentScore.hashCode ^
      highScore.hashCode ^
      fontExam.hashCode ^
      averageExamResult.hashCode ^
      serviceSatisfactionRate.hashCode ^
      certificationPassRate.hashCode ^
      yourMessage.hashCode ^
      writeUsDirectly.hashCode ^
      contact.hashCode ^
      callUs.hashCode ^
      connectUs.hashCode ^
      quizNumber.hashCode ^
      confirmRetry.hashCode ^
      noUserName.hashCode ^
      noQuizResult.hashCode ^
      noInfo.hashCode ^
      errorDialog.hashCode ^
      error.hashCode ^
      confirmDoQuiz.hashCode ^
      resumeQuizDialog.hashCode ^
      bookmarked.hashCode ^
      questionList.hashCode ^
      allQuestion.hashCode ^
      previous.hashCode ^
      finish.hashCode ^
      next.hashCode ^
      questionOf.hashCode ^
      question.hashCode ^
      timeUp.hashCode ^
      remainTime.hashCode ^
      quitQuizDialog.hashCode ^
      timeUpDialog.hashCode ^
      no.hashCode ^
      confirmEndQuiz.hashCode ^
      mustSelectAnswer.hashCode ^
      attention.hashCode ^
      productOfScrumPass.hashCode ^
      wellcomeEnterName.hashCode ^
      allAns.hashCode ^
      save.hashCode ^
      noLimit.hashCode ^
      answered.hashCode ^
      noAnsSelected.hashCode ^
      continueQ.hashCode ^
      time.hashCode ^
      back.hashCode ^
      intruction.hashCode ^
      minPercentage.hashCode ^
      timeEnd.hashCode ^
      timeStart.hashCode ^
      quizDetail.hashCode ^
      userDetail.hashCode ^
      noQuizAvaliable.hashCode ^
      minutes.hashCode ^
      timeLeft.hashCode ^
      duration.hashCode ^
      numOfQuestion.hashCode ^
      begin.hashCode ^
      answer.hashCode ^
      retry.hashCode ^
      home.hashCode ^
      wrongAns.hashCode ^
      rightAns.hashCode ^
      marked.hashCode ^
      finishDate.hashCode ^
      numHasDone.hashCode ^
      numWrongAns.hashCode ^
      numRightAns.hashCode ^
      passRequirement.hashCode ^
      detail.hashCode ^
      resultList.hashCode ^
      quizList.hashCode ^
      aboutUs.hashCode ^
      scrumguide.hashCode ^
      result.hashCode ^
      examPractice.hashCode ^
      numFailQuiz.hashCode ^
      numPassQuiz.hashCode ^
      passPercentage.hashCode ^
      chooseLang.hashCode ^
      lang.hashCode ^
      name.hashCode ^
      hello.hashCode ^
      wellcomeTitlePrince2.hashCode ^
      wellcomeTitleCissp.hashCode ^
      wellcomeTitleComptiaLinux.hashCode ^
      wellcomeTitleComptiaCloud.hashCode ^
      wellcomeTitleComptiaProject.hashCode ^
      wellcomeTitleComptiaServer.hashCode ^
      wellcomeTitleSAFePOPM.hashCode ^
      wellcomeTitleSAFeSSM.hashCode ^
      wellcomeTitleComptiaSecurity.hashCode ^
      wellcomeTitleComptiaNetwork.hashCode ^
      wellcomeTitleComptiaITF.hashCode ^
      wellcomeTitleComptiaA.hashCode ^
      wellcomeTitleAwsMlsIos.hashCode ^
      wellcomeTitleAwsMls.hashCode ^
      wellcomeTitleAwsSap.hashCode ^
      wellcomeTitleAwsDop.hashCode ^
      wellcomeTitleItil.hashCode ^
      wellcomeTitleAwsDva.hashCode ^
      wellcomeTitleAwsClf.hashCode ^
      wellcomeTitleAwsSoa.hashCode ^
      wellcomeTitleRmp.hashCode ^
      wellcomeTitleAwsSaa.hashCode ^
      wellcomeTitlePba.hashCode ^
      wellcomeTitlePfmp.hashCode ^
      wellcomeTitleEcba.hashCode ^
      wellcomeTitleISTQB.hashCode ^
      wellcomeTitlePgmp.hashCode ^
      wellcomeTitleCapm.hashCode ^
      wellcomeTitleCbap.hashCode ^
      wellcomeTitleCCBA.hashCode ^
      wellcomeTitlePmp.hashCode ^
      wellcomeTitlePsd.hashCode ^
      wellcomeTitleAcp.hashCode ^
      wellcomeTitlePsk.hashCode ^
      wellcomeTitleSps.hashCode ^
      wellcomeTitlePal.hashCode ^
      wellcomeTitlePsm2.hashCode ^
      wellcomeTitlePspo2.hashCode ^
      wellcomeTitlePspo.hashCode ^
      wellcomeTitle.hashCode ^
      appDescriptionPrince2.hashCode ^
      appDescriptionCISSP.hashCode ^
      appDescriptionComptiaLinux.hashCode ^
      appDescriptionComptiaCloud.hashCode ^
      appDescriptionComptiaProject.hashCode ^
      appDescriptionComptiaServer.hashCode ^
      appDescriptionSAFePOPM.hashCode ^
      appDescriptionSAFeSSM.hashCode ^
      appDescriptionComptiaSecurity.hashCode ^
      appDescriptionComptiaNetwork.hashCode ^
      appDescriptionComptiaITF.hashCode ^
      appDescriptionComptiaA.hashCode ^
      appDescriptionAwsMls.hashCode ^
      appDescriptionAwsSap.hashCode ^
      appDescriptionAwsDop.hashCode ^
      appDescriptionItil.hashCode ^
      appDescriptionAwsDva.hashCode ^
      appDescriptionAwsClf.hashCode ^
      appDescriptionAwsSoa.hashCode ^
      appDescriptionRmp.hashCode ^
      appDescriptionAwsSaa.hashCode ^
      appDescriptionPba.hashCode ^
      appDescriptionPfmp.hashCode ^
      appDescriptionEcba.hashCode ^
      appDescriptionISTQB.hashCode ^
      appDescriptionPgmp.hashCode ^
      appDescriptionCapm.hashCode ^
      appDescriptionCbap.hashCode ^
      appDescriptionCCBA.hashCode ^
      appDescriptionPmp.hashCode ^
      appDescriptionPsd.hashCode ^
      appDescriptionAcp.hashCode ^
      appDescriptionPsk.hashCode ^
      appDescriptionSps.hashCode ^
      appDescriptionPal.hashCode ^
      appDescriptionPspo2.hashCode ^
      appDescriptionPspo.hashCode ^
      appDescriptionPsm2.hashCode ^
      appDescription.hashCode ^
      appTitlePrince2.hashCode ^
      appTitleCissp.hashCode ^
      appTitleComptiaLinux.hashCode ^
      appTitleComptiaCloud.hashCode ^
      appTitleComptiaProject.hashCode ^
      appTitleComptiaServer.hashCode ^
      appTitleSAFePOPM.hashCode ^
      appTitleSAFeSSM.hashCode ^
      appTitleComptiaSecurity.hashCode ^
      appTitleComptiaNetwork.hashCode ^
      appTitleComptiaITF.hashCode ^
      appTitleComptiaA.hashCode ^
      appTitleAwsMls.hashCode ^
      appTitleAwsSap.hashCode ^
      appTitleAwsDop.hashCode ^
      appTitleItil.hashCode ^
      appTitleAwsDva.hashCode ^
      appTitleAwsClf.hashCode ^
      appTitleAwsSoa.hashCode ^
      appTitleRmp.hashCode ^
      appTitleAwsSaa.hashCode ^
      appTitlePba.hashCode ^
      appTitlePfmp.hashCode ^
      appTitleEcba.hashCode ^
      appTitleISTQB.hashCode ^
      appTitlePgmp.hashCode ^
      appTitleCapm.hashCode ^
      appTitleCbap.hashCode ^
      appTitleCCBA.hashCode ^
      appTitlePmp.hashCode ^
      appTitlePsd.hashCode ^
      appTitleAcp.hashCode ^
      appTitlePsk.hashCode ^
      appTitleSps.hashCode ^
      appTitlePal.hashCode ^
      appTitlePspo2.hashCode ^
      appTitlePspo.hashCode ^
      appTitlePsm2.hashCode ^
      appTitle.hashCode ^
      appNamePrince2.hashCode ^
      appNameCissp.hashCode ^
      appNameComptiaLinux.hashCode ^
      appNameComptiaCloud.hashCode ^
      appNameComptiaProject.hashCode ^
      appNameComptiaServer.hashCode ^
      appNameSAFePOPM.hashCode ^
      appNameSAFeSSM.hashCode ^
      appNameComptiaSecurity.hashCode ^
      appNameComptiaNetwork.hashCode ^
      appNameComptiaITF.hashCode ^
      appNameComptiaA.hashCode ^
      appNameAwsMls.hashCode ^
      appNameAwsSap.hashCode ^
      appNameAwsDop.hashCode ^
      appNameItil.hashCode ^
      appNameAwsDva.hashCode ^
      appNameAwsClf.hashCode ^
      appNameAwsSoa.hashCode ^
      appNameRmp.hashCode ^
      appNameAwsSaa.hashCode ^
      appNamePba.hashCode ^
      appNamePfmp.hashCode ^
      appNameEcba.hashCode ^
      appNameISTQB.hashCode ^
      appNamePgmp.hashCode ^
      appNameCapm.hashCode ^
      appNameCbap.hashCode ^
      appNameCCBA.hashCode ^
      appNamePmp.hashCode ^
      appNamePsd.hashCode ^
      appNameAcp.hashCode ^
      appNamePsk.hashCode ^
      appNameSps.hashCode ^
      appNamePal.hashCode ^
      appNamePspo2.hashCode ^
      appNamePsm2.hashCode ^
      appNamePspo.hashCode ^
      appName.hashCode;
}
