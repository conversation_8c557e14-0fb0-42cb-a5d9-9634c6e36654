import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:psm_app/data_sources/api_servies.dart' hide appkey;
import 'package:psm_app/view/report_detail.dart';
import 'package:psm_app/view/widgets/question_review_explanation.dart';
import 'package:rxdart/rxdart.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/core.dart';
import '../globals.dart';
import '../helper/helper.dart';
import '../localization.dart';
import '../models/report_model.dart';
import 'detail_result/detail_result.dart';
import 'exam/new_quiz_list.dart';
import 'home.dart';
import 'payment/payment.dart';
import 'widgets/error_dialog.dart';
import 'widgets/success_dialog.dart';
import 'package:html/parser.dart';

int type = 1;
bool isDeleteMode = false;
String search = "";
bool isVisibleSearchBox = false;
bool shouldFocusTextField = false;

class QuestionReview extends StatefulWidget {
  const QuestionReview({Key? key}) : super(key: key);

  @override
  State<QuestionReview> createState() => _QuestionReviewState();
}

class _QuestionReviewState extends State<QuestionReview> {
  List bookmark = [];
  List questions = [];
  List getBookmark = [];
  List getQuestions = [];
  bool done = false;
  final deleteQuestionIcon = BehaviorSubject<bool>.seeded(false);
  final deleteBookmarkIcon = BehaviorSubject<bool>.seeded(false);
  Future<dynamic> getLocalData() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    if (!done) {
      final String incorrectString =
          await prefs.getString('incorrectQuestion') ?? "";
      if (incorrectString != "") getQuestions = jsonDecode(incorrectString);
      for (var i = 0; i < getQuestions.length; i++) {
        if (getQuestions[i]['environment'] == null ||
            getQuestions[i]['environment'] == "" ||
            getQuestions[i]['environment'] == environment.text) {
          questions.add(getQuestions[i]);
        }
      }
      final String bookmarkString =
          await prefs.getString('bookmarkQuestion') ?? "";
      if (bookmarkString != "") getBookmark = jsonDecode(bookmarkString);
      for (var i = 0; i < getBookmark.length; i++) {
        if (getBookmark[i]['environment'] == null ||
            getBookmark[i]['environment'] == "" ||
            getBookmark[i]['environment'] == environment.text) {
          bookmark.add(getBookmark[i]);
        }
      }
    }
    if (bookmark.isNotEmpty) {
      deleteBookmarkIcon.value = true;
    }
    if (questions.isNotEmpty) {
      deleteQuestionIcon.value = true;
    }
    done = true;
    return true;
  }

  callbackSetState() {
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    logPage("Review Question");
    isDeleteMode = false;
    type = 1;
    isVisibleSearchBox = false;
    search = "";
    shouldFocusTextField = false;
  }

  Future changeFilterRight() async {
    setState(() {
      if (type == 1) {
        type = 2;
      }
    });
  }

  Future changeFilterLeft() async {
    setState(() {
      if (type == 2) {
        type = 1;
      }
    });
  }

  bool showSearchDeleteIcon() {
    if (Common.premium == false) {
      return false;
    }
    if (Common.premium == true && type == 3) {
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => !(Navigator.of(context).userGestureInProgress),
      child: GestureDetector(
        onHorizontalDragEnd: (DragEndDetails details) {
          if (details.primaryVelocity! > 0) {
            // User swiped Left
            changeFilterLeft();
          } else if (details.primaryVelocity! < 0) {
            // User swiped Right
            changeFilterRight();
          }
        },
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: LoaderOverlay(
          useDefaultLoading: false,
          overlayWidgetBuilder: (_) => const Center(
            child: SpinKitRing(
              color: Colors.white,
              size: 50.0,
            ),
          ),
          overlayColor: Colors.black.withOpacity(0.8),
          child: Scaffold(
            appBar: AppBar(
              centerTitle: true,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back_ios_new),
                iconSize: 20.0,
                color: AppColors.blackText,
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              title: Text(
                // AppLocalizations.of(context).questionReview,
                AppLocalizations.of(context).questionReview,
                style: AppStyles.appBarTitle,
              ),
              actions: [
                StreamBuilder(
                    stream: deleteQuestionIcon.stream,
                    builder: (context, snapshot) {
                      return Row(
                        children: [
                          if (!isDeleteMode && showSearchDeleteIcon()) ...{
                            if ((type == 1 && deleteQuestionIcon.value) ||
                                (type == 2 && deleteBookmarkIcon.value)) ...{
                              !isVisibleSearchBox
                                  ? GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          isVisibleSearchBox = true;
                                          shouldFocusTextField = true;
                                        });
                                      },
                                      child: Padding(
                                          padding:
                                              const EdgeInsets.only(right: 10),
                                          child: Icon(
                                            Icons.search,
                                            color: AppColors.blackText,
                                          )),
                                    )
                                  : Container(),
                              Padding(
                                padding: const EdgeInsets.only(right: 20),
                                child: InkWell(
                                  child: SvgPicture.asset(
                                    'assets/images/delete_icon.svg',
                                    color: AppColors.blackText,
                                  ),
                                  onTap: () {
                                    setState(() {
                                      isDeleteMode = true;
                                    });
                                  },
                                ),
                              )
                            }
                          }
                        ],
                      );
                    })
              ],
              backgroundColor: Colors.white,
              elevation: 0,
            ),
            body: FutureBuilder(
              future: getLocalData(),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  final data = {
                    'type': 'Runtime Error',
                    'error': snapshot.error.toString()
                  };
                  ApiServices().sendErrorReport(data);
                }
                if (snapshot.hasData) {
                  return BoMe(
                    questions: questions,
                    bookmark: bookmark,
                    callbackSetState: callbackSetState,
                  );
                } else {
                  return const Center(child: CircularProgressIndicator());
                }
              },
            ),
          ),
        ),
      ),
    );
  }
}

class BoMe extends StatefulWidget {
  const BoMe(
      {Key? key,
      required this.questions,
      required this.bookmark,
      required this.callbackSetState})
      : super(key: key);
  final List bookmark;
  final List questions;
  final Function callbackSetState;
  @override
  _BoMeState createState() => _BoMeState();
}

class _BoMeState extends State<BoMe> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late TabController _controllerTab;
  bool opened = false;
  List deleteWrongQuestion = [];
  List deleteMarkedQuestion = [];
  List listDeleteWrongQuestion = [];
  List listDeleteMarkedQuestion = [];
  final List alphabet = [
    'A. ',
    'B. ',
    'C. ',
    'D. ',
    'E. ',
    'F. ',
    'G. ',
    'H. ',
    'I.',
    'K. '
  ];
  List sortQuestion = [];
  var searchTextController = TextEditingController();
  final countReport = BehaviorSubject<int>.seeded(0);
  final ValueNotifier<double> notifier = ValueNotifier(0);

  countIncorrectQuestion() {
    List countedQuestion = [];
    List result = [];
    for (int i = 0; i < widget.questions.length; i++) {
      int count = 0;
      var questionId = widget.questions[i]['id'];
      if (countedQuestion.contains(questionId)) {
        continue;
      } else {
        widget.questions
            .forEach((value) => {if (value['id'] == questionId) count++});
      }
      widget.questions[i]['count'] = count;
      result.add(widget.questions[i]);
      countedQuestion.add(questionId);
    }
    result.sort((a, b) => a['count'].compareTo(b['count']));
    sortQuestion = result.reversed.toList();
    if (deleteWrongQuestion.isEmpty) initDeleteWrongQuestion();
  }

  List sortBookmark = [];
  late Future<List<Report>> report;
  countBookmarkQuestion() {
    List countedBookmarkId = [];
    List result = [];
    for (int i = 0; i < widget.bookmark.length; i++) {
      var questionId = widget.bookmark[i]['id'];
      if (countedBookmarkId.contains(questionId)) {
        continue;
      }
      result.add(widget.bookmark[i]);
      countedBookmarkId.add(questionId);
    }
    sortBookmark = result.reversed.toList();
    if (deleteMarkedQuestion.isEmpty) initDeleteMarkedQuestion();
  }

  initDeleteWrongQuestion() {
    deleteWrongQuestion = List.filled(sortQuestion.length, false);
  }

  initDeleteMarkedQuestion() {
    deleteMarkedQuestion = List.filled(sortBookmark.length, false);
  }

  Future<List> getData() async {
    countIncorrectQuestion();
    countBookmarkQuestion();
    int marked = 0;
    int incorrect = 0;
    List<int> arr;
    // for (var i = 0; i < widget.questions.length; i++) {
    //   if (widget.bookmark[i] == 1) {
    //     Marked++;
    //   }
    //   if (widget.correctList[i]) {
    //     Correct++;
    //   }
    //   if (widget.answersText[i] == '' ||
    //       widget.answersText[i].toString() == "[]") {
    //     UnAnswered++;
    //   }
    // }
    if (search != "") {
      for (var i = 0; i < sortBookmark.length; i++) {
        if (sortBookmark[i]["question"].toLowerCase().contains(search)) {
          marked++;
        }
      }
      for (var i = 0; i < sortQuestion.length; i++) {
        if (sortQuestion[i]["question"].toLowerCase().contains(search)) {
          incorrect++;
        }
      }
    } else {
      marked = sortBookmark.length;
      incorrect = sortQuestion.length;
    }
    arr = [marked, incorrect];
    return arr;
  }

  Future<List<Report>> getReport() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final json = preferences.getString('reportedQuestion');
    List<Report> result = [];
    if (json?.isNotEmpty ?? false) {
      result = Report.fromList(
          List<Map<String, dynamic>>.from(jsonDecode(json ?? '')));
    }
    countReport.value = result.length;
    return result;
  }

  addListWrongDeleteQuestion(id) {
    if (!listDeleteWrongQuestion.contains(id)) {
      listDeleteWrongQuestion.add(id);
    } else {
      listDeleteWrongQuestion.remove(id);
    }
  }

  addListMarkedDeleteQuestion(id) {
    if (!listDeleteMarkedQuestion.contains(id)) {
      listDeleteMarkedQuestion.add(id);
    } else {
      listDeleteMarkedQuestion.remove(id);
    }
  }

  deleteWrongQuestionList() {
    List temp = [];
    for (int i = widget.questions.length - 1; i >= 0; i--) {
      if (listDeleteWrongQuestion.contains(widget.questions[i]['id'])) {
        temp.add(i);
      }
    }
    temp.forEach((element) {
      widget.questions.removeAt(element);
    });
    saveToLocal();
    resetDeleteQuestion();
    isDeleteMode = false;
    widget.callbackSetState();
  }

  deleteMarkedQuestionList() {
    List temp = [];
    for (int i = widget.bookmark.length - 1; i >= 0; i--) {
      if (listDeleteMarkedQuestion.contains(widget.bookmark[i]['id'])) {
        temp.add(i);
      }
    }
    temp.forEach((element) {
      widget.bookmark.removeAt(element);
    });
    saveToLocal();
    resetDeleteQuestion();
    isDeleteMode = false;
    widget.callbackSetState();
  }

  saveToLocal() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('incorrectQuestion', jsonEncode(widget.questions));
    prefs.setString('bookmarkQuestion', jsonEncode(widget.bookmark));
  }

  resetDeleteQuestion() {
    deleteMarkedQuestion.fillRange(0, sortBookmark.length, false);
    deleteWrongQuestion.fillRange(0, sortQuestion.length, false);
  }

  void _showConfirmDialog() {
    showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => WillPopScope(
        onWillPop: () async => false,
        child: Dialog(
          // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
          insetPadding: const EdgeInsets.symmetric(horizontal: 16),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
              child: Column(
                children: [
                  Container(
                    width: 196,
                    height: 139,
                    decoration: const BoxDecoration(
                        image: DecorationImage(
                      image: AssetImage("assets/images/info_dialog.png"),
                      fit: BoxFit.fill,
                    )),
                  ),
                  Text(
                    AppLocalizations.of(context).confirmDeleteQuestion,
                    style: AppStyles.dialogText,
                    textAlign: TextAlign.center,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 24),
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      child: Row(
                        children: [
                          Expanded(
                              child: Padding(
                            padding: const EdgeInsets.only(right: 6),
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              child: Text(
                                AppLocalizations.of(context).no,
                                style: AppStyles.secondaryButton,
                                textAlign: TextAlign.center,
                              ),
                              style: ElevatedButton.styleFrom(
                                foregroundColor: AppColors.greenPrimary,
                                backgroundColor: Colors.white,
                                shadowColor: Colors.white,
                                elevation: 0,
                                minimumSize: const Size(20, 44),
                                side: BorderSide(
                                    color: AppColors.greenPrimary,
                                    width: 1.0,
                                    style: BorderStyle.solid),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4.0)),
                              ),
                            ),
                          )),
                          Expanded(
                              child: Padding(
                            padding: const EdgeInsets.only(left: 6),
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                setState(() {
                                  if (type == 1) {
                                    deleteWrongQuestionList();
                                  } else {
                                    deleteMarkedQuestionList();
                                  }
                                });
                              },
                              child: Text(
                                AppLocalizations.of(context).yes,
                                style: AppStyles.primaryButton,
                              ),
                              style: ElevatedButton.styleFrom(
                                foregroundColor: AppColors.white,
                                backgroundColor: AppColors.greenPrimary,
                                shadowColor:
                                    const Color.fromARGB(92, 0, 166, 144),
                                elevation: 0,
                                minimumSize: const Size(20, 44),
                                side: BorderSide(
                                    color: AppColors.greenPrimary,
                                    width: 1.0,
                                    style: BorderStyle.solid),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4.0)),
                              ),
                            ),
                          ))
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  _toggleContainer() {
    if (_animation.status != AnimationStatus.completed) {
      _controller.forward();
      setState(() {
        opened = true;
      });
    } else {
      _controller.animateBack(0, duration: const Duration(seconds: 1));
      Future.delayed(const Duration(milliseconds: 1000), () {
        setState(() {
          opened = false;
        });
      });
    }
  }

  void setStateCallback() {
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.fastLinearToSlowEaseIn,
    );
    _controllerTab = TabController(length: 3, vsync: this);

    searchTextController.addListener(() {
      widget.callbackSetState();
    });

    _controllerTab.addListener(() {
      type = Common.premium ? _controllerTab.index + 1 : _controllerTab.index;
      // setState(() {});
      if (Common.premium && type == 3) {
        isVisibleSearchBox = false;
      }
      notifier.value = 0;
      isDeleteMode = false;
      widget.callbackSetState();
      resetDeleteQuestion();
    });
    report = getReport();
  }

  ItemScrollController _scrollControllerItem2 = ItemScrollController();
  ItemScrollController _scrollControllerItem = ItemScrollController();
  @override
  Widget build(BuildContext context) {
    var apbar_height = AppBar().preferredSize.height;
    return Container(
        // ADDED "child:"
        alignment: Alignment.center,
        child: FutureBuilder<List<dynamic>>(
            future: getData(), // async work
            builder: (context, snapshot) {
              if (snapshot.hasError) {
                final data = {
                  'type': 'Runtime Error',
                  'error': snapshot.error.toString()
                };
                ApiServices().sendErrorReport(data);
              }
              if (!snapshot.hasData) {
                return const CircularProgressIndicator();
              }
              return DefaultTabController(
                length: snapshot.data!.length,
                child: Scaffold(
                  appBar: PreferredSize(
                    preferredSize: isVisibleSearchBox
                        ? const Size.fromHeight(140.0)
                        : const Size.fromHeight(kToolbarHeight + 4),
                    child: ValueListenableBuilder<double>(
                      valueListenable: notifier,
                      builder: (context, value, child) {
                        return Container(
                          decoration: BoxDecoration(
                              color: value == 0
                                  ? const Color(0xFFFAFAFA)
                                  : Colors.white,
                              border: value == 1
                                  ? const Border(
                                      top: BorderSide(
                                          width: 1, color: Color(0xFFD5D7DB)),
                                      bottom: BorderSide(
                                          width: 1, color: Color(0xFFD5D7DB)),
                                    )
                                  : null),
                          child: SafeArea(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                isVisibleSearchBox
                                    ? Padding(
                                        padding: const EdgeInsets.only(
                                            bottom: 20, left: 20, right: 20),
                                        child: Row(
                                          children: <Widget>[
                                            Flexible(
                                              child: TextField(
                                                autofocus: shouldFocusTextField,
                                                controller:
                                                    searchTextController,
                                                // autofocus: true,
                                                onChanged: searchCard,
                                                decoration: InputDecoration(
                                                    isDense: true,
                                                    prefixIcon: const Icon(
                                                        Icons.search),
                                                    hintText: "Search",
                                                    hintStyle: AppStyles.body
                                                        .copyWith(fontSize: 14),
                                                    border: OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                        borderSide:
                                                            const BorderSide(
                                                                color: Colors
                                                                    .blue))),
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                if (search == "") {
                                                  isVisibleSearchBox = false;
                                                } else {
                                                  search = "";
                                                  searchTextController.text =
                                                      "";
                                                }
                                                widget.callbackSetState.call();
                                              },
                                              child: const Padding(
                                                padding:
                                                    EdgeInsets.only(left: 10.0),
                                                child:
                                                    Icon(Icons.close_rounded),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    : Container(),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20.0),
                                  child: TabBar(
                                    isScrollable: true,
                                    controller: _controllerTab,
                                    indicator: BoxDecoration(
                                      color: AppColors.scoreCardText,
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.fromBorderSide(
                                        BorderSide(
                                            color: AppColors.scoreCardText),
                                      ),
                                    ),
                                    unselectedLabelColor: AppColors.blackText,
                                    labelColor: AppColors.lightBlueText,
                                    tabs: [
                                      if (!Common.premium)
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 0,
                                            vertical: 5,
                                          ),
                                          child: StreamBuilder<Object>(
                                              stream: countReport,
                                              builder: (context, snapshot1) {
                                                return Text(
                                                  AppLocalizations.of(context)
                                                          .reportedQuestion +
                                                      " (" +
                                                      countReport.value
                                                          .toString() +
                                                      ")",
                                                  style: GoogleFonts.roboto(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w500),
                                                );
                                              }),
                                        ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 0,
                                          vertical: 5,
                                        ),
                                        child: Text(
                                          AppLocalizations.of(context)
                                                  .wrongQuestion +
                                              " (" +
                                              snapshot.data![1].toString() +
                                              ")",
                                          style: GoogleFonts.roboto(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 0,
                                          vertical: 5,
                                        ),
                                        child: Text(
                                          AppLocalizations.of(context)
                                                  .markedQuestion +
                                              " (" +
                                              snapshot.data![0].toString() +
                                              ")",
                                          style: GoogleFonts.roboto(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500),
                                        ),
                                      ),
                                      if (Common.premium)
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 0,
                                            vertical: 5,
                                          ),
                                          child: StreamBuilder<Object>(
                                              stream: countReport,
                                              builder: (context, snapshot) {
                                                return Text(
                                                  AppLocalizations.of(context)
                                                          .reportedQuestion +
                                                      " (" +
                                                      countReport.value
                                                          .toString() +
                                                      ")",
                                                  style: GoogleFonts.roboto(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w500),
                                                );
                                              }),
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  body: TabBarView(
                    controller: _controllerTab,
                    children: <Widget>[
                      if (!Common.premium) _reportQuestion(apbar_height),
                      _wrongQuestion(apbar_height),
                      _bookmark(apbar_height),
                      if (Common.premium) _reportQuestion(apbar_height),
                    ],
                  ),
                ),
              );
            }));
  }

  Widget _reportQuestion(double apbar_height) {
    return FutureBuilder<List<Report>>(
        future: report,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          if (snapshot.hasError) {
            final data = {
              'type': 'Runtime Error',
              'error': snapshot.error.toString()
            };
            ApiServices().sendErrorReport(data);
          }
          if (snapshot.hasData && (snapshot.data?.isEmpty ?? true)) {
            return Container(
              margin: EdgeInsets.only(bottom: apbar_height + 31),
              child: Center(
                  child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset('assets/images/no_item.svg'),
                  Padding(
                    padding: const EdgeInsets.only(top: 20),
                    child: Column(
                      children: [
                        Text(AppLocalizations.of(context).noReport,
                            style: AppStyles.body
                                .copyWith(color: Colors.black, fontSize: 16)),
                      ],
                    ),
                  ),
                ],
              )),
            );
          }
          return NotificationListener<ScrollNotification>(
            onNotification: (n) {
              //print(n.metrics.pixels);
              if (n.metrics.pixels >= 50) {
                notifier.value = 1;
              } else if (n.metrics.pixels == 0) {
                notifier.value = 0;
              }
              return false;
            },
            child: ListView.builder(
                physics: const ClampingScrollPhysics(),
                itemCount: snapshot.data?.length ?? 0,
                itemBuilder: (context, index) {
                  return ReportItem(
                    report: snapshot.data?[index] ?? Report(),
                    callback: setStateCallback,
                  );
                }),
          );
        });
  }

  Widget _wrongQuestion(double apbar_height) {
    if (Common.premium) {
      return LayoutBuilder(builder: (context, constraints) {
        if ((sortQuestion.isEmpty)) {
          return Container(
            margin: EdgeInsets.only(bottom: apbar_height + 31),
            child: Center(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: SvgPicture.asset('assets/images/no_item.svg'),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: Column(
                    children: [
                      Text(AppLocalizations.of(context).noWrongQuestion,
                          style: AppStyles.body
                              .copyWith(color: Colors.black, fontSize: 16)),
                      const SizedBox(
                        height: 10,
                      ),
                      GestureDetector(
                        child: Text(AppLocalizations.of(context).viewExamList,
                            style: AppStyles.bodyBold.copyWith(
                                fontSize: 16, color: AppColors.greenPrimary)),
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => Widget_Qlist2()));
                        },
                      )
                    ],
                  ),
                ),
              ],
            )),
          );
        } else {
          return Column(
            children: [
              Expanded(
                child: NotificationListener<ScrollNotification>(
                  onNotification: (n) {
                    //print(n.metrics.pixels);
                    if (n.metrics.pixels >= 50) {
                      notifier.value = 1;
                    } else if (n.metrics.pixels == 0) {
                      notifier.value = 0;
                    }
                    return false;
                  },
                  child: ScrollablePositionedList.builder(
                    physics: const ClampingScrollPhysics(),
                    key: ObjectKey(type),
                    itemScrollController: _scrollControllerItem,
                    padding:
                        const EdgeInsets.only(left: 10, right: 10, bottom: 20),
                    itemCount: sortQuestion.length,
                    itemBuilder: (ctxt, Index) => _buildItem(ctxt, Index, 1),
                    shrinkWrap: true,
                  ),
                ),
              ),
              if (isDeleteMode) ...{
                Container(
                  //margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF8A8A8A).withOpacity(0.25),
                        spreadRadius: 0,
                        blurRadius: 16,
                        offset: const Offset(4, -4),
                      ),
                    ],
                  ),

                  child: Padding(
                    padding: const EdgeInsets.only(right: 16.0, left: 16),
                    child: Row(
                      children: [
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(
                              top: 12, right: 6, bottom: 25),
                          child: ElevatedButton(
                            onPressed: () {
                              setState(() {
                                isDeleteMode = false;
                                widget.callbackSetState();
                                resetDeleteQuestion();
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.greenPrimary,
                              backgroundColor: Colors.white,
                              shadowColor: Colors.white,
                              elevation: 0,
                              minimumSize: const Size(20, 48),
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle.solid),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                            child: Text(
                              AppLocalizations.of(context).cancel,
                              style: AppStyles.secondaryButton,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        )),
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(
                              top: 12, left: 6, bottom: 25),
                          child: ElevatedButton(
                            onPressed: () {
                              if ((deleteWrongQuestion.contains(true))) {
                                _showConfirmDialog();
                              } else {
                                return;
                              }
                            },
                            child: Text(
                              AppLocalizations.of(context).delete,
                              style: (deleteWrongQuestion.contains(true))
                                  ? AppStyles.primaryButton
                                  : AppStyles.primaryButton
                                      .copyWith(color: const Color(0xFF6A6A6A)),
                            ),
                            style: (deleteWrongQuestion.contains(true))
                                ? ElevatedButton.styleFrom(
                                    foregroundColor: AppColors.greenPrimary,
                                    backgroundColor: AppColors.greenPrimary,
                                    shadowColor: Colors.white,
                                    elevation: 0,
                                    minimumSize: const Size(20, 48),
                                    side: BorderSide(
                                        color: AppColors.greenPrimary,
                                        width: 1.0,
                                        style: BorderStyle.solid),
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(4.0)),
                                  )
                                : ElevatedButton.styleFrom(
                                    foregroundColor: const Color(0xFFE8E8E8),
                                    backgroundColor: const Color(0xFFE8E8E8),
                                    shadowColor: Colors.white,
                                    elevation: 0,
                                    minimumSize: const Size(20, 48),
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(4.0)),
                                  ),
                          ),
                        ))
                      ],
                    ),
                  ),
                )
              },
            ],
          );
        }
      });
    } else {
      return const PaymentPage(
        fromReviewQuestion: true,
        eventLogType: "incorrect",
      );
    }
  }

  Widget _bookmark(double apbar_height) {
    if (Common.premium) {
      return LayoutBuilder(builder: (context, constraints) {
        if ((sortBookmark.isEmpty)) {
          return Container(
            margin: EdgeInsets.only(bottom: apbar_height + 31),
            child: Center(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: SvgPicture.asset('assets/images/no_item.svg'),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: Column(
                    children: [
                      Text(AppLocalizations.of(context).noMarkedQuestion,
                          style: AppStyles.body
                              .copyWith(color: Colors.black, fontSize: 16)),
                      const SizedBox(
                        height: 10,
                      ),
                      GestureDetector(
                        child: Text(AppLocalizations.of(context).viewExamList,
                            style: AppStyles.bodyBold.copyWith(
                                fontSize: 16, color: AppColors.greenPrimary)),
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => Widget_Qlist2()));
                        },
                      )
                    ],
                  ),
                ),
              ],
            )),
          );
        } else {
          return Column(
            children: [
              Expanded(
                child: NotificationListener<ScrollNotification>(
                  onNotification: (n) {
                    //print(n.metrics.pixels);
                    if (n.metrics.pixels >= 50) {
                      notifier.value = 1;
                    } else if (n.metrics.pixels == 0) {
                      notifier.value = 0;
                    }
                    return false;
                  },
                  child: ScrollablePositionedList.builder(
                    physics: const ClampingScrollPhysics(),
                    key: ObjectKey(type),
                    itemScrollController: _scrollControllerItem2,
                    padding: const EdgeInsets.all(10.0),
                    itemCount: sortBookmark.length,
                    itemBuilder: (ctxt, Index) => _buildItem2(ctxt, Index, 2),
                    shrinkWrap: true,
                  ),
                ),
              ),
              if (isDeleteMode) ...{
                Container(
                  //margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.5),
                        spreadRadius: 5,
                        blurRadius: 7,
                        offset:
                            const Offset(0, 3), // changes position of shadow
                      ),
                    ],
                  ),

                  child: Padding(
                    padding: const EdgeInsets.only(right: 16.0, left: 16),
                    child: Row(
                      children: [
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(
                              top: 12, right: 6, bottom: 25),
                          child: ElevatedButton(
                            onPressed: () {
                              setState(() {
                                isDeleteMode = false;
                                widget.callbackSetState();
                                resetDeleteQuestion();
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.greenPrimary,
                              backgroundColor: Colors.white,
                              shadowColor: Colors.white,
                              elevation: 0,
                              minimumSize: const Size(20, 48),
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle.solid),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                            child: Text(
                              AppLocalizations.of(context).cancel,
                              style: AppStyles.secondaryButton,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        )),
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(
                              top: 12, left: 6, bottom: 25),
                          child: ElevatedButton(
                            onPressed: () {
                              if ((deleteMarkedQuestion.contains(true))) {
                                _showConfirmDialog();
                              } else {
                                return;
                              }
                            },
                            child: Text(
                              AppLocalizations.of(context).delete,
                              style: (deleteMarkedQuestion.contains(true))
                                  ? AppStyles.primaryButton
                                  : AppStyles.primaryButton
                                      .copyWith(color: const Color(0xFF6A6A6A)),
                            ),
                            style: (deleteMarkedQuestion.contains(true))
                                ? ElevatedButton.styleFrom(
                                    foregroundColor: AppColors.greenPrimary,
                                    backgroundColor: AppColors.greenPrimary,
                                    shadowColor: Colors.white,
                                    elevation: 0,
                                    minimumSize: const Size(20, 48),
                                    side: BorderSide(
                                        color: AppColors.greenPrimary,
                                        width: 1.0,
                                        style: BorderStyle.solid),
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(4.0)),
                                  )
                                : ElevatedButton.styleFrom(
                                    foregroundColor: const Color(0xFFE8E8E8),
                                    backgroundColor: const Color(0xFFE8E8E8),
                                    shadowColor: Colors.white,
                                    elevation: 0,
                                    minimumSize: const Size(20, 48),
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(4.0)),
                                  ),
                          ),
                        ))
                      ],
                    ),
                  ),
                )
              },
            ],
          );
        }
      });
    } else {
      return const PaymentPage(
        fromReviewQuestion: true,
        eventLogType: "bookmark",
      );
    }
  }

  callback(index, position, typeFilter) {
    if (double.parse(position) > MediaQuery.of(context).size.height - 100) {
      typeFilter == 1
          ? Timer(const Duration(milliseconds: 400), () {
              _scrollControllerItem.scrollTo(
                  index: index,
                  alignment: -0.2,
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeInOut);
            })
          : Timer(const Duration(milliseconds: 400), () {
              _scrollControllerItem2.scrollTo(
                  index: index,
                  alignment: -0.2,
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeInOut);
            });
    }
  }

  Future openDialog(ques, quizid, num) => showDialog(
      context: context,
      builder: (context) {
        return MyDialog(questions: ques, quizId: quizid, num: num);
      });

  String getQuestionText(question) {
    String questionText = question;
    if (RegExp(r'^<p><br />.*').hasMatch(questionText)) {
      questionText = questionText.substring(9, questionText.length - 4);
    } else if (RegExp(r'<p>.*?</p>').hasMatch(questionText)) {
      questionText = questionText.substring(3, questionText.length - 4);
    } else if (RegExp(r'^<br>.*').hasMatch(questionText)) {
      questionText = questionText.substring(4);
    }
    return questionText.replaceAll('../../', Common.apiDomain);
  }

  bool isChecked = false;
  Widget _buildItem(BuildContext context, int index, int filterType) {
    Map question = sortQuestion[index];
    final questionText = getQuestionText(question['question']);
    if (search != "" &&
        !question['question'].toLowerCase().contains(search.toLowerCase())) {
      return Container();
    }
    return Card(
      borderOnForeground: false,
      shadowColor: Colors.white,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      shape: RoundedRectangleBorder(
          side: BorderSide(
              color: deleteWrongQuestion[index]
                  ? AppColors.lightBlueText
                  : AppColors.lightGreyBorder,
              width: 1.0),
          borderRadius: BorderRadius.circular(4.0)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.only(top: 12, left: 12, right: 12),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                GestureDetector(
                  onTap: () {
                    String imagePath = extractImagePathFromHtml(questionText);
                    if (imagePath != "") {
                      showImagePopup(context, imagePath);
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(right: 30.0),
                    child: Html(
                      data: (index + 1).toString() + '. ' + questionText,
                      style: {
                        "body": Style(
                            margin: Margins.zero,
                            padding: HtmlPaddings.zero,
                            fontFamily: 'Roboto',
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                            lineHeight: const LineHeight(1.5)),
                        "img": Style(
                            width: Width(MediaQuery.of(context).size.width - 50,
                                Unit.px))
                      },
                    ),
                  ),
                ),
                Positioned(
                  // will be positioned in the top right of the container
                  top: 0,
                  right: 0,
                  child: isDeleteMode
                      ? SizedBox(
                          width: 25,
                          height: 25,
                          child: Checkbox(
                            value: deleteWrongQuestion[index],
                            onChanged: (bool? value) {
                              setState(() {
                                deleteWrongQuestion[index] = value!;
                                addListWrongDeleteQuestion(question['id']);
                              });
                            },
                          ),
                        )
                      : InkWell(
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            logEvent("review_question_report_click",
                                {"question_id": question['id']});
                            final data = {
                              'debugId': Common.debugId,
                              'qid': question['id']
                            };
                            try {
                              context.loaderOverlay.show();
                              final report = await ApiServices()
                                  .reportByQuestion(data)
                                  .timeout(Duration(seconds: 20));
                              context.loaderOverlay.hide();
                              if (report.reportId != null) {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => ReportDetail(
                                              report: report,
                                            ))).then((value) {});
                              } else {
                                openDialog(
                                    question, question['quizId'], index + 1);
                              }
                            } catch (e) {
                              context.loaderOverlay.hide();
                              showDialog(
                                  context: context,
                                  builder: (builder) => ErrorDialog(
                                        error: e.toString(),
                                      ));
                            }
                          },
                          child: SizedBox(
                            width: 25,
                            height: 25,
                            child: Image.asset('assets/images/report_icon.png',
                                fit: BoxFit.fill),
                          ),
                        ),
                )
              ],
            ),
          ),
          Divider(
            color: AppColors.lightGreyBorder,
            thickness: 1,
            height: 24,
          ),
          // SizedBox(height: 5.0),
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 0, 12, 2),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              for (int i = 0; i < question['options'].length; i++) ...{
                Padding(
                  padding: const EdgeInsets.only(top: 5.0, bottom: 5.0),
                  child: question['options'][i]['q_option']
                          .toString()
                          .contains("<img")
                      ? Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              alphabet[i],
                              style: AppStyles.textWeight700.copyWith(
                                fontSize: 14,
                                color: question['options'][i]['score'] != "0"
                                    ? AppColors.greenText
                                    : AppColors.blackText,
                              ),
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Expanded(
                              child: Html(
                                data: replaceLinkImg(
                                    question['options'][i]['q_option']),
                                style: optionHtmlStyle(
                                    context,
                                    question['options'][i]['score'] != "0"
                                        ? AppColors.greenText
                                        : AppColors.blackText),
                              ),
                            ),
                          ],
                        )
                      : RichText(
                          text: TextSpan(
                            text: alphabet[i],
                            style: AppStyles.textWeight700.copyWith(
                              fontSize: 14,
                              color: question['options'][i]['score'] != "0"
                                  ? AppColors.greenText
                                  : AppColors.blackText,
                            ),
                            children: <TextSpan>[
                              TextSpan(
                                  text: Helper().parseHtmlString(
                                      question['options'][i]['q_option']),
                                  style: AppStyles.textWeight500.copyWith(
                                      fontSize: 14,
                                      color:
                                          question['options'][i]['score'] != "0"
                                              ? AppColors.greenText
                                              : AppColors.blackText)),
                            ],
                          ),
                        ),
                )
              },
              const SizedBox(height: 8.0),
              const MySeparator(color: Colors.grey),
              question['description'] != null && question['description'] != ''
                  ? Description(
                      description: question['description'],
                      questionid: question['id'],
                      wrong: question['count'],
                      callback: callback,
                      deleteMode: isDeleteMode,
                      index: index,
                      type: filterType)
                  : Padding(
                      padding: const EdgeInsets.only(top: 10, bottom: 10),
                      child: Text(
                        AppLocalizations.of(context)
                            .numberWrong
                            .replaceAll("{e}", question['count'].toString()),
                        style: AppStyles.questionText,
                      ),
                    ),
            ]),
          )
        ],
      ),
    );
  }

  Widget _buildItem2(BuildContext context, int index, int filterType) {
    Map bookmark = sortBookmark[index];
    final questionText = getQuestionText(bookmark['question']);
    if (search != "" && !bookmark['question'].toLowerCase().contains(search)) {
      return Container();
    }
    return Card(
      borderOnForeground: false,
      shadowColor: Colors.white,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      shape: RoundedRectangleBorder(
          side: BorderSide(
              color: deleteMarkedQuestion[index]
                  ? AppColors.lightBlueText
                  : AppColors.lightGreyBorder,
              width: 1.0),
          borderRadius: BorderRadius.circular(4.0)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.only(top: 12, left: 12, right: 12),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 30.0),
                  child: Html(
                    data: (index + 1).toString() + '. ' + questionText,
                    style: {
                      "body": Style(
                          fontFamily: 'Roboto',
                          margin: Margins.zero,
                          padding: HtmlPaddings.zero,
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          lineHeight: const LineHeight(1.5)),
                      "img": Style(
                          width: Width(
                              MediaQuery.of(context).size.width - 50, Unit.px))
                    },
                  ),
                ),
                Positioned(
                  // will be positioned in the top right of the container
                  top: 0,
                  right: 0,
                  child: isDeleteMode
                      ? SizedBox(
                          width: 25,
                          height: 25,
                          child: Checkbox(
                            value: deleteMarkedQuestion[index],
                            onChanged: (bool? value) {
                              setState(() {
                                deleteMarkedQuestion[index] = value!;
                                addListMarkedDeleteQuestion(bookmark['id']);
                              });
                            },
                          ),
                        )
                      : InkWell(
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            logEvent("review_question_fliter_click",
                                {"question_id": bookmark['quizId']});
                            final data = {
                              'debugId': Common.debugId,
                              'qid': bookmark['id']
                            };
                            try {
                              context.loaderOverlay.show();
                              final report =
                                  await ApiServices().reportByQuestion(data);
                              context.loaderOverlay.hide();
                              if (report.reportId != null) {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => ReportDetail(
                                              report: report,
                                            ))).then((value) {});
                              } else {
                                openDialog(
                                    bookmark, bookmark['quizId'], index + 1);
                              }
                            } catch (e) {
                              context.loaderOverlay.hide();
                              showDialog(
                                  context: context,
                                  builder: (builder) => ErrorDialog(
                                        error: e.toString(),
                                      ));
                            }
                          },
                          child: SizedBox(
                            width: 25,
                            height: 25,
                            child: Image.asset('assets/images/report_icon.png',
                                fit: BoxFit.fill),
                          ),
                        ),
                )
              ],
            ),
          ),
          Divider(
            color: AppColors.lightGreyBorder,
            thickness: 1,
            height: 24,
          ),
          // SizedBox(height: 5.0),
          Padding(
            padding:
                bookmark['description'] != null && bookmark['description'] != ''
                    ? const EdgeInsets.fromLTRB(12, 0, 12, 2)
                    : const EdgeInsets.fromLTRB(12, 0, 12, 12),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              for (int i = 0; i < bookmark['options'].length; i++) ...{
                Padding(
                  padding: const EdgeInsets.only(top: 5.0, bottom: 5.0),
                  child: RichText(
                    text: TextSpan(
                      text: alphabet[i],
                      style: AppStyles.textWeight700.copyWith(
                        fontSize: 14,
                        color: bookmark['options'][i]['score'] != "0"
                            ? AppColors.greenText
                            : AppColors.blackText,
                      ),
                      children: <TextSpan>[
                        TextSpan(
                            text: Helper().parseHtmlString(
                                bookmark['options'][i]['q_option']),
                            style: AppStyles.textWeight500.copyWith(
                                fontSize: 14,
                                color: bookmark['options'][i]['score'] != "0"
                                    ? AppColors.greenText
                                    : AppColors.blackText)),
                      ],
                    ),
                  ),
                )
              },
              const SizedBox(height: 8.0),
              if (bookmark['description'] != null &&
                  bookmark['description'] != '') ...{
                const MySeparator(color: Colors.grey),
                Description(
                    description: bookmark['description'],
                    questionid: bookmark['id'],
                    wrong: 0,
                    callback: callback,
                    deleteMode: isDeleteMode,
                    index: index,
                    type: filterType)
              } else ...{
                Container(),
              }
            ]),
          )
        ],
      ),
    );
  }

  searchCard(String query) {
    search = query.toLowerCase();
    setState(() {
      shouldFocusTextField = false;
    });
  }
}

class MyDialog extends StatefulWidget {
  const MyDialog({
    Key? key,
    required this.questions,
    required this.quizId,
    required this.num,
  }) : super(key: key);
  final Map questions;
  final String quizId;
  final int num;
  @override
  _MyDialogState createState() => _MyDialogState();
}

class _MyDialogState extends State<MyDialog> {
  int dropdownvalue = 1;
  late TextEditingController controltext;
  List<int> arr = [];
  @override
  void initState() {
    super.initState();
    controltext = TextEditingController();
  }

  @override
  void dispose() {
    super.dispose();
    controltext.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                AppLocalizations.of(context).question + ":",
                style: AppStyles.reportDialogTitle,
              ),
              const SizedBox(
                height: 4,
              ),
              Column(
                children: <Widget>[
                  Container(
                    width: double.infinity,
                    //height: 200.0,
                    color: Colors.white.withOpacity(0.7),
                    child: Text(
                      parse(widget.questions['question']).documentElement!.text,
                      style: AppStyles.reportDialogText,
                    ),
                  )
                ],
              ),
              const SizedBox(
                height: 10,
              ),
              Text(
                AppLocalizations.of(context).report + ":",
                style: AppStyles.reportDialogTitle,
              ),
              const SizedBox(
                height: 4,
              ),
              TextField(
                onTap: () {
                  setState(() {
                    isEmpty = false;
                  });
                },
                decoration: InputDecoration(
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: isEmpty ? Colors.red : const Color(0xFFACB7C5),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                        color: isEmpty ? Colors.red : const Color(0xFFACB7C5),
                        width: 1.0),
                  ),
                ),
                autofocus: false,
                maxLines: 3,
                // expands: true,
                keyboardType: TextInputType.text,
                controller: controltext,
                textAlignVertical: TextAlignVertical.top,
              ),
              isEmpty == true
                  ? Padding(
                      padding: const EdgeInsets.only(top: 10.0),
                      child: Text(
                        AppLocalizations.of(context).emptyField,
                        style: AppStyles.reportDialogText
                            .copyWith(color: Colors.red),
                      ),
                    )
                  : Container(),
              const SizedBox(
                height: 24,
              ),
              Row(
                children: [
                  Expanded(
                      child: Padding(
                    padding: const EdgeInsets.only(right: 6),
                    child: ElevatedButton(
                      onPressed: close,
                      child: Text(
                        AppLocalizations.of(context).cancel,
                        style: AppStyles.secondaryButton,
                        textAlign: TextAlign.center,
                      ),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: AppColors.greenPrimary,
                        backgroundColor: Colors.white,
                        shadowColor: Colors.white,
                        elevation: 0,
                        minimumSize: const Size(20, 44),
                        side: BorderSide(
                            color: AppColors.greenPrimary,
                            width: 1.0,
                            style: BorderStyle.solid),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4.0)),
                      ),
                    ),
                  )),
                  Expanded(
                      child: Padding(
                    padding: const EdgeInsets.only(left: 6),
                    child: ElevatedButton(
                      onPressed: submit,
                      child: Text(
                        AppLocalizations.of(context).send,
                        style: AppStyles.primaryButton,
                      ),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: AppColors.white,
                        backgroundColor: AppColors.greenPrimary,
                        shadowColor: const Color.fromARGB(92, 0, 166, 144),
                        elevation: 0,
                        minimumSize: const Size(20, 44),
                        side: BorderSide(
                            color: AppColors.greenPrimary,
                            width: 1.0,
                            style: BorderStyle.solid),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4.0)),
                      ),
                    ),
                  ))
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> sendReport() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    String url = apiUrl + "send_report";
    String parsedstring = '';
    var doc = parse(widget.questions['question']);
    if (doc.documentElement != null) {
      parsedstring = doc.documentElement!.text;
    }
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "-Quiz List Request";
      Map data = {
        'appid': Common.appid,
        'qid': widget.questions['id'],
        'quid': widget.quizId,
        'email': '',
        'message': controltext.text,
        'questionReport': parsedstring,
      };
      try {
        final response = await dio.post(url,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data),
              "debugId": Common.debugId
            }));
        if (response.statusCode == 200) {
          inspect(response.data);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        // if (loadRemoteDatatSucceed == false) retryFuture(sendReport, 200);
      }
    }
  }

  bool isEmpty = false;
  void submit() {
    if (controltext.text.isEmpty) {
      setState(() {
        isEmpty = true;
      });
    } else {
      Navigator.of(context).pop(controltext);
      sendReport();
      ApiServices().listReported();
      success();
    }
  }

  void close() {
    Navigator.of(context).pop();
  }

  Future success() => showDialog(
      context: context,
      builder: (context) =>
          SuccessDialog(text: AppLocalizations.of(context).reportSuccess));
  Future error() => showDialog(
      context: context,
      builder: (context) => AlertDialog(
            content: Text(AppLocalizations.of(context).noInfo),
          ));
  DropdownMenuItem<int> buildMenuItem(int e) =>
      DropdownMenuItem(value: e, child: Text(e.toString()));
}

enum ReportStatus { reviewing, answered, closed }

class ReportItem extends StatelessWidget {
  final Report report;
  final Function()? callback;

  const ReportItem({Key? key, required this.report, this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          )
        ],
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => ReportDetail(
                        report: report,
                      ))).then((value) {
            callback?.call();
          });
        },
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                    child: Text(report.reportQuestion ?? '',
                        style: AppStyles.bodyBold.copyWith(
                            color: AppColors.blackText,
                            fontSize: 14,
                            height: 1.57))),
                Icon(
                  Icons.chevron_right,
                  color: AppColors.grey,
                  size: 16,
                )
              ],
            ),
            const Divider(
              height: 24,
              color: Color(0xFFD9E2E8),
              thickness: 1,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(AppLocalizations.of(context).status,
                    style: AppStyles.body.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        height: 1.57)),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: report.reportStatus == ReportStatus.reviewing
                        ? const Color(0xFFE97E01)
                        : report.reportStatus == ReportStatus.answered
                            ? const Color(0xFF0083CC)
                            : const Color(0xFF8A8A8A),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: Text(
                      report.reportStatus == ReportStatus.reviewing
                          ? AppLocalizations.of(context).reviewing
                          : report.reportStatus == ReportStatus.answered
                              ? AppLocalizations.of(context).answered
                              : AppLocalizations.of(context).closed,
                      style: AppStyles.bodyWhite.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          height: 1.5)),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
