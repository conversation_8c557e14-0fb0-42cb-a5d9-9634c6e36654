// ignore_for_file: prefer_const_constructors, sized_box_for_whitespace, unnecessary_new, prefer_const_declarations, unused_local_variable
import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_text_styles.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';

import 'package:psm_app/models/qlist_model.dart';
import 'package:http/http.dart' as http;
import 'package:psm_app/models/result_model.dart';
import 'package:psm_app/view/exam/exam.dart';
import 'package:psm_app/view/exam_detail.dart';
import 'package:psm_app/view/home.dart';
import 'package:psm_app/view/home/<USER>/chart/chart_widget.dart';
import 'package:psm_app/view/payment/payment.dart';
import 'package:psm_app/view/widgets/error_dialog.dart';
import 'package:psm_app/view/widgets/popup.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../data_sources/api_servies.dart';
import '../widgets/showcase_tooltip.dart';

class Widget_Qlist2 extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

_goBack(BuildContext context) {
  logPage("Main");
  Navigator.pop(context);
}

class _MyAppState extends State<Widget_Qlist2> {
  late Future<List<Qlist>> futurePost;
  late bool status;
  List persons = [];
  final ValueNotifier<bool> isLoading = ValueNotifier(false);
  bool ignorePointer = true;
  String resumeQuiz = '';
  Map numAnswered = {};
  Map timeDoQuiz = {};
  bool resumeFlag = false;
  bool internet = false;
  bool haveLocal = false;
  String url = getUserInfor().url;
  bool firstLoadSuccess = false;

  Future<List<Qlist>> fetchPost({forceReload = false}) async {
    isLoading.value = true;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final String resultString = preferences.getString('result') ?? "";
    final List<Result> musics;

    resumeQuiz = preferences.getString('resumeQuiz') ?? '';
    if (resultString != "") {
      musics = Result.decode(resultString);
      inspect(musics);
    } else {
      musics = [];
    }

    await getListQuiz(forceReload);

    String localQuestionList = await preferences.getString('quizList') ?? '';
    if (localQuestionList != '') {
      haveLocal = true;
      final parsed =
          json.decode(localQuestionList).cast<Map<String, dynamic>>();
      List<Qlist> qlist =
          parsed.map<Qlist>((json) => Qlist.fromMap(json)).toList();
      for (int i = 0; i < qlist.length; i++) {
        //Future<double> test = getPercentage(qlist[i].quiz_name);
        if (preferences.getInt('timeDoQuiz_' + qlist[i].quid) != null) {
          timeDoQuiz[qlist[i].quid] =
              preferences.getInt('timeDoQuiz_' + qlist[i].quid);
        }
        String answered =
            preferences.getString('answeredText_' + qlist[i].quid) ?? '';
        if (answered == '') {
          numAnswered[qlist[i].quid] = '0';
        } else {
          int lengthAnswered = 0;
          List answeredList = jsonDecode(answered);
          for (int j = 0; j < answeredList.length; j++) {
            if (answeredList[j].isNotEmpty) {
              lengthAnswered++;
            }
          }
          numAnswered[qlist[i].quid] = lengthAnswered.toString();
        }
        double save = 0;
        for (var j = 0; j < musics.length; j++) {
          if (musics[j].name == qlist[i].quiz_name) {
            if (double.parse(musics[j].percentage) > save) {
              save = double.parse(musics[j].percentage);
            }
          }
        }
        qlist[i].highscore = save;
      }
      isLoading.value = false;
      firstLoadSuccess = true;
      return qlist;
    } else {
      isLoading.value = false;
      throw Exception('Failed to load list');
    }
  }

  Future<void> Request_key() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "-Accquire token";
      /* if (token == null || token == '') { */
      var jwt = await attempPost(info);
      preferences.setString('token', jwt);
    }
  }

  Future<void> getListQuiz(bool force) async {
    isLoading.value = true;
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) {
      var today = DateFormat('dd/MM/yyyy').format(DateTime.now());
      var newQuizListTime = DateFormat('dd/MM/yyyy')
          .format(DateTime.now().add(Duration(days: 7)));
      var quizListTimer = preferences.getString('quizListTimer');

      DateTime todayDate = new DateFormat("dd/MM/yyyy").parse(today);
      DateTime quizListDated =
          new DateFormat("dd/MM/yyyy").parse(newQuizListTime);
      try {
        var quizlistLocal = preferences.getString('quizList');
        if (quizlistLocal == null || force == true) {
          await ApiServices().getListQuiz();
        } else {
          if (quizListTimer != null) {
            quizListDated = DateFormat("dd/MM/yyyy").parse(quizListTimer);
          }
          if (todayDate.isAfter(quizListDated)) {
            await ApiServices().getListQuiz();
          }
        }
      } catch (e) {
        rethrow;
      }
    }
  }

  Future<double> getPercentage(String quizname) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final String resultString = preferences.getString('result') ?? "";
    final List<Result> musics = Result.decode(resultString);
    double save = 0;
    for (var i = 0; i < musics.length; i++) {
      if (musics[i].name == quizname) {
        if (double.parse(musics[i].percentage) > save) {
          save = double.parse(musics[i].percentage);
        }
      }
    }
    return save;
  }

/* void main() => runApp(MyApp()); */
  List<Qlist> postFromJson(String str) =>
      List<Qlist>.from(json.decode(str).map((x) => Qlist.fromMap(x)));
  List fakequiz_psm = [
    QuizCard(
        noq: 10,
        duration: 10,
        name: "PSM-I Practice 10 Questions",
        type: "Random"),
    QuizCard(
        noq: 20,
        duration: 20,
        name: "PSM-I Practice 20 Questions",
        type: "Random"),
    QuizCard(
        noq: 40,
        duration: 30,
        name: "PSM-I Practice 40 Questions",
        type: "Random"),
    QuizCard(
        noq: 80,
        duration: 40,
        name: "PSM-I Practice 80 Questions",
        type: "Random"),
    QuizCard(noq: 30, duration: 90, name: "PSM-I Exam 1", type: "Question"),
  ];
  List fakequiz_pspo = [
    QuizCard(
        noq: 10,
        duration: 10,
        name: "PSPO-I Practice 10 Questions",
        type: "Random"),
    QuizCard(
        noq: 20,
        duration: 20,
        name: "PSPO-I Practice 20 Questions",
        type: "Random"),
    QuizCard(
        noq: 40,
        duration: 30,
        name: "PSPO-I Practice 40 Questions",
        type: "Random"),
    QuizCard(
        noq: 80,
        duration: 40,
        name: "PSPO-I Practice 80 Questions",
        type: "Random"),
    QuizCard(noq: 77, duration: 60, name: "	PSPO-I Exam 1", type: "Question"),
  ];
  void _checkInternet() async {
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) internet = true;
  }

  void _showConfirmDialog(user) {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => Dialog(
        // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
        insetPadding: EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
            child: Column(
              children: [
                Container(
                  width: 196,
                  height: 139,
                  decoration: BoxDecoration(
                      image: DecorationImage(
                    image: AssetImage("assets/images/info_dialog.png"),
                    fit: BoxFit.fill,
                  )),
                ),
                Text(
                  AppLocalizations.of(context).resumeQuizDialog,
                  style: AppStyles.dialogText,
                  textAlign: TextAlign.center,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.7,
                    child: Row(
                      children: [
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(right: 6),
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: Text(
                              AppLocalizations.of(context).no,
                              style: AppStyles.secondaryButton,
                              textAlign: TextAlign.center,
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.greenPrimary,
                              backgroundColor: Colors
                                  .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                              shadowColor: Colors
                                  .white, //specify the button's elevation color
                              elevation: 0, //buttons Material shadow
                              // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                              minimumSize: Size(20,
                                  44), //specify the button's first: width and second: height
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle
                                      .solid), //set border for the button
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          ),
                        )),
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(left: 6),
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          ExamDetail(user: user)));
                            },
                            child: Text(
                              "OK",
                              style: AppStyles.primaryButton,
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.white,
                              backgroundColor: AppColors.greenPrimary,
                              shadowColor: Color.fromARGB(92, 0, 166, 144),
                              elevation: 0,
                              minimumSize: Size(20, 44),
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle.solid),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          ),
                        ))
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showResumeConfirmDialog(user) {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => Dialog(
        // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
        insetPadding: EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
            child: Column(
              children: [
                Container(
                  width: 196,
                  height: 139,
                  decoration: BoxDecoration(
                      image: DecorationImage(
                    image: AssetImage("assets/images/info_dialog.png"),
                    fit: BoxFit.fill,
                  )),
                ),
                Text(
                  AppLocalizations.of(context).confirmDoQuiz,
                  style: AppStyles.dialogText,
                  textAlign: TextAlign.center,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                      children: [
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(right: 6),
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: Text(
                              AppLocalizations.of(context).no,
                              style: AppStyles.secondaryButton,
                              textAlign: TextAlign.center,
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.greenPrimary,
                              backgroundColor: Colors
                                  .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                              shadowColor: Colors
                                  .white, //specify the button's elevation color
                              elevation: 0, //buttons Material shadow
                              // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                              minimumSize: Size(20,
                                  44), //specify the button's first: width and second: height
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle
                                      .solid), //set border for the button
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          ),
                        )),
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(left: 6),
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              Navigator.pushReplacement(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => Exam(
                                          resume: true,
                                          idQuiz: user.quid,
                                          exam_quiz: user.exam_quiz,
                                          questionSelection:
                                              user.question_selection,
                                          quizName: user.quiz_name,
                                          passPercent:
                                              int.parse(user.pass_percentage),
                                          duration: int.parse(user.duration))));
                            },
                            child: Text(
                              "OK",
                              style: AppStyles.primaryButton,
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.white,
                              backgroundColor: AppColors.greenPrimary,
                              shadowColor: Color.fromARGB(92, 0, 166, 144),
                              elevation: 0,
                              minimumSize: Size(20, 44),
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle.solid),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          ),
                        ))
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _remainTime(duration, timeDoQuiz) {
    if (duration == '0') {
      return AppLocalizations.of(context).noLimit;
    } else {
      final remainMinute = (int.parse(duration) - (timeDoQuiz / 60));
      final remainSecond = (int.parse(duration) * 60 - timeDoQuiz);
      final remainInSecond = remainSecond - (remainMinute.floor() * 60);
      if (remainMinute < 1) {
        return remainSecond.toString() +
            " " +
            AppLocalizations.of(context).secondShort +
            " " +
            AppLocalizations.of(context).left;
      } else {
        if (remainInSecond > 0) {
          return remainMinute.floor().toString() +
              " " +
              AppLocalizations.of(context).minutesShort +
              " $remainInSecond ${AppLocalizations.of(context).secondShort} " +
              AppLocalizations.of(context).left;
        } else {
          return remainMinute.round().toString() +
              " " +
              AppLocalizations.of(context).minutesShort +
              " " +
              AppLocalizations.of(context).left;
        }
      }
    }
  }

  late String appName = 'PSM Exam Simulator';
  Future _initPackageInfo() async {
    final PackageInfo info = await PackageInfo.fromPlatform();
    appName = info.appName;
    setState(() {
      if (appName.contains("PSPO") && appName != '') {
        persons = fakequiz_pspo;
      } else {
        persons = fakequiz_psm;
      }
    });
  }

  @override
  void initState() {
    resumeQuiz = '';
    _checkInternet();
    futurePost = fetchPost();
    _initPackageInfo();
    super.initState();
    logPage("Exam List");
    showShowcaseQuizList();
    //Future<String?> token = attempPost();
    //getPercentage();
  }

  String TimeHeader = "Thời lượng \n"
      "(Tính theo phút)";
  final ButtonStyle raisedButtonStyle = ElevatedButton.styleFrom(
    foregroundColor: Colors.black87,
    backgroundColor: Colors.lightBlueAccent,
    minimumSize: Size(88, 36),
    padding: EdgeInsets.symmetric(horizontal: 16),
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(5)),
    ),
  );
  final GlobalKey _showcase1 = GlobalKey();
  final GlobalKey _showcase2 = GlobalKey();
  BuildContext? myContext;

  void showShowcaseQuizList() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final show = preferences.getString('showShowcaseQuizList');
    if (show?.isEmpty ?? true) {
      preferences.setString('showShowcaseQuizList', '0');
      ShowCaseWidget.of(myContext!).startShowCase([_showcase2, _showcase1]);
      Future.delayed(Duration(seconds: 2), () {
        setState(() {
          ignorePointer = false;
        });
      });
    } else {
      setState(() {
        ignorePointer = false;
      });
    }
  }

  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  void _onRefreshList() async {
    try {
      futurePost = fetchPost(forceReload: true);
      await Future.wait([futurePost]);
      _refreshController.refreshCompleted();
      setState(() {});
    } catch (e) {
      _refreshController.refreshFailed();
    }
  }

  @override
  Widget build(BuildContext context) {
    for (var item in persons) {
      if (item.type == "Random") {
        item.type = AppLocalizations.of(context).randomSet;
      } else {
        item.type = AppLocalizations.of(context).questionSet;
      }
    }

    var apbar_height = AppBar().preferredSize.height;
    return IgnorePointer(
      ignoring: ignorePointer,
      child: Scaffold(
          appBar: AppBar(
            centerTitle: true,
            leading: IconButton(
              icon: Icon(Icons.arrow_back_ios_new),
              iconSize: 20.0,
              color: AppColors.blackText,
              onPressed: () {
                _goBack(context);
              },
            ),
            title: Text(
              AppLocalizations.of(context).quizList,
              style: AppStyles.appBarTitle,
            ),
            backgroundColor: Colors.white,
            elevation: 0,
          ),
          body: ShowCaseWidget(
              disableBarrierInteraction: true,
              disableMovingAnimation: true,
              enableAutoScroll: true,
              builder: (context) {
                return Container(
                  color: Colors.white,
                  child: Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      Container(
                        height: MediaQuery.of(context).size.height,
                        decoration: BoxDecoration(color: Colors.transparent),
                        child: FutureBuilder<List<Qlist>>(
                          future: futurePost,
                          builder:
                              (BuildContext context, AsyncSnapshot snapshot) {
                            if (snapshot.hasData) {
                              if (snapshot.data?.isEmpty ?? true) {
                                return Container(
                                  margin: EdgeInsets.only(bottom: apbar_height),
                                  child: Center(
                                      child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                          'assets/images/no_item.svg'),
                                      Padding(
                                        padding: const EdgeInsets.only(top: 20),
                                        child: Column(
                                          children: [
                                            Text(
                                                AppLocalizations.of(context)
                                                    .noQuizAvaliable,
                                                style: AppStyles.body.copyWith(
                                                    color: Colors.black,
                                                    fontSize: 16)),
                                            SizedBox(
                                              height: 10,
                                            ),
                                            /* GestureDetector(
                                        child: Text(
                                            AppLocalizations.of(context).retryInternet,
                                            style: AppStyles.bodyBold.copyWith(
                                                fontSize: 16, color: Colors.blue)),
                                        onTap: () {
                                          Navigator.pushReplacement(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (BuildContext context) =>
                                                      super.widget));
                                        },
                                      ) */
                                          ],
                                        ),
                                      ),
                                    ],
                                  )),
                                );
                              } else {
                                bool canShowShowcase1 = true;
                                bool canShowShowcase2 = true;
                                return SmartRefresher(
                                  controller: _refreshController,
                                  onRefresh: _onRefreshList,
                                  header: MaterialClassicHeader(),
                                  child: SingleChildScrollView(
                                    child: Column(
                                      children: [
                                        ListView.builder(
                                          itemCount: snapshot.data.length,
                                          shrinkWrap: true,
                                          padding:
                                              const EdgeInsets.only(top: 5.0),
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemBuilder: (BuildContext context,
                                              int index) {
                                            if ((canShowShowcase1 &&
                                                    snapshot.data[index]
                                                            .question_selection ==
                                                        "0") ||
                                                (canShowShowcase2 &&
                                                    snapshot.data[index]
                                                            .question_selection !=
                                                        "0")) {
                                              if (snapshot.data[index]
                                                      .question_selection ==
                                                  "0") {
                                                canShowShowcase1 = false;
                                              } else {
                                                canShowShowcase2 = false;
                                              }

                                              return Container(
                                                //height: 100,
                                                margin: const EdgeInsets.only(
                                                    left: 16.0,
                                                    right: 16.0,
                                                    bottom: 16),
                                                decoration: BoxDecoration(
                                                    boxShadow: const [
                                                      BoxShadow(
                                                        color: Color.fromRGBO(
                                                            0, 0, 0, 0.16),
                                                        spreadRadius: 0,
                                                        blurRadius: 10,
                                                        offset: Offset(4,
                                                            4), // changes position of shadow
                                                      ),
                                                    ],
                                                    color: AppColors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8)),
                                                child: Showcase.withWidget(
                                                  width: 300,
                                                  height: 320,
                                                  targetBorderRadius:
                                                      BorderRadius.circular(8),
                                                  container: Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            top: 16),
                                                    child: Padding(
                                                      padding: EdgeInsets.only(
                                                          left: (MediaQuery.of(
                                                                          context)
                                                                      .size
                                                                      .width -
                                                                  332) /
                                                              2),
                                                      child: ShowcaseTooltip(
                                                        showBack: !canShowShowcase1 &&
                                                                snapshot
                                                                        .data[
                                                                            index]
                                                                        .question_selection ==
                                                                    "0"
                                                            ? true
                                                            : false,
                                                        posittionTop: -10,
                                                        posittionLeft: 150,
                                                        showcaseContext:
                                                            myContext!,
                                                        lastItem: !canShowShowcase1 &&
                                                                snapshot
                                                                        .data[
                                                                            index]
                                                                        .question_selection ==
                                                                    "0"
                                                            ? true
                                                            : false,
                                                        text: !canShowShowcase1 &&
                                                                snapshot
                                                                        .data[
                                                                            index]
                                                                        .question_selection ==
                                                                    "0"
                                                            ? AppLocalizations
                                                                    .of(context)
                                                                .listQuizShowcase2
                                                            : AppLocalizations
                                                                    .of(context)
                                                                .listQuizShowcase1,
                                                      ),
                                                    ),
                                                  ),
                                                  key: !canShowShowcase1 &&
                                                          snapshot.data[index]
                                                                  .question_selection ==
                                                              "0"
                                                      ? _showcase1
                                                      : _showcase2,
                                                  child: InkWell(
                                                      onTap: () {
                                                        logEvent(
                                                            "exam_list_item_click",
                                                            {
                                                              "test_name":
                                                                  snapshot
                                                                      .data[
                                                                          index]
                                                                      .quiz_name
                                                            });
                                                        if (resumeQuiz != '') {
                                                          if (resumeQuiz ==
                                                              snapshot
                                                                  .data[index]
                                                                  .quid) {
                                                            _showResumeConfirmDialog(
                                                                snapshot.data[
                                                                    index]);
                                                          } else {
                                                            _showConfirmDialog(
                                                                snapshot.data[
                                                                    index]);
                                                          }
                                                        } else {
                                                          Navigator.push(
                                                              context,
                                                              MaterialPageRoute(
                                                                  builder: (context) =>
                                                                      ExamDetail(
                                                                          user:
                                                                              snapshot.data[index])));
                                                        }
                                                      },
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(12.0),
                                                        child: Column(
                                                          children: [
                                                            Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceBetween,
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .center,
                                                              children: [
                                                                Row(
                                                                  children: [
                                                                    snapshot.data[index].question_selection ==
                                                                            "0"
                                                                        ? SvgPicture.asset(
                                                                            'assets/images/set.svg')
                                                                        : SvgPicture.asset(
                                                                            'assets/images/shuffle.svg'),
                                                                    SizedBox(
                                                                      width: 8,
                                                                    ),
                                                                    snapshot.data[index].question_selection ==
                                                                            "0"
                                                                        ? Text(
                                                                            AppLocalizations.of(context).questionSet.toUpperCase(),
                                                                            style: AppStyles.body.copyWith(
                                                                                color: Color(0xff00326C),
                                                                                fontWeight: FontWeight.w600,
                                                                                fontSize: 12),
                                                                          )
                                                                        : Text(
                                                                            AppLocalizations.of(context).randomSet.toUpperCase(),
                                                                            style: AppStyles.body.copyWith(
                                                                                color: Color(0xff00326C),
                                                                                fontWeight: FontWeight.w600,
                                                                                fontSize: 12),
                                                                          ),
                                                                  ],
                                                                ),
                                                                Row(
                                                                  /* crossAxisAlignment:
                                                              CrossAxisAlignment.center, */
                                                                  children: [
                                                                    resumeQuiz ==
                                                                            snapshot.data[index].quid
                                                                        ? Container(
                                                                            decoration:
                                                                                BoxDecoration(color: Color(0xFFFFFAEE), borderRadius: BorderRadius.circular(4)),
                                                                            child:
                                                                                Padding(
                                                                              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                                                                              child: Text(
                                                                                AppLocalizations.of(context).doing,
                                                                                style: AppStyles.body.copyWith(color: Color(0xFFCB7A00), fontWeight: FontWeight.w500, fontSize: 12, height: 1.5),
                                                                              ),
                                                                            ),
                                                                          )
                                                                        : Container(),
                                                                    SizedBox(
                                                                      width: 8,
                                                                    ),
                                                                    Container(
                                                                      decoration: BoxDecoration(
                                                                          color: Common.premium
                                                                              ? Color(0xFFEDFBFF)
                                                                              : Color(0xFFD8E3FF),
                                                                          borderRadius: BorderRadius.circular(4)),
                                                                      child:
                                                                          Padding(
                                                                        padding: const EdgeInsets
                                                                            .symmetric(
                                                                            vertical:
                                                                                4,
                                                                            horizontal:
                                                                                6),
                                                                        child: !Common.premium
                                                                            ? Text(
                                                                                AppLocalizations.of(context).free,
                                                                                style: AppStyles.body.copyWith(color: Color(0xff2D4686), fontWeight: FontWeight.w500, fontSize: 12, height: 1.5),
                                                                              )
                                                                            : Text(
                                                                                AppLocalizations.of(context).premium,
                                                                                style: AppStyles.body.copyWith(color: Color(0xff116C78), fontWeight: FontWeight.w500, fontSize: 12),
                                                                              ),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                )
                                                              ],
                                                            ),
                                                            Divider(
                                                                height: 16,
                                                                thickness: 1,
                                                                color: Color(
                                                                    0xFFD9E2E8)),
                                                            Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceBetween,
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .center,
                                                              children: [
                                                                Expanded(
                                                                  child: Column(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .start,
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      Padding(
                                                                        padding: const EdgeInsets
                                                                            .only(
                                                                            bottom:
                                                                                10.0),
                                                                        child:
                                                                            Text(
                                                                          snapshot
                                                                              .data[index]
                                                                              .quiz_name,
                                                                          style: AppStyles.body.copyWith(
                                                                              color: Color(0xff1F1F1F),
                                                                              fontWeight: FontWeight.w700,
                                                                              fontSize: 14,
                                                                              height: 1.57),
                                                                        ),
                                                                      ),
                                                                      Container(
                                                                        width: MediaQuery.of(context).size.width *
                                                                            0.65,
                                                                        child:
                                                                            Wrap(
                                                                          alignment:
                                                                              WrapAlignment.start,
                                                                          direction:
                                                                              Axis.horizontal,
                                                                          children: [
                                                                            if (resumeQuiz ==
                                                                                snapshot.data[index].quid) ...{
                                                                              Padding(
                                                                                padding: const EdgeInsets.only(
                                                                                  right: 5,
                                                                                ),
                                                                                child: Icon(Icons.sticky_note_2_outlined, color: AppColors.lightGreyText, size: 15),
                                                                              ),
                                                                              Padding(
                                                                                padding: const EdgeInsets.only(right: 5),
                                                                                child: Text(snapshot.data[index].noq + " " + AppLocalizations.of(context).numberQuestion + " (" + (int.parse(snapshot.data[index].noq) - int.parse(numAnswered[snapshot.data[index].quid])).toString() + " " + AppLocalizations.of(context).left + ")", style: AppStyles.listQuizInfo),
                                                                              ),
                                                                              if (snapshot.data[index].duration == '0') ...{
                                                                                Padding(
                                                                                  padding: const EdgeInsets.only(right: 5),
                                                                                  child: Icon(
                                                                                    Icons.access_time,
                                                                                    color: AppColors.lightGreyText,
                                                                                    size: 15,
                                                                                  ),
                                                                                ),
                                                                                Text(_remainTime(snapshot.data[index].duration, timeDoQuiz[snapshot.data[index].quid]), style: AppStyles.listQuizInfo),
                                                                              } else ...{
                                                                                Padding(
                                                                                  padding: const EdgeInsets.only(top: 5, right: 5),
                                                                                  child: Icon(
                                                                                    Icons.circle_rounded,
                                                                                    size: 5,
                                                                                    color: Color(0xff7A8694),
                                                                                  ),
                                                                                ),
                                                                                Wrap(children: [
                                                                                  Padding(
                                                                                    padding: const EdgeInsets.only(right: 5),
                                                                                    child: Icon(
                                                                                      Icons.access_time,
                                                                                      color: AppColors.lightGreyText,
                                                                                      size: 15,
                                                                                    ),
                                                                                  ),
                                                                                  Text(_remainTime(snapshot.data[index].duration, timeDoQuiz[snapshot.data[index].quid]), style: AppStyles.listQuizInfo)
                                                                                ])
                                                                              },
                                                                            } else ...{
                                                                              Padding(
                                                                                padding: const EdgeInsets.only(
                                                                                  right: 5,
                                                                                ),
                                                                                child: Icon(Icons.sticky_note_2_outlined, color: AppColors.lightGreyText, size: 15),
                                                                              ),
                                                                              Padding(
                                                                                padding: const EdgeInsets.only(right: 5),
                                                                                child: Text(snapshot.data[index].noq + " " + AppLocalizations.of(context).numberQuestion, style: AppStyles.listQuizInfo),
                                                                              ),
                                                                              if (snapshot.data[index].duration == '0') ...{
                                                                                Padding(
                                                                                  padding: const EdgeInsets.only(top: 5, right: 5),
                                                                                  child: Icon(
                                                                                    Icons.circle_rounded,
                                                                                    size: 5,
                                                                                    color: Color(0xff7A8694),
                                                                                  ),
                                                                                ),
                                                                                Wrap(children: [
                                                                                  Padding(
                                                                                      padding: const EdgeInsets.only(right: 5),
                                                                                      child: Icon(
                                                                                        Icons.access_time,
                                                                                        color: AppColors.lightGreyText,
                                                                                        size: 15,
                                                                                      )),
                                                                                  Text(AppLocalizations.of(context).noLimit, style: AppStyles.listQuizInfo)
                                                                                ])
                                                                              } else ...{
                                                                                Padding(
                                                                                  padding: const EdgeInsets.only(top: 5, right: 5),
                                                                                  child: Icon(
                                                                                    Icons.circle_rounded,
                                                                                    size: 5,
                                                                                    color: Color(0xff7A8694),
                                                                                  ),
                                                                                ),
                                                                                Wrap(children: [
                                                                                  Padding(
                                                                                      padding: const EdgeInsets.only(right: 5),
                                                                                      child: Icon(
                                                                                        Icons.access_time,
                                                                                        color: AppColors.lightGreyText,
                                                                                        size: 15,
                                                                                      )),
                                                                                  Padding(
                                                                                    padding: const EdgeInsets.only(top: 0.5),
                                                                                    child: Text(snapshot.data[index].duration + " " + AppLocalizations.of(context).minutesShort, style: AppStyles.listQuizInfo),
                                                                                  )
                                                                                ])
                                                                              },
                                                                            },
                                                                          ],
                                                                        ),
                                                                      )
                                                                    ],
                                                                  ),
                                                                ),
                                                                ChartWidget(
                                                                  percent: snapshot
                                                                          .data[
                                                                              index]
                                                                          .highscore /
                                                                      100,
                                                                ),
                                                              ],
                                                            )
                                                          ],
                                                        ),
                                                      )),
                                                ),
                                              );
                                            } else {
                                              return Container(
                                                //height: 100,
                                                margin: const EdgeInsets.only(
                                                    left: 16.0,
                                                    right: 16.0,
                                                    bottom: 16),
                                                decoration: BoxDecoration(
                                                    boxShadow: const [
                                                      BoxShadow(
                                                        color: Color.fromRGBO(
                                                            0, 0, 0, 0.16),
                                                        spreadRadius: 0,
                                                        blurRadius: 10,
                                                        offset: Offset(4,
                                                            4), // changes position of shadow
                                                      ),
                                                    ],
                                                    color: AppColors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8)),
                                                child: InkWell(
                                                    onTap: () {
                                                      logEvent(
                                                          "exam_list_item_click",
                                                          {
                                                            "test_name":
                                                                snapshot
                                                                    .data[index]
                                                                    .quiz_name
                                                          });
                                                      if (resumeQuiz != '') {
                                                        if (resumeQuiz ==
                                                            snapshot.data[index]
                                                                .quid) {
                                                          _showResumeConfirmDialog(
                                                              snapshot
                                                                  .data[index]);
                                                        } else {
                                                          _showConfirmDialog(
                                                              snapshot
                                                                  .data[index]);
                                                        }
                                                      } else {
                                                        Navigator.push(
                                                            context,
                                                            MaterialPageRoute(
                                                                builder: (context) =>
                                                                    ExamDetail(
                                                                        user: snapshot
                                                                            .data[index])));
                                                      }
                                                    },
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              12.0),
                                                      child: Column(
                                                        children: [
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceBetween,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .center,
                                                            children: [
                                                              Row(
                                                                children: [
                                                                  snapshot.data[index].question_selection ==
                                                                          "0"
                                                                      ? SvgPicture
                                                                          .asset(
                                                                              'assets/images/set.svg')
                                                                      : SvgPicture
                                                                          .asset(
                                                                              'assets/images/shuffle.svg'),
                                                                  SizedBox(
                                                                    width: 8,
                                                                  ),
                                                                  snapshot.data[index]
                                                                              .question_selection ==
                                                                          "0"
                                                                      ? Text(
                                                                          AppLocalizations.of(context)
                                                                              .questionSet
                                                                              .toUpperCase(),
                                                                          style: AppStyles.body.copyWith(
                                                                              color: Color(0xff00326C),
                                                                              fontWeight: FontWeight.w600,
                                                                              fontSize: 12),
                                                                        )
                                                                      : Text(
                                                                          AppLocalizations.of(context)
                                                                              .randomSet
                                                                              .toUpperCase(),
                                                                          style: AppStyles.body.copyWith(
                                                                              color: Color(0xff00326C),
                                                                              fontWeight: FontWeight.w600,
                                                                              fontSize: 12),
                                                                        ),
                                                                ],
                                                              ),
                                                              Row(
                                                                /* crossAxisAlignment:
                                                            CrossAxisAlignment.center, */
                                                                children: [
                                                                  resumeQuiz ==
                                                                          snapshot
                                                                              .data[index]
                                                                              .quid
                                                                      ? Container(
                                                                          decoration: BoxDecoration(
                                                                              color: Color(0xFFFFFAEE),
                                                                              borderRadius: BorderRadius.circular(4)),
                                                                          child:
                                                                              Padding(
                                                                            padding:
                                                                                const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                                                                            child:
                                                                                Text(
                                                                              AppLocalizations.of(context).doing,
                                                                              style: AppStyles.body.copyWith(color: Color(0xFFCB7A00), fontWeight: FontWeight.w500, fontSize: 12, height: 1.5),
                                                                            ),
                                                                          ),
                                                                        )
                                                                      : Container(),
                                                                  SizedBox(
                                                                    width: 8,
                                                                  ),
                                                                  Container(
                                                                    decoration: BoxDecoration(
                                                                        color: Common.premium
                                                                            ? Color(
                                                                                0xFFEDFBFF)
                                                                            : Color(
                                                                                0xFFD8E3FF),
                                                                        borderRadius:
                                                                            BorderRadius.circular(4)),
                                                                    child:
                                                                        Padding(
                                                                      padding: const EdgeInsets
                                                                          .symmetric(
                                                                          vertical:
                                                                              4,
                                                                          horizontal:
                                                                              6),
                                                                      child: !Common
                                                                              .premium
                                                                          ? Text(
                                                                              AppLocalizations.of(context).free,
                                                                              style: AppStyles.body.copyWith(color: Color(0xff2D4686), fontWeight: FontWeight.w500, fontSize: 12, height: 1.5),
                                                                            )
                                                                          : Text(
                                                                              AppLocalizations.of(context).premium,
                                                                              style: AppStyles.body.copyWith(color: Color(0xff116C78), fontWeight: FontWeight.w500, fontSize: 12, height: 1.5),
                                                                            ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              )
                                                            ],
                                                          ),
                                                          Divider(
                                                              height: 16,
                                                              thickness: 1,
                                                              color: Color(
                                                                  0xFFD9E2E8)),
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceBetween,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .center,
                                                            children: [
                                                              Expanded(
                                                                child: Column(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .start,
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    Padding(
                                                                      padding: const EdgeInsets
                                                                          .only(
                                                                          bottom:
                                                                              10.0),
                                                                      child:
                                                                          Text(
                                                                        snapshot
                                                                            .data[index]
                                                                            .quiz_name,
                                                                        style: AppStyles.body.copyWith(
                                                                            color: Color(
                                                                                0xff1F1F1F),
                                                                            fontWeight: FontWeight
                                                                                .w700,
                                                                            fontSize:
                                                                                14,
                                                                            height:
                                                                                1.57),
                                                                      ),
                                                                    ),
                                                                    Container(
                                                                      width: MediaQuery.of(context)
                                                                              .size
                                                                              .width *
                                                                          0.65,
                                                                      child:
                                                                          Wrap(
                                                                        alignment:
                                                                            WrapAlignment.start,
                                                                        direction:
                                                                            Axis.horizontal,
                                                                        children: [
                                                                          if (resumeQuiz ==
                                                                              snapshot.data[index].quid) ...{
                                                                            Padding(
                                                                              padding: const EdgeInsets.only(
                                                                                right: 5,
                                                                              ),
                                                                              child: Icon(Icons.sticky_note_2_outlined, color: AppColors.lightGreyText, size: 15),
                                                                            ),
                                                                            Padding(
                                                                              padding: const EdgeInsets.only(right: 5),
                                                                              child: Text(snapshot.data[index].noq + " " + AppLocalizations.of(context).numberQuestion + " (" + (int.parse(snapshot.data[index].noq) - int.parse(numAnswered[snapshot.data[index].quid])).toString() + " " + AppLocalizations.of(context).left + ")", style: AppStyles.listQuizInfo),
                                                                            ),
                                                                            if (snapshot.data[index].duration ==
                                                                                '0') ...{
                                                                              Padding(
                                                                                padding: const EdgeInsets.only(right: 5),
                                                                                child: Icon(
                                                                                  Icons.access_time,
                                                                                  color: AppColors.lightGreyText,
                                                                                  size: 15,
                                                                                ),
                                                                              ),
                                                                              Text(_remainTime(snapshot.data[index].duration, timeDoQuiz[snapshot.data[index].quid]), style: AppStyles.listQuizInfo),
                                                                            } else ...{
                                                                              Padding(
                                                                                padding: const EdgeInsets.only(top: 5, right: 5),
                                                                                child: Icon(
                                                                                  Icons.circle_rounded,
                                                                                  size: 5,
                                                                                  color: Color(0xff7A8694),
                                                                                ),
                                                                              ),
                                                                              Wrap(children: [
                                                                                Padding(
                                                                                  padding: const EdgeInsets.only(right: 5),
                                                                                  child: Icon(
                                                                                    Icons.access_time,
                                                                                    color: AppColors.lightGreyText,
                                                                                    size: 15,
                                                                                  ),
                                                                                ),
                                                                                Text(_remainTime(snapshot.data[index].duration, timeDoQuiz[snapshot.data[index].quid]), style: AppStyles.listQuizInfo)
                                                                              ])
                                                                            },
                                                                          } else ...{
                                                                            Padding(
                                                                              padding: const EdgeInsets.only(
                                                                                right: 5,
                                                                              ),
                                                                              child: Icon(Icons.sticky_note_2_outlined, color: AppColors.lightGreyText, size: 15),
                                                                            ),
                                                                            Padding(
                                                                              padding: const EdgeInsets.only(right: 5),
                                                                              child: Text(snapshot.data[index].noq + " " + AppLocalizations.of(context).numberQuestion, style: AppStyles.listQuizInfo),
                                                                            ),
                                                                            if (snapshot.data[index].duration ==
                                                                                '0') ...{
                                                                              Padding(
                                                                                padding: const EdgeInsets.only(top: 5, right: 5),
                                                                                child: Icon(
                                                                                  Icons.circle_rounded,
                                                                                  size: 5,
                                                                                  color: Color(0xff7A8694),
                                                                                ),
                                                                              ),
                                                                              Wrap(children: [
                                                                                Padding(
                                                                                    padding: const EdgeInsets.only(right: 5),
                                                                                    child: Icon(
                                                                                      Icons.access_time,
                                                                                      color: AppColors.lightGreyText,
                                                                                      size: 15,
                                                                                    )),
                                                                                Text(AppLocalizations.of(context).noLimit, style: AppStyles.listQuizInfo)
                                                                              ])
                                                                            } else ...{
                                                                              Padding(
                                                                                padding: const EdgeInsets.only(top: 5, right: 5),
                                                                                child: Icon(
                                                                                  Icons.circle_rounded,
                                                                                  size: 5,
                                                                                  color: Color(0xff7A8694),
                                                                                ),
                                                                              ),
                                                                              Wrap(children: [
                                                                                Padding(
                                                                                    padding: const EdgeInsets.only(right: 5),
                                                                                    child: Icon(
                                                                                      Icons.access_time,
                                                                                      color: AppColors.lightGreyText,
                                                                                      size: 15,
                                                                                    )),
                                                                                Padding(
                                                                                  padding: const EdgeInsets.only(top: 0.5),
                                                                                  child: Text(snapshot.data[index].duration + " " + AppLocalizations.of(context).minutesShort, style: AppStyles.listQuizInfo),
                                                                                )
                                                                              ])
                                                                            },
                                                                          },
                                                                        ],
                                                                      ),
                                                                    )
                                                                  ],
                                                                ),
                                                              ),
                                                              ChartWidget(
                                                                percent: snapshot
                                                                        .data[
                                                                            index]
                                                                        .highscore /
                                                                    100,
                                                              ),
                                                            ],
                                                          )
                                                        ],
                                                      ),
                                                    )),
                                              );
                                            }
                                          },
                                        ),
                                        Common.premium == false
                                            ? CustomPaint(
                                                foregroundPainter:
                                                    FadingEffect(),
                                                child: ListView.builder(
                                                    itemCount: persons.length,
                                                    shrinkWrap: true,
                                                    physics:
                                                        const NeverScrollableScrollPhysics(),
                                                    itemBuilder:
                                                        (context, index) {
                                                      return FakeQuiz(
                                                        name:
                                                            persons[index].name,
                                                        noq: persons[index].noq,
                                                        duration: persons[index]
                                                            .duration,
                                                        type:
                                                            persons[index].type,
                                                      );
                                                    }),
                                              )
                                            : Container()
                                      ],
                                    ),
                                  ),
                                );
                              }
                            } else if (snapshot.hasError) {
                              if (internet == false) {
                                return Center(
                                    child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(
                                        'assets/images/no_internet.svg'),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 20),
                                      child: Column(
                                        children: [
                                          Text(
                                              AppLocalizations.of(context)
                                                  .noInternet,
                                              style: AppStyles.body.copyWith(
                                                  color: Colors.black,
                                                  fontSize: 16)),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          /* GestureDetector(
                                      child: Text(
                                          AppLocalizations.of(context).retryInternet,
                                          style: AppStyles.bodyBold.copyWith(
                                              fontSize: 16, color: Colors.blue)),
                                      onTap: () {
                                        Navigator.pushReplacement(
                                            context,
                                            MaterialPageRoute(
                                                builder: (BuildContext context) =>
                                                    super.widget));
                                      },
                                    ) */
                                        ],
                                      ),
                                    ),
                                  ],
                                ));
                              } else {
                                return ErrorDialog(
                                  error: snapshot.error.toString(),
                                  getList: true,
                                );
                              }
                            } else {
                              return Scaffold(
                                  body: Center(
                                      child: CircularProgressIndicator()));
                            }
                          },
                        ),
                      ),
                      ValueListenableBuilder(
                          valueListenable: isLoading,
                          builder: (context, _, __) {
                            if (isLoading.value && !firstLoadSuccess) {
                              return Container();
                            } else {
                              return Common.premium
                                  ? Container()
                                  : Positioned(
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                            left: 16.0, right: 16),
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(bottom: 20),
                                          child: ElevatedButton(
                                              style: ElevatedButton.styleFrom(
                                                minimumSize:
                                                    Size.fromHeight(40),
                                                backgroundColor:
                                                    AppColors.greenPrimary,
                                              ),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(16.0),
                                                child: Text(
                                                  AppLocalizations.of(context)
                                                      .unlockAllExam,
                                                  textAlign: TextAlign.center,
                                                  style: AppStyles.bodyBold
                                                      .copyWith(
                                                          color:
                                                              AppColors.white,
                                                          fontSize: 18),
                                                ),
                                              ),
                                              onPressed: () {
                                                logEvent(
                                                    "exam_list_premium_click",
                                                    {});
                                                showModal(context);
                                                /* Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => PaymentPage())); */
                                              }),
                                        ),
                                      ),
                                    );
                            }
                          })
                    ],
                  ),
                );
              })),
    );
  }
}

class QuizCard {
  String name;
  int noq;
  int duration;
  String type;
  QuizCard(
      {required this.noq,
      required this.name,
      required this.duration,
      required this.type});
}

class FadingEffect extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Rect rect = Rect.fromPoints(Offset(0, 0), Offset(size.width, size.height));
    LinearGradient lg = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: const [
          //create 2 white colors, one transparent
          Color.fromARGB(100, 255, 255, 255),
          Color.fromARGB(255, 255, 255, 255)
        ]);
    Paint paint = Paint()..shader = lg.createShader(rect);
    canvas.drawRect(rect, paint);
  }

  @override
  bool shouldRepaint(FadingEffect linePainter) => false;
}

void showModal(context) {
  /* showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return FractionallySizedBox(
            heightFactor: 1,
            child: Padding(
              padding: const EdgeInsets.only(top: 30),
              child: PaymentPage(),
            ));
      }); */
  Navigator.push(
      context, MaterialPageRoute(builder: (context) => PaymentPage()));
}

class FakeQuiz extends StatelessWidget {
  final String name;
  final int noq;
  final int duration;
  final String type;

  const FakeQuiz(
      {Key? key,
      required this.name,
      required this.noq,
      required this.duration,
      required this.type})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Container(
      //height: 100,
      margin: const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 16),
      decoration: BoxDecoration(boxShadow: const [
        BoxShadow(
          color: Color.fromRGBO(0, 0, 0, 0.16),
          spreadRadius: 0,
          blurRadius: 16,
          offset: Offset(4, 4), // changes position of shadow
        ),
      ], color: AppColors.white, borderRadius: BorderRadius.circular(8)),
      child: InkWell(
          onTap: () {},
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        type == 'Random' || type == 'Random set'
                            ? SvgPicture.asset('assets/images/shuffle.svg')
                            : SvgPicture.asset('assets/images/set.svg'),
                        SizedBox(
                          width: 8,
                        ),
                        Text(
                          type.toUpperCase(),
                          style: AppStyles.body.copyWith(
                              color: Color(0xff00326C),
                              fontWeight: FontWeight.w600,
                              fontSize: 12),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4.0),
                      child: Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                                color: Color(0xFFEDFBFF),
                                borderRadius: BorderRadius.circular(4)),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 4, horizontal: 6),
                              child: Text(
                                AppLocalizations.of(context).premium,
                                style: AppStyles.body.copyWith(
                                    color: Color(0xff116C78),
                                    fontWeight: FontWeight.w500,
                                    fontSize: 12,
                                    height: 1.5),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
                Divider(height: 16, thickness: 1, color: Color(0xFFD9E2E8)),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(bottom: 10.0),
                            child: Text(
                              name,
                              style: AppStyles.body.copyWith(
                                  color: Color(0xff1F1F1F),
                                  fontWeight: FontWeight.w700,
                                  fontSize: 14),
                            ),
                          ),
                          Row(
                            children: [
                              Icon(Icons.sticky_note_2_outlined,
                                  color: AppColors.lightGreyText, size: 15),
                              SizedBox(
                                width: 5,
                              ),
                              Text(
                                noq.toString() +
                                    " " +
                                    AppLocalizations.of(context).numberQuestion,
                                style: AppStyles.body.copyWith(
                                    color: Color(0xff7A8694),
                                    fontWeight: FontWeight.w500,
                                    fontSize: 13),
                              ),
                              SizedBox(
                                width: 5,
                              ),
                              Icon(
                                Icons.circle_rounded,
                                size: 5,
                                color: Color(0xff7A8694),
                              ),
                              SizedBox(
                                width: 5,
                              ),
                              Icon(Icons.access_time,
                                  color: AppColors.lightGreyText, size: 15),
                              SizedBox(
                                width: 5,
                              ),
                              Text(
                                duration.toString() +
                                    " " +
                                    AppLocalizations.of(context).minutesShort,
                                style: AppStyles.body.copyWith(
                                    color: Color(0xff7A8694),
                                    fontWeight: FontWeight.w500,
                                    fontSize: 13),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                    ChartWidget(
                      percent: 0 / 100,
                    ),
                  ],
                )
              ],
            ),
          )),
    );
  }
}
