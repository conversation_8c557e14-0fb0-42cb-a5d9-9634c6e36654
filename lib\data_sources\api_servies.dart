import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/models/question_api_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/report_model.dart';
import 'api_urls.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

import 'dio.dart';

final String appkey = getUserInfor().appkey;
String get url => getUserInfor().url;
String get urlPremium => getUserInfor().url_premium;

class ApiServices {
  int retryCount = 0;
  String question = '';
  final dio = NewDio().getDio();
  Future<List<Question>> fetchQuestion(id) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      try {
        await dio
            .post(ApiUrls().API_QUIZ_BY_ID + id,
                data: FormData.fromMap({
                  "key": appkey,
                  "token": token,
                  "info": "Do Quiz",
                  "debugId": Common.debugId
                }))
            .timeout(const Duration(seconds: 30))
            .then((response) {
          final String jsonBody = response.data;
          final int statusCode = response.statusCode ?? 0;

          if (statusCode != 200 || jsonBody == null) {
            throw Exception("Api Error");
          } else {
            preferences.setString('question_' + id, jsonBody);
          }
        });
      } catch (e) {
        print(e);
      }
    }
    question = preferences.getString('question_' + id) ?? '';
    final JsonDecoder _decoder = JsonDecoder();
    List useListContainer = [];
    if (question != "")
      useListContainer = _decoder.convert(question);
    else
      useListContainer = [];
    final List questionList = useListContainer;
    return questionList.map((e) => Question.fromJson(e)).toList();
  }

  Future<bool> sendResult(data) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool sendDatatSucceed = false;
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "- Send Result";
      try {
        final response = await dio.post(ApiUrls().API_SAVE_RESULT,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": data,
              "platform": Common.platform,
              "debugId": Common.debugId
            }));
        print('send result, retry: ${retryCount}');
        if (response.statusCode == 200 && response.data == 'success') {
          sendDatatSucceed = true;
          retryCount = 0;
        } else {
          return false;
        }
      } catch (e) {}
    }

    return sendDatatSucceed;
  }

  Future<dynamic> checkVersion() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool sendDatatSucceed = false;
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;
    if (connect) {
      var token = preferences.getString('token');
      token ??= await Request_key();
      String info = await GetDeviceInfo() + "- Check version";
      try {
        final response = await dio.post(ApiUrls().API_CHECK_VERSION,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "version": version,
              "appid": Common.appid,
              "debugId": Common.debugId
            }));
        print('check version, retry: ${retryCount}');
        if (response.statusCode == 200 && response.data != null) {
          sendDatatSucceed = true;
          retryCount = 0;
          return jsonDecode(response.data);
        } else {
          return false;
        }
      } catch (e) {
        print(e);
      }
    } else {
      return false;
    }
  }

  Future<dynamic> savePurchaseInfo(data) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool sendDatatSucceed = false;
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      token ??= await Request_key();
      String info = await GetDeviceInfo() + "- Save Purchase Info";
      try {
        final response = await dio.post(ApiUrls().API_SAVE_PURCHASE,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": data,
              "appid": Common.appid,
              "debugId": Common.debugId
            }));
        print('save purchase info, retry: ${retryCount}');
        if (response.statusCode == 200 && response.data != null) {
          sendDatatSucceed = true;
          retryCount = 0;
          if (response.data == "success") {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      } catch (e) {
        print(e);
      }
    } else {
      return false;
    }
  }

  Future<String> Request_key() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    var jwt = '';
    if (connect) {
      var token = preferences.getString('token');
      if (token == null) {
        jwt = await getToken();
        preferences.setString('token', jwt);
      }
    }
    return jwt;
  }

  Future<String> getToken() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    String info = await GetDeviceInfo() + "-Accquire token";
    var token = preferences.getString('token');
    token ??= "";
    try {
      final response = await dio.post(url,
          data: FormData.fromMap({
            "key": appkey,
            "token": token,
            "info": info,
            "debugId": Common.debugId
          }));

      if (response.statusCode == 200) {
        return response.data;
      }
    } catch (e) {
      print(e);
    }
    return "";
  }

  Future<void> changeEnvironment() async {
    if (environment == Environment.prod) {
      environment = Environment.prodBackup;
      SharedPreferences preferences = await SharedPreferences.getInstance();
      preferences.setString('env', 'prodBackup');
      logEvent("change_env_to_prodBackup}", {});
    }
    log("Environment: ${environment.toString()}");
  }

  // retryFuture(future, data, delay) {
  //   Future.delayed(Duration(milliseconds: delay), () {
  //     future(data);
  //   });
  // }

  Future<dynamic> sendErrorReport(data) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool sendDatatSucceed = false;
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      token ??= await Request_key();
      String info = await GetDeviceInfo();
      try {
        final response = await dio.post(ApiUrls().API_SEND_ERROR,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": data,
              "appid": Common.appid,
              "debugId": Common.debugId
            }));
        if (response.statusCode == 200 && response.data != null) {
          sendDatatSucceed = true;
          retryCount = 0;
          if (response.data == "success") {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      } catch (e) {
        print(e);
      }
    } else {
      return false;
    }
  }

  Future<bool> saveReportResponse(data) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool sendDatatSucceed = false;
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      token ??= await Request_key();
      String info = await GetDeviceInfo();
      try {
        final response = await dio.post(ApiUrls().API_SAVE_REPORT_RESPONSE,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data),
              "appid": Common.appid,
              "debugId": Common.debugId
            }));
        if (response.statusCode == 200 && response.data != null) {
          sendDatatSucceed = true;
          retryCount = 0;
          if (response.data == "Success") {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      } catch (e) {
        print(e);
        return false;
      }
    } else {
      return false;
    }
  }

  Future<List<Report>> listReported() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool sendDatatSucceed = false;
    var token = preferences.getString('token');
    token ??= await Request_key();
    String info = await GetDeviceInfo();
    final data = {"id": Common.debugId};
    try {
      final response = await dio.post(ApiUrls().API_LIST_REPORTED,
          data: FormData.fromMap({
            "key": appkey,
            "token": token,
            "info": info,
            "data": jsonEncode(data),
            "appid": Common.appid,
            "debugId": Common.debugId
          }));
      if (response.statusCode == 200 && response.data != null) {
        sendDatatSucceed = true;
        preferences.setString('reportedQuestion', response.data);
        return Report.fromList(List.from(jsonDecode(response.data)));
      }
      return [];
    } catch (e) {
      throw Exception(e);
    }
  }

  Future<Report> reportByQuestion(data) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var token = preferences.getString('token');
    token ??= await Request_key();
    String info = await GetDeviceInfo();
    try {
      final response = await dio.post(ApiUrls().API_REPORT_BY_QUESTION,
          data: FormData.fromMap({
            "key": appkey,
            "token": token,
            "info": info,
            "data": jsonEncode(data),
            "appid": Common.appid,
            "debugId": Common.debugId
          }));
      if (response.statusCode == 200 && response.data != null) {
        final data = jsonDecode(response.data);
        if (data is String) {
          return Report();
        }
        final report = Report.fromJson(data);
        return report;
      }
      return Report();
    } catch (e) {
      rethrow;
    }
  }

  Future<List<ReportResponse>> reportResponse(data) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var token = preferences.getString('token');
    token ??= await Request_key();
    String info = await GetDeviceInfo();
    try {
      final response = await dio.post(ApiUrls().API_RESPONSE_REPORTED,
          data: FormData.fromMap({
            "key": appkey,
            "token": token,
            "info": info,
            "data": jsonEncode(data),
            "appid": Common.appid,
            "debugId": Common.debugId
          }));
      if (response.statusCode == 200 && response.data != null) {
        return ReportResponse.fromList(List.from(jsonDecode(response.data)));
      }
      return [];
    } catch (e) {
      throw Exception(e);
    }
  }

  Future<bool> closeReport(data) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var token = preferences.getString('token');
    token ??= await Request_key();
    String info = await GetDeviceInfo();
    try {
      final response = await dio.post(ApiUrls().API_CLOSE_REPORT,
          data: FormData.fromMap({
            "key": appkey,
            "token": token,
            "info": info,
            "data": jsonEncode(data),
            "appid": Common.appid,
            "debugId": Common.debugId
          }));
      if (response.statusCode == 200 && response.data != null) {
        if (response.data == 'success') {
          return true;
        }
      }
      return false;
    } catch (e) {
      throw Exception(e);
    }
  }

  Future<bool> saveEventLog(data) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var token = preferences.getString('token');
    token ??= await Request_key();
    String info = await GetDeviceInfo();
    try {
      final response = await dio.post(ApiUrls().API_SAVE_LOG,
          data: FormData.fromMap({
            "key": appkey,
            "token": token,
            "info": info,
            "data": jsonEncode(data),
            "appid": Common.appid,
            "debugId": Common.debugId
          }));
      if (response.statusCode == 200 && response.data != null) {
        if (response.data == 'success') {
          return true;
        }
      }
      return false;
    } catch (e) {
      throw Exception(e);
    }
  }

  Future<void> getListQuiz() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) {
      try {
        var newQuizListTime = DateFormat('dd/MM/yyyy')
            .format(DateTime.now().add(const Duration(days: 7)));
        var token = preferences.getString('token');
        String info = await GetDeviceInfo() + "-Quiz List Request";
        Map data = {'appid': Common.appid};
        String getListUrl = '';
        if (Common.premium) {
          getListUrl = urlPremium;
        } else {
          getListUrl = url;
        }
        final response = await dio.post(
          getListUrl,
          data: FormData.fromMap({
            'key': appkey,
            'token': token,
            'info': info,
            'data': jsonEncode(data),
            "debugId": Common.debugId
          }),
        );
        if (response.statusCode == 200) {
          preferences.setString('quizList', response.data);
          preferences.setString('quizListTimer', newQuizListTime);
          //loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        print(e);
      }
    }
  }

  Future<void> requestKey() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var jwt = await getToken();
      preferences.setString('token', jwt);
    }
  }
}
