import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/view/home.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../localization.dart';

class WebViewContainer extends StatefulWidget {
  final url;

  WebViewContainer(this.url);

  @override
  createState() => _WebViewContainerState(this.url);
}

class _WebViewContainerState extends State<WebViewContainer> {
  var _url;
  final _key = UniqueKey();
  late Future<String> futurePost;
  _goBack(BuildContext context) {
    logPage("Main");
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const Home()),
    );
  }

  _WebViewContainerState(this._url);

  Future<String> _checkInternet() async {
    bool connect = await InternetConnectionChecker.instance.hasConnection;
    if (connect) {
      return 'connect';
    } else {
      return 'disconnect';
    }
  }

  late WebViewController webViewController;

  @override
  void initState() {
    futurePost = _checkInternet();
    super.initState();
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {},
          onWebResourceError: (WebResourceError error) {},
        ),
      )
      ..loadRequest(Uri.parse(_url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              iconSize: 20.0,
              onPressed: () {
                _goBack(context);
              },
            ),
            title: RichText(
              text: TextSpan(
                style:
                    const TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
                children: [
                  const TextSpan(
                    text: 'The Scrum Guide ',
                  ),
                  WidgetSpan(
                    child: Transform.translate(
                      offset: const Offset(0.0, -7.0),
                      child: const Text(
                        'TM',
                        style: TextStyle(fontSize: 11),
                      ),
                    ),
                  ),
                ],
              ),
            )),
        body: FutureBuilder(
            future: futurePost,
            builder: (BuildContext context, AsyncSnapshot snapshot) {
              if (snapshot.data == 'connect') {
                return Column(
                  children: [
                    Expanded(
                        child: WebViewWidget(
                      key: _key,
                      controller: webViewController,
                    ))
                  ],
                );
              } else {
                return Center(
                    child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset('assets/images/no_internet.svg'),
                    Padding(
                      padding: const EdgeInsets.only(top: 20),
                      child: Column(
                        children: [
                          Text(AppLocalizations.of(context).noInternet,
                              style: AppStyles.body
                                  .copyWith(color: Colors.black, fontSize: 16)),
                          const SizedBox(
                            height: 10,
                          ),
                          GestureDetector(
                            child: Text(
                                AppLocalizations.of(context).retryInternet,
                                style: AppStyles.bodyBold.copyWith(
                                    fontSize: 16,
                                    color: AppColors.greenPrimary)),
                            onTap: () {
                              Navigator.pushReplacement(
                                  context,
                                  MaterialPageRoute(
                                      builder: (BuildContext context) =>
                                          super.widget));
                            },
                          )
                        ],
                      ),
                    ),
                  ],
                ));
              }
            }));
  }
}
